import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { SnackbarProvider } from 'notistack';

// Context Providers
import { ThemeProvider as AppThemeProvider } from './contexts/ThemeContext';
import { ConnectionProvider } from './contexts/ConnectionContext';
import { AuthProvider } from './contexts/AuthContext';

// Layout Components
import ModernLayout from './components/layout/ModernLayout';
import ErrorBoundary from './components/common/ErrorBoundary';

// Page Components
import ModernDashboard from './components/dashboard/ModernDashboard';
import AdvancedQueryEditor from './components/query/AdvancedQueryEditor';
import SchemaExplorer from './components/schema/SchemaExplorer';
import DataVisualization from './components/visualization/DataVisualization';
import PerformanceMonitor from './components/monitoring/PerformanceMonitor';
import UserManagement from './components/auth/UserManagement';
import ConnectionManager from './components/connection/ConnectionManager';
import Settings from './components/settings/Settings';
import LoginPage from './components/auth/LoginPage';
import ProfilePage from './components/auth/ProfilePage';

// Legacy Components (for backward compatibility)
import Dashboard from './components/dashboard/Dashboard';
import QueryEditor from './components/query/QueryEditor';
import DatabaseExplorer from './components/database/DatabaseExplorer';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  // In a real app, check authentication status
  const isAuthenticated = true; // Replace with actual auth check
  
  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

// Main App Component
const ModernApp = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AppThemeProvider>
          <ThemeProvider theme={(theme) => theme}>
            <CssBaseline />
            <SnackbarProvider 
              maxSnack={3}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
            >
              <AuthProvider>
                <ConnectionProvider>
                  <Router>
                    <Routes>
                      {/* Public Routes */}
                      <Route path="/login" element={<LoginPage />} />
                      
                      {/* Protected Routes */}
                      <Route
                        path="/"
                        element={
                          <ProtectedRoute>
                            <ModernLayout />
                          </ProtectedRoute>
                        }
                      >
                        {/* Modern Dashboard */}
                        <Route index element={<Navigate to="/dashboard" replace />} />
                        <Route path="dashboard" element={<ModernDashboard />} />
                        
                        {/* Advanced Query Editor */}
                        <Route path="query" element={<AdvancedQueryEditor />} />
                        
                        {/* Schema Explorer */}
                        <Route path="schema" element={<SchemaExplorer />} />
                        
                        {/* Data Visualization */}
                        <Route path="visualize" element={<DataVisualization />} />
                        
                        {/* Performance Monitoring */}
                        <Route path="monitoring" element={<PerformanceMonitor />} />
                        
                        {/* User Management */}
                        <Route path="users" element={<UserManagement />} />
                        
                        {/* Connection Management */}
                        <Route path="connections" element={<ConnectionManager />} />
                        
                        {/* Settings */}
                        <Route path="settings" element={<Settings />} />
                        
                        {/* Profile */}
                        <Route path="profile" element={<ProfilePage />} />
                        
                        {/* Legacy Routes (for backward compatibility) */}
                        <Route path="legacy">
                          <Route path="dashboard" element={<Dashboard />} />
                          <Route path="query-editor" element={<QueryEditor />} />
                          <Route path="database" element={<DatabaseExplorer />} />
                        </Route>
                        
                        {/* Catch-all route */}
                        <Route path="*" element={<Navigate to="/dashboard" replace />} />
                      </Route>
                    </Routes>
                  </Router>
                </ConnectionProvider>
              </AuthProvider>
            </SnackbarProvider>
          </ThemeProvider>
        </AppThemeProvider>
        
        {/* React Query DevTools (only in development) */}
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default ModernApp;
