import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
  Chip,
  Collapse,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  TableChart as TableIcon,
  ViewColumn as ColumnIcon,
  Key as KeyIcon,
  Link as LinkIcon,
  Index as IndexIcon,
  Security as SecurityIcon,
  Functions as FunctionIcon,
  Storage as ViewIcon,
  MoreVert as MoreVertIcon,
  ContentCopy as CopyIcon,
  PlayArrow as QueryIcon,
  Schema as SchemaIcon,
} from '@mui/icons-material';
// import { TreeView, TreeItem } from '@mui/x-tree-view';
import { useConnection } from '../../contexts/ConnectionContext';

const SchemaExplorer = ({ onGenerateQuery }) => {
  const { activeConnection, executeQuery } = useConnection();
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedNodes, setExpandedNodes] = useState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [schemaData, setSchemaData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);
  const [tableDetailsDialog, setTableDetailsDialog] = useState(null);
  const [activeTab, setActiveTab] = useState(0);

  // Load schema data
  useEffect(() => {
    if (activeConnection) {
      loadSchemaData();
    }
  }, [activeConnection]);

  const loadSchemaData = async () => {
    if (!activeConnection) return;
    
    setLoading(true);
    setError(null);
    
    try {
      let query = '';
      
      // Different queries for different database types
      switch (activeConnection.type) {
        case 'postgresql':
          query = `
            SELECT 
              schemaname as schema_name,
              tablename as table_name,
              'table' as object_type
            FROM pg_tables 
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            UNION ALL
            SELECT 
              schemaname as schema_name,
              viewname as table_name,
              'view' as object_type
            FROM pg_views 
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            ORDER BY schema_name, table_name;
          `;
          break;
          
        case 'mysql':
          query = `
            SELECT 
              TABLE_SCHEMA as schema_name,
              TABLE_NAME as table_name,
              TABLE_TYPE as object_type
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
            ORDER BY TABLE_SCHEMA, TABLE_NAME;
          `;
          break;
          
        case 'sqlite':
          query = `
            SELECT 
              'main' as schema_name,
              name as table_name,
              type as object_type
            FROM sqlite_master 
            WHERE type IN ('table', 'view')
            AND name NOT LIKE 'sqlite_%'
            ORDER BY name;
          `;
          break;
          
        default:
          throw new Error(`Unsupported database type: ${activeConnection.type}`);
      }
      
      const result = await executeQuery(query);
      setSchemaData(result);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Get table details
  const getTableDetails = async (schemaName, tableName) => {
    if (!activeConnection) return null;
    
    try {
      let query = '';
      
      switch (activeConnection.type) {
        case 'postgresql':
          query = `
            SELECT 
              column_name,
              data_type,
              is_nullable,
              column_default,
              character_maximum_length,
              numeric_precision,
              numeric_scale
            FROM information_schema.columns 
            WHERE table_schema = '${schemaName}' 
            AND table_name = '${tableName}'
            ORDER BY ordinal_position;
          `;
          break;
          
        case 'mysql':
          query = `
            SELECT 
              COLUMN_NAME as column_name,
              DATA_TYPE as data_type,
              IS_NULLABLE as is_nullable,
              COLUMN_DEFAULT as column_default,
              CHARACTER_MAXIMUM_LENGTH as character_maximum_length,
              NUMERIC_PRECISION as numeric_precision,
              NUMERIC_SCALE as numeric_scale
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = '${schemaName}' 
            AND TABLE_NAME = '${tableName}'
            ORDER BY ORDINAL_POSITION;
          `;
          break;
          
        case 'sqlite':
          query = `PRAGMA table_info(${tableName});`;
          break;
      }
      
      const result = await executeQuery(query);
      return result;
      
    } catch (err) {
      console.error('Error fetching table details:', err);
      return null;
    }
  };

  // Organize schema data into tree structure
  const treeData = useMemo(() => {
    if (!schemaData || !schemaData.rows) return [];
    
    const schemas = {};
    
    schemaData.rows.forEach(row => {
      const schemaName = row.schema_name || 'default';
      if (!schemas[schemaName]) {
        schemas[schemaName] = {
          tables: [],
          views: [],
          functions: []
        };
      }
      
      if (row.object_type === 'table' || row.object_type === 'BASE TABLE') {
        schemas[schemaName].tables.push(row.table_name);
      } else if (row.object_type === 'view' || row.object_type === 'VIEW') {
        schemas[schemaName].views.push(row.table_name);
      }
    });
    
    return Object.entries(schemas).map(([schemaName, objects]) => ({
      id: schemaName,
      name: schemaName,
      type: 'schema',
      children: [
        ...(objects.tables.length > 0 ? [{
          id: `${schemaName}_tables`,
          name: `Tables (${objects.tables.length})`,
          type: 'folder',
          children: objects.tables.map(tableName => ({
            id: `${schemaName}.${tableName}`,
            name: tableName,
            type: 'table',
            schema: schemaName
          }))
        }] : []),
        ...(objects.views.length > 0 ? [{
          id: `${schemaName}_views`,
          name: `Views (${objects.views.length})`,
          type: 'folder',
          children: objects.views.map(viewName => ({
            id: `${schemaName}.${viewName}`,
            name: viewName,
            type: 'view',
            schema: schemaName
          }))
        }] : [])
      ]
    }));
  }, [schemaData]);

  // Filter tree data based on search
  const filteredTreeData = useMemo(() => {
    if (!searchTerm) return treeData;
    
    const filterNode = (node) => {
      const matchesSearch = node.name.toLowerCase().includes(searchTerm.toLowerCase());
      const filteredChildren = node.children ? node.children.map(filterNode).filter(Boolean) : [];
      
      if (matchesSearch || filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren
        };
      }
      
      return null;
    };
    
    return treeData.map(filterNode).filter(Boolean);
  }, [treeData, searchTerm]);

  const handleNodeToggle = (nodeId) => {
    setExpandedNodes(prev => 
      prev.includes(nodeId) 
        ? prev.filter(id => id !== nodeId)
        : [...prev, nodeId]
    );
  };

  const handleNodeSelect = (nodeId) => {
    setSelectedNode(nodeId);
  };

  const handleContextMenu = (event, node) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      node
    });
  };

  const handleCloseContextMenu = () => {
    setContextMenu(null);
  };

  const handleShowTableDetails = async (node) => {
    const details = await getTableDetails(node.schema, node.name);
    setTableDetailsDialog({ node, details });
    handleCloseContextMenu();
  };

  const handleGenerateSelect = (node) => {
    if (onGenerateQuery) {
      const query = `SELECT * FROM ${node.schema ? `${node.schema}.` : ''}${node.name} LIMIT 100;`;
      onGenerateQuery(query);
    }
    handleCloseContextMenu();
  };

  const getNodeIcon = (type) => {
    switch (type) {
      case 'schema': return <SecurityIcon />;
      case 'table': return <TableIcon />;
      case 'view': return <ViewIcon />;
      case 'folder': return <ColumnIcon />;
      default: return <TableIcon />;
    }
  };

  // Tree node rendering is now handled inline with List components

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Schema Explorer</Typography>
          <Tooltip title="Refresh Schema">
            <IconButton onClick={loadSchemaData} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
        
        <TextField
          fullWidth
          size="small"
          placeholder="Search tables, views..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        )}
        
        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}
        
        {!loading && !error && filteredTreeData.length === 0 && (
          <Alert severity="info" sx={{ m: 2 }}>
            {searchTerm ? 'No objects found matching your search.' : 'No database objects found.'}
          </Alert>
        )}
        
        {!loading && !error && filteredTreeData.length > 0 && (
          <List>
            {filteredTreeData.map((schema) => (
              <React.Fragment key={schema.id}>
                <ListItem button onClick={() => handleNodeToggle(schema.id)}>
                  <ListItemIcon>
                    <SchemaIcon />
                  </ListItemIcon>
                  <ListItemText primary={schema.name} />
                  {expandedNodes.includes(schema.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </ListItem>
                {expandedNodes.includes(schema.id) && schema.children && (
                  <List component="div" disablePadding>
                    {schema.children.map((folder) => (
                      <React.Fragment key={folder.id}>
                        <ListItem button sx={{ pl: 4 }} onClick={() => handleNodeToggle(folder.id)}>
                          <ListItemIcon>
                            <ColumnIcon />
                          </ListItemIcon>
                          <ListItemText primary={folder.name} />
                          {expandedNodes.includes(folder.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </ListItem>
                        {expandedNodes.includes(folder.id) && folder.children && (
                          <List component="div" disablePadding>
                            {folder.children.map((table) => (
                              <ListItem
                                key={table.id}
                                button
                                sx={{ pl: 8 }}
                                onClick={() => handleNodeSelect(table.id)}
                                onContextMenu={(e) => handleContextMenu(e, table)}
                              >
                                <ListItemIcon>
                                  {table.type === 'table' ? <TableIcon /> : <ViewIcon />}
                                </ListItemIcon>
                                <ListItemText primary={table.name} />
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleContextMenu(e, table);
                                  }}
                                >
                                  <MoreVertIcon />
                                </IconButton>
                              </ListItem>
                            ))}
                          </List>
                        )}
                      </React.Fragment>
                    ))}
                  </List>
                )}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={handleCloseContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem onClick={() => handleGenerateSelect(contextMenu?.node)}>
          <ListItemIcon>
            <QueryIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Generate SELECT</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleShowTableDetails(contextMenu?.node)}>
          <ListItemIcon>
            <ColumnIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={handleCloseContextMenu}>
          <ListItemIcon>
            <CopyIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Copy Name</ListItemText>
        </MenuItem>
      </Menu>

      {/* Table Details Dialog */}
      <Dialog
        open={Boolean(tableDetailsDialog)}
        onClose={() => setTableDetailsDialog(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {tableDetailsDialog?.node?.name} Details
        </DialogTitle>
        <DialogContent>
          {tableDetailsDialog?.details && (
            <Box>
              <Typography variant="h6" gutterBottom>Columns</Typography>
              {/* Add table details content here */}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTableDetailsDialog(null)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default SchemaExplorer;
