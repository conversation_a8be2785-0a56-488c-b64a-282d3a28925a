import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  TableChart as TableIcon,
  Storage as ViewIcon,
} from '@mui/icons-material';
import { useConnection } from '../../contexts/ConnectionContext';

const SimpleSchemaExplorer = () => {
  const { activeConnection, executeQuery } = useConnection();
  const [searchTerm, setSearchTerm] = useState('');
  const [schemaData, setSchemaData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (activeConnection) {
      loadSchemaData();
    }
  }, [activeConnection]);

  const loadSchemaData = async () => {
    if (!activeConnection) return;
    
    setLoading(true);
    setError(null);
    
    try {
      let query = '';
      
      switch (activeConnection.type) {
        case 'postgresql':
          query = `
            SELECT 
              schemaname as schema_name,
              tablename as table_name,
              'table' as object_type
            FROM pg_tables 
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            ORDER BY schemaname, tablename;
          `;
          break;
          
        case 'mysql':
          query = `
            SELECT 
              TABLE_SCHEMA as schema_name,
              TABLE_NAME as table_name,
              TABLE_TYPE as object_type
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
            ORDER BY TABLE_SCHEMA, TABLE_NAME;
          `;
          break;
          
        case 'sqlite':
          query = `
            SELECT 
              'main' as schema_name,
              name as table_name,
              type as object_type
            FROM sqlite_master 
            WHERE type IN ('table', 'view')
            AND name NOT LIKE 'sqlite_%'
            ORDER BY name;
          `;
          break;
          
        default:
          throw new Error(`Unsupported database type: ${activeConnection.type}`);
      }
      
      const result = await executeQuery(query);
      setSchemaData(result);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const filteredData = schemaData?.rows?.filter(row => 
    !searchTerm || 
    row.table_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    row.schema_name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Schema Explorer</Typography>
          <IconButton onClick={loadSchemaData} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        </Box>
        
        <TextField
          fullWidth
          size="small"
          placeholder="Search tables, views..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        )}
        
        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}
        
        {!loading && !error && filteredData.length === 0 && (
          <Alert severity="info" sx={{ m: 2 }}>
            {searchTerm ? 'No objects found matching your search.' : 'No database objects found.'}
          </Alert>
        )}
        
        {!loading && !error && filteredData.length > 0 && (
          <List>
            {filteredData.map((item, index) => (
              <ListItem key={index} button>
                <ListItemIcon>
                  {item.object_type === 'table' || item.object_type === 'BASE TABLE' ? 
                    <TableIcon /> : <ViewIcon />
                  }
                </ListItemIcon>
                <ListItemText
                  primary={item.table_name}
                  secondary={`${item.schema_name} • ${item.object_type}`}
                />
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Paper>
  );
};

export default SimpleSchemaExplorer;
