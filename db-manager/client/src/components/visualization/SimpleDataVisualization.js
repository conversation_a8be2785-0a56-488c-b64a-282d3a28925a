import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON> as Bar<PERSON>hartI<PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartI<PERSON>,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useConnection } from '../../contexts/ConnectionContext';

const SimpleDataVisualization = () => {
  const { activeConnection, executeQuery } = useConnection();
  const [chartType, setChartType] = useState('bar');
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const sampleQueries = [
    {
      name: 'Table Row Counts',
      query: `
        SELECT 
          table_name,
          table_rows as count
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
        AND table_rows > 0
        ORDER BY table_rows DESC
        LIMIT 10
      `,
    },
    {
      name: 'Database Size by Table',
      query: `
        SELECT 
          table_name,
          ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
        ORDER BY (data_length + index_length) DESC
        LIMIT 10
      `,
    },
  ];

  const executeVisualizationQuery = async (query) => {
    if (!activeConnection) {
      setError('Please connect to a database first');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await executeQuery(query);
      setData(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const exportData = () => {
    if (!data || !data.rows) return;

    const csv = [
      data.columns.join(','),
      ...data.rows.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'visualization_data.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (!activeConnection) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          Please connect to a database to start creating visualizations.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Data Visualization
      </Typography>

      <Grid container spacing={3}>
        {/* Controls */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Visualization Controls
            </Typography>
            
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Chart Type</InputLabel>
                  <Select
                    value={chartType}
                    onChange={(e) => setChartType(e.target.value)}
                    label="Chart Type"
                  >
                    <MenuItem value="bar">
                      <BarChartIcon sx={{ mr: 1 }} />
                      Bar Chart
                    </MenuItem>
                    <MenuItem value="line">
                      <LineChartIcon sx={{ mr: 1 }} />
                      Line Chart
                    </MenuItem>
                    <MenuItem value="pie">
                      <PieChartIcon sx={{ mr: 1 }} />
                      Pie Chart
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => setData(null)}
                  fullWidth
                >
                  Clear Data
                </Button>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={exportData}
                  disabled={!data}
                  fullWidth
                >
                  Export CSV
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Sample Queries */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Sample Queries
            </Typography>
            
            {sampleQueries.map((sample, index) => (
              <Card key={index} sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    {sample.name}
                  </Typography>
                  <Button
                    variant="contained"
                    size="small"
                    onClick={() => executeVisualizationQuery(sample.query)}
                    disabled={loading}
                  >
                    Execute & Visualize
                  </Button>
                </CardContent>
              </Card>
            ))}
          </Paper>
        </Grid>

        {/* Data Display */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Data Preview
            </Typography>
            
            {loading && (
              <Alert severity="info">
                Executing query...
              </Alert>
            )}
            
            {error && (
              <Alert severity="error">
                {error}
              </Alert>
            )}
            
            {data && data.rows && (
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {data.columns.map((column, index) => (
                        <TableCell key={index}>{column}</TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {data.rows.slice(0, 10).map((row, index) => (
                      <TableRow key={index}>
                        {data.columns.map((column, colIndex) => (
                          <TableCell key={colIndex}>
                            {row[column]}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            
            {!loading && !error && !data && (
              <Alert severity="info">
                Execute a query to see data visualization.
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Chart Placeholder */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: 3, minHeight: 400 }}>
            <Typography variant="h6" gutterBottom>
              {chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart
            </Typography>
            
            {data && data.rows ? (
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                height: 300,
                border: '2px dashed',
                borderColor: 'divider',
                borderRadius: 1
              }}>
                <Box sx={{ textAlign: 'center' }}>
                  {chartType === 'bar' && <BarChartIcon sx={{ fontSize: 64, color: 'primary.main' }} />}
                  {chartType === 'line' && <LineChartIcon sx={{ fontSize: 64, color: 'primary.main' }} />}
                  {chartType === 'pie' && <PieChartIcon sx={{ fontSize: 64, color: 'primary.main' }} />}
                  <Typography variant="h6" color="primary" sx={{ mt: 2 }}>
                    {chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart Visualization
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Advanced charting with Recharts coming soon!
                  </Typography>
                  <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                    Data: {data.rows.length} rows, {data.columns.length} columns
                  </Typography>
                </Box>
              </Box>
            ) : (
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                height: 300,
                bgcolor: 'grey.50'
              }}>
                <Typography variant="body1" color="text.secondary">
                  Execute a query to generate visualization
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SimpleDataVisualization;
