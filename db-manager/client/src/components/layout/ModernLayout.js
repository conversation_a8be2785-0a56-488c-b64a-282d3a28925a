import React, { useState, useMemo } from "react";
import { Outlet, Link, useLocation, useNavigate } from "react-router-dom";
import { styled, useTheme } from "@mui/material/styles";
import {
  Box,
  CssBaseline,
  IconButton,
  Typography,
  Drawer,
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  useMediaQuery,
  AppBar as MuiAppBar,
  Toolbar as MuiToolbar,
  Badge,
  Avatar,
  Menu,
  MenuItem,
  Chip,
  Tooltip,
  Switch,
  FormControlLabel,
  Alert,
  Snackbar,
} from "@mui/material";
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Dashboard as DashboardIcon,
  Storage as DatabaseIcon,
  Code as QueryIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Timeline as MonitoringIcon,
  People as UsersIcon,
  Schema as SchemaIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  ExitToApp as LogoutIcon,
  Person as ProfileIcon,
  <PERSON><PERSON>hart as ChartIcon,
  Security as SecurityIcon,
} from "@mui/icons-material";
import { useTheme as useAppTheme } from "../../contexts/ThemeContext";
import { useConnection } from "../../contexts/ConnectionContext";
import ConnectionDialog from "../connection/ConnectionDialog";
import ConnectionStatus from "../connection/ConnectionStatus";

const drawerWidth = 280;

const Main = styled("main", { shouldForwardProp: (prop) => prop !== "open" })(
  ({ theme, open }) => ({
    flexGrow: 1,
    padding: theme.spacing(3),
    transition: theme.transitions.create("margin", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginLeft: 0,
    ...(open && {
      transition: theme.transitions.create("margin", {
        easing: theme.transitions.easing.easeOut,
        duration: theme.transitions.duration.enteringScreen,
      }),
      marginLeft: drawerWidth,
    }),
  })
);

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme, open }) => ({
  transition: theme.transitions.create(["margin", "width"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    width: `calc(100% - ${drawerWidth}px)`,
    marginLeft: drawerWidth,
    transition: theme.transitions.create(["margin", "width"], {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(0, 1),
  ...theme.mixins.toolbar,
  justifyContent: "flex-end",
}));

const ModernLayout = () => {
  const theme = useTheme();
  const { darkMode, toggleDarkMode } = useAppTheme();
  const { activeConnection, connections } = useConnection();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  
  const [open, setOpen] = useState(!isMobile);
  const [connectionDialogOpen, setConnectionDialogOpen] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);
  const [notifications, setNotifications] = useState([
    { id: 1, message: "Database connection established", type: "success", timestamp: new Date() },
    { id: 2, message: "Query executed successfully", type: "info", timestamp: new Date() },
  ]);
  const [showNotification, setShowNotification] = useState(false);

  // Enhanced navigation items with modern features
  const navigationItems = [
    {
      text: "Dashboard",
      icon: <DashboardIcon />,
      path: "/dashboard",
      description: "Overview and statistics",
    },
    {
      text: "Query Editor",
      icon: <QueryIcon />,
      path: "/query",
      description: "Advanced SQL editor",
    },
    {
      text: "Schema Explorer",
      icon: <SchemaIcon />,
      path: "/schema",
      description: "Browse database structure",
    },
    {
      text: "Data Visualization",
      icon: <ChartIcon />,
      path: "/visualize",
      description: "Charts and graphs",
    },
    {
      text: "Performance Monitor",
      icon: <MonitoringIcon />,
      path: "/monitoring",
      description: "Real-time monitoring",
    },
    {
      text: "User Management",
      icon: <UsersIcon />,
      path: "/users",
      description: "Manage users and roles",
    },
    {
      text: "Database Connections",
      icon: <DatabaseIcon />,
      path: "/connections",
      description: "Manage connections",
    },
    {
      text: "Settings",
      icon: <SettingsIcon />,
      path: "/settings",
      description: "Application settings",
    },
  ];

  const handleDrawerToggle = () => {
    setOpen(!open);
  };

  const handleUserMenuOpen = (event) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleLogout = () => {
    // Implement logout logic
    handleUserMenuClose();
    navigate('/login');
  };

  const isActiveRoute = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const unreadNotifications = notifications.filter(n => !n.read).length;

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      
      {/* App Bar */}
      <AppBar position="fixed" open={open} elevation={1}>
        <MuiToolbar>
          <IconButton
            color="inherit"
            aria-label="toggle drawer"
            onClick={handleDrawerToggle}
            edge="start"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Database Manager Pro
          </Typography>

          {/* Connection Status */}
          <Box sx={{ mr: 2 }}>
            <ConnectionStatus />
          </Box>

          {/* Theme Toggle */}
          <Tooltip title={`Switch to ${darkMode ? 'light' : 'dark'} mode`}>
            <IconButton color="inherit" onClick={toggleDarkMode}>
              {darkMode ? <LightModeIcon /> : <DarkModeIcon />}
            </IconButton>
          </Tooltip>

          {/* Notifications */}
          <Tooltip title="Notifications">
            <IconButton 
              color="inherit" 
              onClick={() => setShowNotification(true)}
            >
              <Badge badgeContent={unreadNotifications} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* User Menu */}
          <Tooltip title="User menu">
            <IconButton
              color="inherit"
              onClick={handleUserMenuOpen}
              sx={{ ml: 1 }}
            >
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                <AccountIcon />
              </Avatar>
            </IconButton>
          </Tooltip>
        </MuiToolbar>
      </AppBar>

      {/* Navigation Drawer */}
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            boxSizing: "border-box",
          },
        }}
        variant={isMobile ? "temporary" : "persistent"}
        anchor="left"
        open={open}
        onClose={handleDrawerToggle}
      >
        <DrawerHeader>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <DatabaseIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6" color="primary">
              DB Manager
            </Typography>
            <IconButton onClick={handleDrawerToggle} sx={{ ml: 'auto' }}>
              {theme.direction === "ltr" ? <ChevronLeftIcon /> : <ChevronRightIcon />}
            </IconButton>
          </Box>
        </DrawerHeader>
        
        <Divider />

        {/* Active Connection Info */}
        {activeConnection && (
          <Box sx={{ p: 2 }}>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Active Connection
            </Typography>
            <Chip
              icon={<DatabaseIcon />}
              label={`${activeConnection.type}: ${activeConnection.database || activeConnection.host}`}
              color="primary"
              size="small"
              sx={{ width: '100%' }}
            />
          </Box>
        )}

        <Divider />

        {/* Navigation Items */}
        <List sx={{ flexGrow: 1 }}>
          {navigationItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                component={Link}
                to={item.path}
                selected={isActiveRoute(item.path)}
                sx={{
                  minHeight: 48,
                  px: 2.5,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 0,
                    mr: 3,
                    justifyContent: "center",
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text}
                  secondary={item.description}
                  primaryTypographyProps={{ variant: 'body2', fontWeight: 'medium' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        <Divider />

        {/* Quick Actions */}
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Quick Actions
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Chip
              icon={<AddIcon />}
              label="New Connection"
              clickable
              onClick={() => setConnectionDialogOpen(true)}
              size="small"
              variant="outlined"
            />
            <Chip
              icon={<QueryIcon />}
              label="New Query"
              clickable
              onClick={() => navigate('/query')}
              size="small"
              variant="outlined"
            />
          </Box>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Main open={open}>
        <DrawerHeader />
        <Outlet />
      </Main>

      {/* User Menu */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
        onClick={handleUserMenuClose}
        PaperProps={{
          elevation: 3,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            minWidth: 200,
          },
        }}
      >
        <MenuItem onClick={() => navigate('/profile')}>
          <ListItemIcon>
            <ProfileIcon fontSize="small" />
          </ListItemIcon>
          Profile
        </MenuItem>
        <MenuItem onClick={() => navigate('/settings')}>
          <ListItemIcon>
            <SettingsIcon fontSize="small" />
          </ListItemIcon>
          Settings
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          Logout
        </MenuItem>
      </Menu>

      {/* Connection Dialog */}
      <ConnectionDialog
        open={connectionDialogOpen}
        onClose={() => setConnectionDialogOpen(false)}
      />

      {/* Notifications Snackbar */}
      <Snackbar
        open={showNotification}
        autoHideDuration={6000}
        onClose={() => setShowNotification(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={() => setShowNotification(false)} 
          severity="info" 
          sx={{ width: '100%' }}
        >
          {notifications.length > 0 ? notifications[0].message : 'No new notifications'}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ModernLayout;
