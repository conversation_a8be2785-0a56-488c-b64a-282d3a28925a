import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Palette as ThemeIcon,
  Storage as DatabaseIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Speed as PerformanceIcon,
} from '@mui/icons-material';

const Settings = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [settings, setSettings] = useState({
    // General settings
    autoSave: true,
    confirmDelete: true,
    showLineNumbers: true,
    
    // Theme settings
    darkMode: false,
    primaryColor: '#1976d2',
    fontSize: 14,
    
    // Database settings
    queryTimeout: 30000,
    maxConnections: 10,
    connectionTimeout: 10000,
    
    // Performance settings
    enableCaching: true,
    cacheTimeout: 300000,
    maxQueryHistory: 100,
    
    // Notification settings
    showNotifications: true,
    soundEnabled: false,
    emailNotifications: true,
  });

  const handleSettingChange = (setting) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setSettings(prev => ({
      ...prev,
      [setting]: value,
    }));
  };

  const handleSave = () => {
    // Save settings to localStorage or API
    localStorage.setItem('appSettings', JSON.stringify(settings));
    // Show success message
  };

  const handleReset = () => {
    // Reset to default settings
    setSettings({
      autoSave: true,
      confirmDelete: true,
      showLineNumbers: true,
      darkMode: false,
      primaryColor: '#1976d2',
      fontSize: 14,
      queryTimeout: 30000,
      maxConnections: 10,
      connectionTimeout: 10000,
      enableCaching: true,
      cacheTimeout: 300000,
      maxQueryHistory: 100,
      showNotifications: true,
      soundEnabled: false,
      emailNotifications: true,
    });
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Settings
      </Typography>

      <Paper elevation={2}>
        <Tabs
          value={activeTab}
          onChange={(e, v) => setActiveTab(v)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="General" icon={<SettingsIcon />} />
          <Tab label="Appearance" icon={<ThemeIcon />} />
          <Tab label="Database" icon={<DatabaseIcon />} />
          <Tab label="Performance" icon={<PerformanceIcon />} />
          <Tab label="Notifications" icon={<NotificationsIcon />} />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* General Settings */}
          {activeTab === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  General Settings
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Editor Preferences
                    </Typography>
                    <List>
                      <ListItem>
                        <ListItemText primary="Auto Save" secondary="Automatically save queries" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.autoSave}
                              onChange={handleSettingChange('autoSave')}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Show Line Numbers" secondary="Display line numbers in editor" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.showLineNumbers}
                              onChange={handleSettingChange('showLineNumbers')}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Confirm Delete" secondary="Show confirmation before deleting" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.confirmDelete}
                              onChange={handleSettingChange('confirmDelete')}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Appearance Settings */}
          {activeTab === 1 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Appearance Settings
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Theme
                    </Typography>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.darkMode}
                          onChange={handleSettingChange('darkMode')}
                        />
                      }
                      label="Dark Mode"
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Font Size"
                      type="number"
                      value={settings.fontSize}
                      onChange={handleSettingChange('fontSize')}
                      InputProps={{ inputProps: { min: 10, max: 24 } }}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Primary Color"
                      type="color"
                      value={settings.primaryColor}
                      onChange={handleSettingChange('primaryColor')}
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Database Settings */}
          {activeTab === 2 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Database Settings
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Connection Settings
                    </Typography>
                    
                    <TextField
                      fullWidth
                      label="Query Timeout (ms)"
                      type="number"
                      value={settings.queryTimeout}
                      onChange={handleSettingChange('queryTimeout')}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Max Connections"
                      type="number"
                      value={settings.maxConnections}
                      onChange={handleSettingChange('maxConnections')}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Connection Timeout (ms)"
                      type="number"
                      value={settings.connectionTimeout}
                      onChange={handleSettingChange('connectionTimeout')}
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Performance Settings */}
          {activeTab === 3 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Performance Settings
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Caching & Performance
                    </Typography>
                    
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.enableCaching}
                          onChange={handleSettingChange('enableCaching')}
                        />
                      }
                      label="Enable Caching"
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Cache Timeout (ms)"
                      type="number"
                      value={settings.cacheTimeout}
                      onChange={handleSettingChange('cacheTimeout')}
                      disabled={!settings.enableCaching}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Max Query History"
                      type="number"
                      value={settings.maxQueryHistory}
                      onChange={handleSettingChange('maxQueryHistory')}
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Notification Settings */}
          {activeTab === 4 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Notification Settings
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Notification Preferences
                    </Typography>
                    <List>
                      <ListItem>
                        <ListItemText primary="Show Notifications" secondary="Display in-app notifications" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.showNotifications}
                              onChange={handleSettingChange('showNotifications')}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Sound Enabled" secondary="Play sound for notifications" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.soundEnabled}
                              onChange={handleSettingChange('soundEnabled')}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Email Notifications" secondary="Send notifications via email" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.emailNotifications}
                              onChange={handleSettingChange('emailNotifications')}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Action Buttons */}
          <Box sx={{ mt: 4, display: 'flex', gap: 2 }}>
            <Button variant="contained" onClick={handleSave}>
              Save Settings
            </Button>
            <Button variant="outlined" onClick={handleReset}>
              Reset to Defaults
            </Button>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default Settings;
