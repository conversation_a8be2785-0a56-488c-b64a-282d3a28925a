import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Chip,
  Alert,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
  Timeline as TimelineIcon,
  QueryStats as QueryStatsIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { useConnection } from '../../contexts/ConnectionContext';
import { formatDistanceToNow } from 'date-fns';

const PerformanceMonitor = () => {
  const { activeConnection, executeQuery } = useConnection();
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [performanceData, setPerformanceData] = useState([]);
  const [currentStats, setCurrentStats] = useState(null);
  const [slowQueries, setSlowQueries] = useState([]);
  const [connectionPool, setConnectionPool] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [error, setError] = useState(null);
  
  const intervalRef = useRef(null);
  const maxDataPoints = 50;

  useEffect(() => {
    if (isMonitoring && activeConnection) {
      startMonitoring();
    } else {
      stopMonitoring();
    }
    
    return () => stopMonitoring();
  }, [isMonitoring, activeConnection]);

  const startMonitoring = () => {
    if (intervalRef.current) return;
    
    intervalRef.current = setInterval(async () => {
      await collectPerformanceData();
    }, 2000); // Collect data every 2 seconds
    
    collectPerformanceData(); // Initial collection
  };

  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const collectPerformanceData = async () => {
    if (!activeConnection) return;
    
    try {
      const timestamp = new Date();
      let stats = {};
      
      // Collect database-specific performance metrics
      switch (activeConnection.type) {
        case 'postgresql':
          stats = await collectPostgreSQLStats();
          break;
        case 'mysql':
          stats = await collectMySQLStats();
          break;
        case 'sqlite':
          stats = await collectSQLiteStats();
          break;
      }
      
      // Add timestamp and simulated system metrics
      const dataPoint = {
        timestamp: timestamp.toISOString(),
        time: timestamp.toLocaleTimeString(),
        ...stats,
        cpuUsage: Math.random() * 100,
        memoryUsage: Math.random() * 100,
        diskIO: Math.random() * 1000,
        networkLatency: Math.random() * 50 + 10,
      };
      
      setPerformanceData(prev => {
        const newData = [...prev, dataPoint];
        return newData.slice(-maxDataPoints);
      });
      
      setCurrentStats(dataPoint);
      
      // Check for alerts
      checkAlerts(dataPoint);
      
    } catch (err) {
      setError(err.message);
    }
  };

  const collectPostgreSQLStats = async () => {
    try {
      // Active connections
      const connectionsResult = await executeQuery(`
        SELECT COUNT(*) as active_connections 
        FROM pg_stat_activity 
        WHERE state = 'active'
      `);
      
      // Database size
      const sizeResult = await executeQuery(`
        SELECT pg_database_size(current_database()) as db_size
      `);
      
      // Long running queries
      const longQueriesResult = await executeQuery(`
        SELECT query, state, query_start, now() - query_start as duration
        FROM pg_stat_activity 
        WHERE state = 'active' 
        AND now() - query_start > interval '5 seconds'
        ORDER BY query_start
        LIMIT 10
      `);
      
      setSlowQueries(longQueriesResult.rows || []);
      
      return {
        activeConnections: connectionsResult.rows[0]?.active_connections || 0,
        databaseSize: sizeResult.rows[0]?.db_size || 0,
        queriesPerSecond: Math.random() * 100,
        cacheHitRatio: 95 + Math.random() * 5,
      };
    } catch (err) {
      console.error('Error collecting PostgreSQL stats:', err);
      return {};
    }
  };

  const collectMySQLStats = async () => {
    try {
      // Active connections
      const connectionsResult = await executeQuery(`
        SELECT COUNT(*) as active_connections 
        FROM information_schema.processlist 
        WHERE command != 'Sleep'
      `);
      
      // Database size
      const sizeResult = await executeQuery(`
        SELECT SUM(data_length + index_length) as db_size
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      `);
      
      return {
        activeConnections: connectionsResult.rows[0]?.active_connections || 0,
        databaseSize: sizeResult.rows[0]?.db_size || 0,
        queriesPerSecond: Math.random() * 100,
        cacheHitRatio: 95 + Math.random() * 5,
      };
    } catch (err) {
      console.error('Error collecting MySQL stats:', err);
      return {};
    }
  };

  const collectSQLiteStats = async () => {
    try {
      // SQLite doesn't have the same monitoring capabilities
      // Simulate some basic metrics
      return {
        activeConnections: 1,
        databaseSize: Math.random() * 1000000,
        queriesPerSecond: Math.random() * 50,
        cacheHitRatio: 90 + Math.random() * 10,
      };
    } catch (err) {
      console.error('Error collecting SQLite stats:', err);
      return {};
    }
  };

  const checkAlerts = (stats) => {
    const newAlerts = [];
    
    if (stats.cpuUsage > 90) {
      newAlerts.push({
        id: Date.now() + 'cpu',
        type: 'error',
        message: `High CPU usage: ${stats.cpuUsage.toFixed(1)}%`,
        timestamp: new Date(),
      });
    }
    
    if (stats.memoryUsage > 85) {
      newAlerts.push({
        id: Date.now() + 'memory',
        type: 'warning',
        message: `High memory usage: ${stats.memoryUsage.toFixed(1)}%`,
        timestamp: new Date(),
      });
    }
    
    if (stats.activeConnections > 50) {
      newAlerts.push({
        id: Date.now() + 'connections',
        type: 'warning',
        message: `High connection count: ${stats.activeConnections}`,
        timestamp: new Date(),
      });
    }
    
    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev].slice(0, 20));
    }
  };

  const MetricCard = ({ title, value, unit, icon, color = 'primary', threshold }) => {
    const isAlert = threshold && value > threshold;
    
    return (
      <Card elevation={2} sx={{ height: '100%' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography color="textSecondary" gutterBottom variant="body2">
                {title}
              </Typography>
              <Typography 
                variant="h4" 
                component="div" 
                color={isAlert ? 'error' : color}
              >
                {typeof value === 'number' ? value.toFixed(1) : value}
                {unit && (
                  <Typography variant="h6" component="span" color="textSecondary">
                    {unit}
                  </Typography>
                )}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              {icon}
              {isAlert && <WarningIcon color="error" sx={{ mt: 1 }} />}
            </Box>
          </Box>
          {typeof value === 'number' && (
            <LinearProgress
              variant="determinate"
              value={Math.min(value, 100)}
              color={isAlert ? 'error' : value > 75 ? 'warning' : 'success'}
              sx={{ mt: 2, height: 6, borderRadius: 3 }}
            />
          )}
        </CardContent>
      </Card>
    );
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'error': return <ErrorIcon color="error" />;
      case 'warning': return <WarningIcon color="warning" />;
      default: return <SuccessIcon color="success" />;
    }
  };

  if (!activeConnection) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          Please connect to a database to start performance monitoring.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Performance Monitor
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={isMonitoring}
                onChange={(e) => setIsMonitoring(e.target.checked)}
                color="primary"
              />
            }
            label={isMonitoring ? 'Monitoring Active' : 'Start Monitoring'}
          />
          <Chip
            icon={isMonitoring ? <StartIcon /> : <StopIcon />}
            label={isMonitoring ? 'Live' : 'Stopped'}
            color={isMonitoring ? 'success' : 'default'}
          />
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Tabs value={activeTab} onChange={(e, v) => setActiveTab(v)} sx={{ mb: 3 }}>
        <Tab label="Real-time Metrics" />
        <Tab label="Query Performance" />
        <Tab label="Connection Pool" />
        <Tab label="Alerts" />
      </Tabs>

      {/* Real-time Metrics Tab */}
      {activeTab === 0 && (
        <Grid container spacing={3}>
          {/* Current Stats Cards */}
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="CPU Usage"
              value={currentStats?.cpuUsage}
              unit="%"
              icon={<SpeedIcon color="primary" />}
              threshold={90}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Memory Usage"
              value={currentStats?.memoryUsage}
              unit="%"
              icon={<MemoryIcon color="secondary" />}
              threshold={85}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Active Connections"
              value={currentStats?.activeConnections}
              icon={<NetworkIcon color="info" />}
              threshold={50}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Queries/sec"
              value={currentStats?.queriesPerSecond}
              icon={<QueryStatsIcon color="warning" />}
            />
          </Grid>

          {/* Performance Charts */}
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 2, height: 400 }}>
              <Typography variant="h6" gutterBottom>
                CPU & Memory Usage
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <LineChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis domain={[0, 100]} />
                  <RechartsTooltip />
                  <Line 
                    type="monotone" 
                    dataKey="cpuUsage" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    name="CPU %"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="memoryUsage" 
                    stroke="#82ca9d" 
                    strokeWidth={2}
                    name="Memory %"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 2, height: 400 }}>
              <Typography variant="h6" gutterBottom>
                Database Activity
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <AreaChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <RechartsTooltip />
                  <Area 
                    type="monotone" 
                    dataKey="queriesPerSecond" 
                    stackId="1"
                    stroke="#ffc658" 
                    fill="#ffc658"
                    name="Queries/sec"
                  />
                  <Area 
                    type="monotone" 
                    dataKey="activeConnections" 
                    stackId="2"
                    stroke="#ff7300" 
                    fill="#ff7300"
                    name="Connections"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Query Performance Tab */}
      {activeTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                Slow Running Queries
              </Typography>
              {slowQueries.length === 0 ? (
                <Alert severity="success">No slow queries detected</Alert>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Query</TableCell>
                        <TableCell>Duration</TableCell>
                        <TableCell>State</TableCell>
                        <TableCell>Started</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {slowQueries.map((query, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography 
                              variant="body2" 
                              sx={{ 
                                fontFamily: 'monospace',
                                maxWidth: 300,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {query.query}
                            </Typography>
                          </TableCell>
                          <TableCell>{query.duration}</TableCell>
                          <TableCell>
                            <Chip label={query.state} size="small" />
                          </TableCell>
                          <TableCell>
                            {formatDistanceToNow(new Date(query.query_start), { addSuffix: true })}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Alerts Tab */}
      {activeTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                System Alerts
              </Typography>
              {alerts.length === 0 ? (
                <Alert severity="success">No alerts at this time</Alert>
              ) : (
                <List>
                  {alerts.map((alert, index) => (
                    <React.Fragment key={alert.id}>
                      <ListItem>
                        <ListItemIcon>
                          {getAlertIcon(alert.type)}
                        </ListItemIcon>
                        <ListItemText
                          primary={alert.message}
                          secondary={formatDistanceToNow(alert.timestamp, { addSuffix: true })}
                        />
                      </ListItem>
                      {index < alerts.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default PerformanceMonitor;
