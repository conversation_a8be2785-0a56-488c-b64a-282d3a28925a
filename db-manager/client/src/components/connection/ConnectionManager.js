import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as ConnectIcon,
  Stop as DisconnectIcon,
  Storage as DatabaseIcon,
  CheckCircle as ConnectedIcon,
  Error as ErrorIcon,
  Schedule as RecentIcon,
} from '@mui/icons-material';
import { useConnection } from '../../contexts/ConnectionContext';
import ConnectionDialog from './ConnectionDialog';
import { formatDistanceToNow } from 'date-fns';

const ConnectionManager = () => {
  const { connections, activeConnection, connect, disconnect, deleteConnection } = useConnection();
  const [showConnectionDialog, setShowConnectionDialog] = useState(false);
  const [editingConnection, setEditingConnection] = useState(null);
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState(null);

  const handleCreateConnection = () => {
    setEditingConnection(null);
    setShowConnectionDialog(true);
  };

  const handleEditConnection = (connection) => {
    setEditingConnection(connection);
    setShowConnectionDialog(true);
  };

  const handleDeleteConnection = (connection) => {
    setDeleteConfirmDialog(connection);
  };

  const confirmDelete = () => {
    if (deleteConfirmDialog) {
      deleteConnection(deleteConfirmDialog.id);
      setDeleteConfirmDialog(null);
    }
  };

  const handleConnect = async (connection) => {
    try {
      await connect(connection);
    } catch (error) {
      console.error('Connection failed:', error);
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  const getConnectionStatus = (connection) => {
    if (activeConnection && activeConnection.id === connection.id) {
      return { status: 'connected', color: 'success', icon: ConnectedIcon };
    }
    return { status: 'disconnected', color: 'default', icon: DatabaseIcon };
  };

  const getDatabaseTypeColor = (type) => {
    const colors = {
      postgresql: 'primary',
      mysql: 'warning',
      sqlite: 'info',
      mongodb: 'success',
    };
    return colors[type] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Database Connections
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateConnection}
        >
          New Connection
        </Button>
      </Box>

      {/* Active Connection */}
      {activeConnection && (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <Box>
              <Typography variant="subtitle1">
                Connected to: {activeConnection.name}
              </Typography>
              <Typography variant="body2">
                {activeConnection.type} - {activeConnection.host}:{activeConnection.port}
              </Typography>
            </Box>
            <Button
              variant="outlined"
              color="error"
              startIcon={<DisconnectIcon />}
              onClick={handleDisconnect}
            >
              Disconnect
            </Button>
          </Box>
        </Alert>
      )}

      {/* Connections Grid */}
      <Grid container spacing={3}>
        {connections.length === 0 ? (
          <Grid item xs={12}>
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <DatabaseIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No database connections configured
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Create your first database connection to get started
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateConnection}
              >
                Create Connection
              </Button>
            </Paper>
          </Grid>
        ) : (
          connections.map((connection) => {
            const status = getConnectionStatus(connection);
            const StatusIcon = status.icon;
            
            return (
              <Grid item xs={12} sm={6} md={4} key={connection.id}>
                <Card
                  elevation={2}
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    border: activeConnection?.id === connection.id ? 2 : 0,
                    borderColor: 'primary.main',
                  }}
                >
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <StatusIcon color={status.color} sx={{ mr: 1 }} />
                      <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                        {connection.name}
                      </Typography>
                      <Chip
                        label={connection.type}
                        color={getDatabaseTypeColor(connection.type)}
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {connection.host}:{connection.port}
                    </Typography>
                    
                    {connection.database && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Database: {connection.database}
                      </Typography>
                    )}
                    
                    <Chip
                      label={status.status}
                      color={status.color}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                    
                    {connection.lastUsed && (
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        Last used: {formatDistanceToNow(new Date(connection.lastUsed), { addSuffix: true })}
                      </Typography>
                    )}
                  </CardContent>
                  
                  <CardActions>
                    {activeConnection?.id === connection.id ? (
                      <Button
                        size="small"
                        color="error"
                        startIcon={<DisconnectIcon />}
                        onClick={handleDisconnect}
                      >
                        Disconnect
                      </Button>
                    ) : (
                      <Button
                        size="small"
                        color="primary"
                        startIcon={<ConnectIcon />}
                        onClick={() => handleConnect(connection)}
                      >
                        Connect
                      </Button>
                    )}
                    
                    <IconButton
                      size="small"
                      onClick={() => handleEditConnection(connection)}
                    >
                      <EditIcon />
                    </IconButton>
                    
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteConnection(connection)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </CardActions>
                </Card>
              </Grid>
            );
          })
        )}
      </Grid>

      {/* Recent Connections */}
      {connections.length > 0 && (
        <Paper elevation={2} sx={{ mt: 4, p: 3 }}>
          <Typography variant="h6" gutterBottom>
            <RecentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Recent Connections
          </Typography>
          <List>
            {connections
              .filter(conn => conn.lastUsed)
              .sort((a, b) => new Date(b.lastUsed) - new Date(a.lastUsed))
              .slice(0, 5)
              .map((connection, index) => (
                <React.Fragment key={connection.id}>
                  <ListItem button onClick={() => handleConnect(connection)}>
                    <ListItemIcon>
                      <DatabaseIcon color={getDatabaseTypeColor(connection.type)} />
                    </ListItemIcon>
                    <ListItemText
                      primary={connection.name}
                      secondary={`${connection.type} - ${formatDistanceToNow(new Date(connection.lastUsed), { addSuffix: true })}`}
                    />
                    <ListItemSecondaryAction>
                      <Chip
                        label={connection.type}
                        color={getDatabaseTypeColor(connection.type)}
                        size="small"
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < 4 && <Divider />}
                </React.Fragment>
              ))}
          </List>
        </Paper>
      )}

      {/* Connection Dialog */}
      <ConnectionDialog
        open={showConnectionDialog}
        onClose={() => setShowConnectionDialog(false)}
        connection={editingConnection}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={Boolean(deleteConfirmDialog)}
        onClose={() => setDeleteConfirmDialog(null)}
      >
        <DialogTitle>Delete Connection</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the connection "{deleteConfirmDialog?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmDialog(null)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConnectionManager;
