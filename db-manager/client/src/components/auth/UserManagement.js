import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  Key as KeyIcon,
  Shield as ShieldIcon,
  Visibility as ViewIcon,
  VisibilityOff as HideIcon,
  AdminPanelSettings as AdminIcon,
  SupervisorAccount as SupervisorIcon,
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { formatDistanceToNow } from 'date-fns';

const USER_ROLES = {
  admin: { name: 'Administrator', color: 'error', icon: AdminIcon },
  editor: { name: 'Editor', color: 'warning', icon: SupervisorIcon },
  viewer: { name: 'Viewer', color: 'info', icon: PersonIcon },
};

const PERMISSIONS = {
  'database.connect': 'Connect to databases',
  'database.create': 'Create databases',
  'database.drop': 'Drop databases',
  'query.execute': 'Execute queries',
  'query.save': 'Save queries',
  'schema.view': 'View schema',
  'schema.modify': 'Modify schema',
  'users.manage': 'Manage users',
  'monitoring.view': 'View monitoring',
  'export.data': 'Export data',
};

const UserManagement = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [sessions, setSessions] = useState([]);
  const [showUserDialog, setShowUserDialog] = useState(false);
  const [showRoleDialog, setShowRoleDialog] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [editingRole, setEditingRole] = useState(null);
  const [userForm, setUserForm] = useState({
    username: '',
    email: '',
    password: '',
    role: 'viewer',
    active: true,
  });
  const [roleForm, setRoleForm] = useState({
    name: '',
    description: '',
    permissions: [],
  });
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    loadUsers();
    loadRoles();
    loadSessions();
  }, []);

  const loadUsers = () => {
    // Simulate loading users from API
    const mockUsers = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        active: true,
        lastLogin: new Date(Date.now() - 3600000),
        createdAt: new Date(Date.now() - 86400000 * 30),
      },
      {
        id: 2,
        username: 'john_doe',
        email: '<EMAIL>',
        role: 'editor',
        active: true,
        lastLogin: new Date(Date.now() - 7200000),
        createdAt: new Date(Date.now() - 86400000 * 15),
      },
      {
        id: 3,
        username: 'jane_smith',
        email: '<EMAIL>',
        role: 'viewer',
        active: false,
        lastLogin: new Date(Date.now() - 86400000 * 2),
        createdAt: new Date(Date.now() - 86400000 * 7),
      },
    ];
    setUsers(mockUsers);
  };

  const loadRoles = () => {
    // Simulate loading roles from API
    const mockRoles = [
      {
        id: 1,
        name: 'admin',
        description: 'Full system access',
        permissions: Object.keys(PERMISSIONS),
        userCount: 1,
      },
      {
        id: 2,
        name: 'editor',
        description: 'Can execute queries and modify schema',
        permissions: [
          'database.connect',
          'query.execute',
          'query.save',
          'schema.view',
          'schema.modify',
          'monitoring.view',
          'export.data',
        ],
        userCount: 1,
      },
      {
        id: 3,
        name: 'viewer',
        description: 'Read-only access',
        permissions: [
          'database.connect',
          'query.execute',
          'schema.view',
          'monitoring.view',
        ],
        userCount: 1,
      },
    ];
    setRoles(mockRoles);
  };

  const loadSessions = () => {
    // Simulate loading active sessions
    const mockSessions = [
      {
        id: 1,
        userId: 1,
        username: 'admin',
        ipAddress: '*************',
        userAgent: 'Chrome 91.0.4472.124',
        loginTime: new Date(Date.now() - 3600000),
        lastActivity: new Date(Date.now() - 300000),
        active: true,
      },
      {
        id: 2,
        userId: 2,
        username: 'john_doe',
        ipAddress: '*************',
        userAgent: 'Firefox 89.0',
        loginTime: new Date(Date.now() - 7200000),
        lastActivity: new Date(Date.now() - 600000),
        active: true,
      },
    ];
    setSessions(mockSessions);
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setUserForm({
      username: '',
      email: '',
      password: '',
      role: 'viewer',
      active: true,
    });
    setShowUserDialog(true);
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setUserForm({
      username: user.username,
      email: user.email,
      password: '',
      role: user.role,
      active: user.active,
    });
    setShowUserDialog(true);
  };

  const handleSaveUser = () => {
    if (editingUser) {
      // Update existing user
      setUsers(prev => prev.map(user => 
        user.id === editingUser.id 
          ? { ...user, ...userForm, password: userForm.password || user.password }
          : user
      ));
    } else {
      // Create new user
      const newUser = {
        id: Date.now(),
        ...userForm,
        lastLogin: null,
        createdAt: new Date(),
      };
      setUsers(prev => [...prev, newUser]);
    }
    setShowUserDialog(false);
  };

  const handleDeleteUser = (userId) => {
    setUsers(prev => prev.filter(user => user.id !== userId));
  };

  const handleCreateRole = () => {
    setEditingRole(null);
    setRoleForm({
      name: '',
      description: '',
      permissions: [],
    });
    setShowRoleDialog(true);
  };

  const handleEditRole = (role) => {
    setEditingRole(role);
    setRoleForm({
      name: role.name,
      description: role.description,
      permissions: role.permissions,
    });
    setShowRoleDialog(true);
  };

  const handleSaveRole = () => {
    if (editingRole) {
      // Update existing role
      setRoles(prev => prev.map(role => 
        role.id === editingRole.id 
          ? { ...role, ...roleForm }
          : role
      ));
    } else {
      // Create new role
      const newRole = {
        id: Date.now(),
        ...roleForm,
        userCount: 0,
      };
      setRoles(prev => [...prev, newRole]);
    }
    setShowRoleDialog(false);
  };

  const handlePermissionChange = (permission) => {
    setRoleForm(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const getRoleInfo = (roleName) => {
    return USER_ROLES[roleName] || { name: roleName, color: 'default', icon: PersonIcon };
  };

  const userColumns = [
    { field: 'username', headerName: 'Username', width: 150 },
    { field: 'email', headerName: 'Email', width: 200 },
    {
      field: 'role',
      headerName: 'Role',
      width: 120,
      renderCell: (params) => {
        const roleInfo = getRoleInfo(params.value);
        return (
          <Chip
            icon={<roleInfo.icon />}
            label={roleInfo.name}
            color={roleInfo.color}
            size="small"
          />
        );
      },
    },
    {
      field: 'active',
      headerName: 'Status',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'Active' : 'Inactive'}
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      ),
    },
    {
      field: 'lastLogin',
      headerName: 'Last Login',
      width: 150,
      renderCell: (params) => (
        params.value ? formatDistanceToNow(params.value, { addSuffix: true }) : 'Never'
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton size="small" onClick={() => handleEditUser(params.row)}>
            <EditIcon />
          </IconButton>
          <IconButton size="small" onClick={() => handleDeleteUser(params.row.id)}>
            <DeleteIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          User Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateUser}
        >
          Add User
        </Button>
      </Box>

      {/* Tabs */}
      <Tabs value={activeTab} onChange={(e, v) => setActiveTab(v)} sx={{ mb: 3 }}>
        <Tab label="Users" icon={<PersonIcon />} />
        <Tab label="Roles & Permissions" icon={<SecurityIcon />} />
        <Tab label="Active Sessions" icon={<KeyIcon />} />
      </Tabs>

      {/* Users Tab */}
      {activeTab === 0 && (
        <Paper elevation={2} sx={{ height: 600 }}>
          <DataGrid
            rows={users}
            columns={userColumns}
            pageSize={10}
            rowsPerPageOptions={[10, 25, 50]}
            disableSelectionOnClick
            sx={{ border: 0 }}
          />
        </Paper>
      )}

      {/* Roles Tab */}
      {activeTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateRole}
              >
                Add Role
              </Button>
            </Box>
          </Grid>
          
          {roles.map((role) => (
            <Grid item xs={12} md={6} lg={4} key={role.id}>
              <Card elevation={2}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <ShieldIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">{role.name}</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {role.description}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Users:</strong> {role.userCount}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Permissions:</strong> {role.permissions.length}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {role.permissions.slice(0, 3).map((permission) => (
                      <Chip
                        key={permission}
                        label={PERMISSIONS[permission]?.split(' ')[0] || permission}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                    {role.permissions.length > 3 && (
                      <Chip
                        label={`+${role.permissions.length - 3} more`}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </Box>
                </CardContent>
                <CardActions>
                  <Button size="small" onClick={() => handleEditRole(role)}>
                    Edit
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Sessions Tab */}
      {activeTab === 2 && (
        <Paper elevation={2}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>IP Address</TableCell>
                  <TableCell>User Agent</TableCell>
                  <TableCell>Login Time</TableCell>
                  <TableCell>Last Activity</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sessions.map((session) => (
                  <TableRow key={session.id}>
                    <TableCell>{session.username}</TableCell>
                    <TableCell>{session.ipAddress}</TableCell>
                    <TableCell>{session.userAgent}</TableCell>
                    <TableCell>
                      {formatDistanceToNow(session.loginTime, { addSuffix: true })}
                    </TableCell>
                    <TableCell>
                      {formatDistanceToNow(session.lastActivity, { addSuffix: true })}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={session.active ? 'Active' : 'Inactive'}
                        color={session.active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Button size="small" color="error">
                        Terminate
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* User Dialog */}
      <Dialog open={showUserDialog} onClose={() => setShowUserDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingUser ? 'Edit User' : 'Create User'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Username"
                value={userForm.username}
                onChange={(e) => setUserForm(prev => ({ ...prev, username: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={userForm.email}
                onChange={(e) => setUserForm(prev => ({ ...prev, email: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={editingUser ? "New Password (leave blank to keep current)" : "Password"}
                type={showPassword ? 'text' : 'password'}
                value={userForm.password}
                onChange={(e) => setUserForm(prev => ({ ...prev, password: e.target.value }))}
                InputProps={{
                  endAdornment: (
                    <IconButton onClick={() => setShowPassword(!showPassword)}>
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={userForm.role}
                  onChange={(e) => setUserForm(prev => ({ ...prev, role: e.target.value }))}
                  label="Role"
                >
                  {roles.map((role) => (
                    <MenuItem key={role.id} value={role.name}>
                      {role.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={userForm.active}
                    onChange={(e) => setUserForm(prev => ({ ...prev, active: e.target.checked }))}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUserDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveUser} variant="contained">
            {editingUser ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Role Dialog */}
      <Dialog open={showRoleDialog} onClose={() => setShowRoleDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingRole ? 'Edit Role' : 'Create Role'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Role Name"
                value={roleForm.name}
                onChange={(e) => setRoleForm(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={roleForm.description}
                onChange={(e) => setRoleForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Permissions
              </Typography>
              <Grid container spacing={1}>
                {Object.entries(PERMISSIONS).map(([key, description]) => (
                  <Grid item xs={12} sm={6} key={key}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={roleForm.permissions.includes(key)}
                          onChange={() => handlePermissionChange(key)}
                        />
                      }
                      label={description}
                    />
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowRoleDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveRole} variant="contained">
            {editingRole ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagement;
