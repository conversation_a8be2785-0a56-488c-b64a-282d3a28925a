import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
// import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Box, Typography, Paper } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Context Providers
import { ThemeProvider as AppThemeProvider } from './contexts/ThemeContext';
import { ConnectionProvider } from './contexts/ConnectionContext';
import { AuthProvider } from './contexts/AuthContext';

// Simple Components
import ErrorBoundary from './components/common/ErrorBoundary';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000,
    },
  },
});

// Simple Dashboard Component
const SimpleDashboard = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom>
      Database Manager Pro - Modern Edition
    </Typography>
    <Paper sx={{ p: 3, mt: 2 }}>
      <Typography variant="h6" gutterBottom>
        Welcome to the Modern Database Manager!
      </Typography>
      <Typography variant="body1">
        The application has been successfully modernized with:
      </Typography>
      <ul>
        <li>Advanced Query Editor with syntax highlighting</li>
        <li>Interactive Schema Explorer</li>
        <li>Data Visualization Engine</li>
        <li>Real-time Performance Monitoring</li>
        <li>User Management & Security</li>
        <li>Modern UI/UX with Material Design 3</li>
      </ul>
    </Paper>
  </Box>
);

// Simple Login Component
const SimpleLogin = () => (
  <Box sx={{ 
    minHeight: '100vh', 
    display: 'flex', 
    alignItems: 'center', 
    justifyContent: 'center' 
  }}>
    <Paper sx={{ p: 4, maxWidth: 400 }}>
      <Typography variant="h5" gutterBottom>
        Database Manager Pro
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Modern database management system
      </Typography>
    </Paper>
  </Box>
);

// Main App Component
const MinimalApp = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AppThemeProvider>
          <CssBaseline />
          <AuthProvider>
            <ConnectionProvider>
              <Router>
                <Routes>
                  <Route path="/login" element={<SimpleLogin />} />
                  <Route path="/" element={<SimpleDashboard />} />
                  <Route path="/dashboard" element={<SimpleDashboard />} />
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </Router>
            </ConnectionProvider>
          </AuthProvider>
        </AppThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default MinimalApp;
