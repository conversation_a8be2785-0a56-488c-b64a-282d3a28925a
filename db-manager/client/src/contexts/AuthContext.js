import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [permissions, setPermissions] = useState([]);

  useEffect(() => {
    // Check for existing authentication on app load
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // Check localStorage for existing session
      const token = localStorage.getItem('authToken');
      const userData = localStorage.getItem('userData');
      
      if (token && userData) {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        setIsAuthenticated(true);
        setPermissions(parsedUser.permissions || []);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      setLoading(true);
      
      // Simulate API call
      const response = await simulateLogin(credentials);
      
      if (response.success) {
        const { user: userData, token } = response;
        
        // Store in localStorage
        localStorage.setItem('authToken', token);
        localStorage.setItem('userData', JSON.stringify(userData));
        
        // Update state
        setUser(userData);
        setIsAuthenticated(true);
        setPermissions(userData.permissions || []);
        
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    // Clear localStorage
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    
    // Reset state
    setUser(null);
    setIsAuthenticated(false);
    setPermissions([]);
  };

  const updateUser = (userData) => {
    setUser(userData);
    localStorage.setItem('userData', JSON.stringify(userData));
  };

  const hasPermission = (permission) => {
    return permissions.includes(permission) || user?.role === 'admin';
  };

  const hasRole = (role) => {
    return user?.role === role;
  };

  // Simulate login API call
  const simulateLogin = async (credentials) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Mock users database
        const users = {
          'admin': {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            password: 'admin123',
            role: 'admin',
            permissions: [
              'database.connect',
              'database.create',
              'database.drop',
              'query.execute',
              'query.save',
              'schema.view',
              'schema.modify',
              'users.manage',
              'monitoring.view',
              'export.data',
            ],
            avatar: null,
            lastLogin: new Date(),
          },
          'editor': {
            id: 2,
            username: 'editor',
            email: '<EMAIL>',
            password: 'editor123',
            role: 'editor',
            permissions: [
              'database.connect',
              'query.execute',
              'query.save',
              'schema.view',
              'schema.modify',
              'monitoring.view',
              'export.data',
            ],
            avatar: null,
            lastLogin: new Date(),
          },
          'viewer': {
            id: 3,
            username: 'viewer',
            email: '<EMAIL>',
            password: 'viewer123',
            role: 'viewer',
            permissions: [
              'database.connect',
              'query.execute',
              'schema.view',
              'monitoring.view',
            ],
            avatar: null,
            lastLogin: new Date(),
          },
        };

        const user = users[credentials.username];
        
        if (user && user.password === credentials.password) {
          const { password, ...userWithoutPassword } = user;
          resolve({
            success: true,
            user: userWithoutPassword,
            token: `mock-jwt-token-${Date.now()}`,
          });
        } else {
          resolve({
            success: false,
            error: 'Invalid username or password',
          });
        }
      }, 1000); // Simulate network delay
    });
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    permissions,
    login,
    logout,
    updateUser,
    hasPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
