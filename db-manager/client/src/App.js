import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import {
  createTheme,
  CssBaseline,
  ThemeProvider as MuiThemeProvider,
} from "@mui/material";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { SnackbarProvider } from "notistack";

// Layout Components
import Layout from "./components/layout/Layout";
// import ModernLayout from "./components/layout/ModernLayout";
import LoadingSpinner from "./components/common/LoadingSpinner";

// Legacy Pages
import DashboardPage from "./pages/DashboardPage";
import ConnectionsPage from "./pages/ConnectionsPage";
import QueryPage from "./pages/QueryPage";
import SettingsPage from "./pages/SettingsPage";
import NotFoundPage from "./pages/NotFoundPage";

// Modern Components
import ModernDashboard from "./components/dashboard/ModernDashboard";
import AdvancedQueryEditor from "./components/query/AdvancedQueryEditor";
import SimpleSchemaExplorer from "./components/schema/SimpleSchemaExplorer";
import SimpleDataVisualization from "./components/visualization/SimpleDataVisualization";
import SimplePerformanceMonitor from "./components/monitoring/SimplePerformanceMonitor";
// import UserManagement from "./components/auth/UserManagement";
// import ConnectionManager from "./components/connection/ConnectionManager";
// import Settings from "./components/settings/Settings";
import LoginPage from "./components/auth/LoginPage";
import ProfilePage from "./components/auth/ProfilePage";

// Context
import { ConnectionProvider } from "./contexts/ConnectionContext";
import { ThemeProvider, useTheme } from "./contexts/ThemeContext";
import { AuthProvider } from "./contexts/AuthContext";
import ErrorBoundary from "./components/common/ErrorBoundary";

// Utils
import { getTheme } from "./utils/theme";

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function AppContent() {
  const { mode } = useTheme();
  const theme = createTheme(getTheme(mode));

  return (
    <QueryClientProvider client={queryClient}>
      <MuiThemeProvider theme={theme}>
        <SnackbarProvider
          maxSnack={3}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          autoHideDuration={5000}
        >
          <CssBaseline />
          <AuthProvider>
            <ConnectionProvider>
              <Router>
                <React.Suspense fallback={<LoadingSpinner />}>
                  <Routes>
                    {/* Public Routes */}
                    <Route path="/login" element={<LoginPage />} />

                    {/* Modern Routes */}
                    <Route path="/modern" element={<ModernLayout />}>
                      <Route index element={<Navigate to="/modern/dashboard" replace />} />
                      <Route path="dashboard" element={<ModernDashboard />} />
                      <Route path="query" element={<AdvancedQueryEditor />} />
                      <Route path="schema" element={<SimpleSchemaExplorer />} />
                      <Route path="visualize" element={<SimpleDataVisualization />} />
                      <Route path="monitoring" element={<SimplePerformanceMonitor />} />
                      <Route path="users" element={<div>User Management Coming Soon</div>} />
                      <Route path="connections" element={<div>Connection Manager Coming Soon</div>} />
                      <Route path="settings" element={<div>Settings Coming Soon</div>} />
                      <Route path="profile" element={<ProfilePage />} />
                    </Route>

                    {/* Legacy Routes */}
                    <Route path="/legacy" element={<Layout />}>
                      <Route path="dashboard" element={<DashboardPage />} />
                      <Route path="connections" element={<ConnectionsPage />} />
                      <Route path="query" element={<QueryPage />} />
                      <Route path="query/:connectionId" element={<QueryPage />} />
                      <Route path="settings" element={<SettingsPage />} />
                    </Route>

                    {/* Default Routes */}
                    <Route path="/" element={<Navigate to="/modern/dashboard" replace />} />
                    <Route path="/dashboard" element={<Navigate to="/modern/dashboard" replace />} />
                    <Route path="/connections" element={<Navigate to="/modern/connections" replace />} />
                    <Route path="/query" element={<Navigate to="/modern/query" replace />} />
                    <Route path="/settings" element={<Navigate to="/modern/settings" replace />} />
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </React.Suspense>
              </Router>
            </ConnectionProvider>
          </AuthProvider>
        </SnackbarProvider>
      </MuiThemeProvider>
      {process.env.NODE_ENV === "development" && (
        <ReactQueryDevtools initialIsOpen={false} position="bottom-right" />
      )}
    </QueryClientProvider>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
