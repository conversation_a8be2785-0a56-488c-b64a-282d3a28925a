msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-06-20 16:04+0000\n"
"PO-Revision-Date: 2025-06-21 16:37\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Bosnian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: bs\n"
"X-Crowdin-File: /[frappe.lms] develop/lms/locale/main.pot\n"
"X-Crowdin-File-ID: 90\n"
"Language: bs_BA\n"

#: lms/templates/emails/assignment_submission.html:5
msgid " Please evaluate and grade it."
msgstr "Procjeni i Ocjeni"

#: frontend/src/pages/Programs.vue:39
#, python-format
msgid "% completed"
msgstr "% završeno"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/lms-settings/LMS%20Settings\">LMS Settings</a>"
msgstr "<a href=\"/app/lms-settings/LMS%20Settings\">Postavke</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/web-page/new-web-page-1\">Setup a Home Page</a>"
msgstr "<a href=\"/app/web-page/new-web-page-1\">Postavi Početnu Stranicu</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses\">Visit LMS Portal</a>"
msgstr "<a href=\"/lms/courses\">Posjeti Portal</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses/new/edit\">Create a Course</a>"
msgstr "<a href=\"/lms/courses/new/edit\">Kreiraj Kurs</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"https://docs.frappe.io/learning\">Documentation</a>"
msgstr "<a href=\"https://docs.frappe.io/learning\">Dokumentacija</a>"

#: frontend/src/components/Modals/EmailTemplateModal.vue:50
msgid "<p>Dear {{ member_name }},</p>\\n\\n<p>You have been enrolled in our upcoming batch {{ batch_name }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappe Learning</p>"
msgstr "<p>Dragi/a {{ member_name }},</p>\\n\\n<p>Upisani ste u našu sljedeću grupu {{ batch_name }}.</p>\\n\\n<p>Hvala,</p>\\n<p>Frappe Učenje</p>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Get Started</b></span>"
msgstr "<span class=\"h4\"><b>Započni</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Master</b></span>"
msgstr "<span class=\"h4\"><b>Postavke</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span style=\"font-size: 18px;\"><b>Statistics</b></span>"
msgstr "<span style=\"font-size: 18px;\"><b>Statistika</b></span>"

#: lms/lms/doctype/lms_course/lms_course.py:63
msgid "A course cannot have both paid certificate and certificate of completion."
msgstr "Kurs ne može imati i plaćeni certifikat i certifikat o završenom kursu."

#: frontend/src/pages/CourseForm.vue:82
msgid "A one line introduction to the course that appears on the course card"
msgstr "Uvod u kurs u jednom redu koji se pojavljuje na kartici kursa"

#: frontend/src/pages/ProfileAbout.vue:4
msgid "About"
msgstr "O"

#: frontend/src/pages/Batch.vue:101
msgid "About this batch"
msgstr "O ovoj Grupi"

#. Label of the verify_terms (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Acceptance for Terms and/or Policies"
msgstr "Prihvatanje Uslova i/ili Pravila"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Accepted"
msgstr "Prihvaćeno"

#: frontend/src/components/Settings/ZoomSettings.vue:174
msgid "Account"
msgstr "Račun"

#. Label of the account_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the account_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:55
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Account ID"
msgstr "Račun"

#. Label of the account_name (Data) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:30
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Account Name"
msgstr "Naziv Računa"

#: frontend/src/pages/ProfileAbout.vue:17
msgid "Achievements"
msgstr "Dostignuća"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Active"
msgstr "Aktivan"

#: frontend/src/pages/Statistics.vue:16
msgid "Active Members"
msgstr "Aktivni Članovi"

#: frontend/src/components/Assessments.vue:11
#: frontend/src/components/BatchCourses.vue:11
#: frontend/src/components/BatchStudents.vue:73
#: frontend/src/components/LiveClass.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:29
#: frontend/src/components/Settings/Categories.vue:43
#: frontend/src/components/Settings/Evaluators.vue:39
#: frontend/src/components/Settings/Members.vue:44
#: frontend/src/pages/ProgramForm.vue:30 frontend/src/pages/ProgramForm.vue:92
#: frontend/src/pages/ProgramForm.vue:137
msgid "Add"
msgstr "Dodaj"

#: frontend/src/components/CourseOutline.vue:18
#: frontend/src/components/CreateOutline.vue:18
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Add Chapter"
msgstr "Dodaj Poglavlje"

#: frontend/src/components/CourseOutline.vue:133
msgid "Add Lesson"
msgstr "Dodaj Lekciju"

#: frontend/src/components/VideoBlock.vue:121
msgid "Add Quiz to Video"
msgstr "Dodaj kviz u video"

#: frontend/src/pages/ProfileEvaluator.vue:89
msgid "Add Slot"
msgstr "Dodaj Vrijeme"

#: lms/templates/onboarding_header.html:26
msgid "Add a Chapter"
msgstr "Dodaj Poglavlje"

#: lms/templates/onboarding_header.html:33
msgid "Add a Lesson"
msgstr "Dodaj Lekciju"

#: frontend/src/components/Modals/StudentModal.vue:5
msgid "Add a Student"
msgstr "Dodaj Studenta"

#: frontend/src/components/AppSidebar.vue:567
msgid "Add a chapter"
msgstr "Dodaj Poglavlje"

#: frontend/src/components/Modals/BatchCourseModal.vue:5
msgid "Add a course"
msgstr "Dodaj Kurs"

#: frontend/src/pages/CourseForm.vue:67
msgid "Add a keyword and then press enter"
msgstr "Dodaj ključnu riječ, a zatim pritisnite enter"

#: frontend/src/components/AppSidebar.vue:568
msgid "Add a lesson"
msgstr "Dodaj Lekciju"

#: frontend/src/components/Modals/Question.vue:163
#: frontend/src/pages/QuizForm.vue:183
msgid "Add a new question"
msgstr "Dodaj novo pitanje"

#: frontend/src/components/AssessmentPlugin.vue:11
msgid "Add a quiz to your lesson"
msgstr "Dodaj kviz svojoj lekciji"

#: frontend/src/components/Modals/AssessmentModal.vue:5
msgid "Add an assessment"
msgstr "Dodaj Procjenu"

#: frontend/src/components/AssessmentPlugin.vue:14
msgid "Add an assignment to your lesson"
msgstr "Dodaj zadatak svojoj lekciji"

#: lms/lms/doctype/lms_question/lms_question.py:66
msgid "Add at least one possible answer for this question: {0}"
msgstr "Dodaj barem jedan mogući odgovor na ovo pitanje: {0}"

#: frontend/src/components/AppSidebar.vue:531
msgid "Add courses to your batch"
msgstr "Dodaj kurseve u vašu grupu"

#: frontend/src/components/Modals/QuizInVideo.vue:5
msgid "Add quiz to this video"
msgstr "Dodaj kviz ovom videu"

#: frontend/src/components/AppSidebar.vue:510
msgid "Add students to your batch"
msgstr "Dodaj učenike u vašu grupu"

#: frontend/src/components/Modals/PageModal.vue:6
msgid "Add web page to sidebar"
msgstr "Dodaj web stranicu na bočnu traku"

#: frontend/src/components/Assignment.vue:68
msgid "Add your assignment as {0}"
msgstr "Dodaj zadatak kao {0}"

#: frontend/src/components/AppSidebar.vue:443
msgid "Add your first chapter"
msgstr "Dodaj vaše prvo poglavlje"

#: frontend/src/components/AppSidebar.vue:459
msgid "Add your first lesson"
msgstr "Dodaj vašu prvu lekciju"

#. Label of the address (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:64
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Address"
msgstr "Adresa"

#: frontend/src/pages/Billing.vue:74
msgid "Address Line 1"
msgstr "Adresna linija 1"

#: frontend/src/pages/Billing.vue:78
msgid "Address Line 2"
msgstr "Adresna linija 2"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Admin"
msgstr "Admin"

#. Name of a role
#: frontend/src/pages/Batches.vue:273 lms/lms/doctype/lms_badge/lms_badge.json
msgid "All"
msgstr "Sve"

#: frontend/src/pages/Batches.vue:26
msgid "All Batches"
msgstr "Sve Grupe"

#: frontend/src/pages/Courses.vue:26 lms/lms/widgets/BreadCrumb.html:3
msgid "All Courses"
msgstr "Svi Kursevi"

#: lms/templates/quiz/quiz.html:141
msgid "All Submissions"
msgstr "Sva Podnošenja"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:44
msgid "All questions should have the same marks if the limit is set."
msgstr "Sva pitanja bi trebala imati iste ocjene ako je ograničenje postavljeno."

#. Label of the allow_guest_access (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Allow Guest Access"
msgstr "Dozvoli Pristup Gostima"

#. Label of the allow_posting (Check) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Allow Job Posting From Website"
msgstr "Dozvolite objavljivanje poslova sa web stranice"

#. Label of the allow_self_enrollment (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow Self Enrollment"
msgstr "Dozvoli Samostalnu Registraciju"

#. Label of the allow_future (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow accessing future dates"
msgstr "Dozvoli Pristup Budućim Datumima"

#: frontend/src/pages/BatchForm.vue:57
msgid "Allow self enrollment"
msgstr "Dozvoli Samostalnu Registraciju"

#: lms/lms/user.py:34
msgid "Already Registered"
msgstr "Već Registrovan"

#. Label of the amount (Currency) field in DocType 'LMS Batch'
#. Label of the course_price (Currency) field in DocType 'LMS Course'
#. Label of the amount (Currency) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:265 frontend/src/pages/CourseForm.vue:245
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount"
msgstr "Iznos"

#. Label of the amount_usd (Currency) field in DocType 'LMS Batch'
#. Label of the amount_usd (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Amount (USD)"
msgstr "Iznos (USD)"

#: lms/lms/doctype/lms_batch/lms_batch.py:70
msgid "Amount and currency are required for paid batches."
msgstr "Iznos i Valuta su obavezni za plaćene grupe."

#: lms/lms/doctype/lms_course/lms_course.py:74
msgid "Amount and currency are required for paid certificates."
msgstr "Iznos i valuta su obevezni za plaćene certifikate."

#: lms/lms/doctype/lms_course/lms_course.py:71
msgid "Amount and currency are required for paid courses."
msgstr "Iznos i valuta su potrebni za plaćene kurseve."

#. Label of the amount_with_gst (Currency) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount with GST"
msgstr "Iznos sa PDV-om"

#: frontend/src/components/Modals/AnnouncementModal.vue:33
msgid "Announcement"
msgstr "Objava"

#: frontend/src/components/Modals/AnnouncementModal.vue:101
msgid "Announcement has been sent successfully"
msgstr "Objava je uspješno poslana"

#: frontend/src/components/Modals/AnnouncementModal.vue:96
msgid "Announcement is required"
msgstr "Najava je obavezna"

#. Label of the answer (Text Editor) field in DocType 'LMS Assignment'
#. Label of the answer (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the answer (Code) field in DocType 'LMS Exercise'
#: frontend/src/pages/QuizSubmission.vue:60
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Answer"
msgstr "Odgovori"

#: frontend/src/pages/CourseForm.vue:112 frontend/src/pages/CourseForm.vue:131
msgid "Appears on the course card in the course list"
msgstr "Pojavljuje se na kartici kursa u listi kurseva"

#: frontend/src/pages/BatchForm.vue:240
msgid "Appears when the batch URL is shared on any online platform"
msgstr "Pojavljuje se kada se grupni URL dijeli na bilo kojoj online platformi"

#: frontend/src/pages/BatchForm.vue:221
msgid "Appears when the batch URL is shared on socials"
msgstr "Pojavljuje se kada se URL grupe podijeli na društvenim mrežama"

#: frontend/src/pages/JobDetail.vue:51
msgid "Apply"
msgstr "Primjeni"

#. Label of the apply_gst (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply GST for India"
msgstr "Primijeni GST za Indiju"

#. Label of the apply_rounding (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply Rounding on Equivalent"
msgstr "Primijeni zaokruživanje na ekvivalent"

#: frontend/src/components/Modals/JobApplicationModal.vue:6
msgid "Apply for this job"
msgstr "Prijavi se za ovaj posao"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Approved"
msgstr "Odobreno"

#: frontend/src/components/Apps.vue:13
msgid "Apps"
msgstr "Aplikacije"

#: frontend/src/pages/Batches.vue:283
msgid "Archived"
msgstr "Arhivirano"

#: frontend/src/components/UpcomingEvaluations.vue:172
msgid "Are you sure you want to cancel this evaluation? This action cannot be undone."
msgstr "Jeste li sigurni da želite otkazati ovo ocjenjivanje? Ova radnja se ne može poništiti."

#: frontend/src/components/UserDropdown.vue:175
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Jeste li sigurni da se želite prijaviti na svoju Frappe Cloud Nadzornu Tablu?"

#. Label of the assessment_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the assessment (Table) field in DocType 'LMS Batch'
#: frontend/src/components/Modals/AssessmentModal.vue:27
#: frontend/src/components/Modals/BatchStudentProgress.vue:41
#: lms/lms/doctype/lms_batch/lms_batch.json lms/templates/assessments.html:11
msgid "Assessment"
msgstr "Procjena"

#. Label of the assessment_name (Dynamic Link) field in DocType 'LMS
#. Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Name"
msgstr "Naziv Procjene"

#. Label of the assessment_type (Link) field in DocType 'LMS Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Type"
msgstr "Tip Procjene"

#: frontend/src/components/Modals/AssessmentModal.vue:91
msgid "Assessment added successfully"
msgstr "Procjena je uspješno dodana"

#: lms/lms/doctype/lms_batch/lms_batch.py:80
msgid "Assessment {0} has already been added to this batch."
msgstr "Procjena {0} je već dodana ovoj grupi."

#. Label of the show_assessments (Check) field in DocType 'LMS Settings'
#: frontend/src/components/AppSidebar.vue:580
#: frontend/src/components/Assessments.vue:5
#: frontend/src/components/BatchStudents.vue:32
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/templates/assessments.html:3
msgid "Assessments"
msgstr "Procjene"

#: lms/lms/doctype/lms_badge/lms_badge.js:50
msgid "Assign"
msgstr "Dodijeli"

#. Label of the section_break_16 (Section Break) field in DocType 'Course
#. Lesson'
#. Label of the assignment (Link) field in DocType 'LMS Assignment Submission'
#: frontend/src/pages/AssignmentSubmissionList.vue:12
#: frontend/src/utils/assignment.js:24
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/templates/assignment.html:3
msgid "Assignment"
msgstr "Dodjela"

#. Label of the assignment_attachment (Attach) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Attachment"
msgstr "Prilog Zadatku"

#: frontend/src/pages/AssignmentSubmissionList.vue:223
msgid "Assignment Submissions"
msgstr "Podnošenje Zadataka"

#. Label of the assignment_title (Data) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Title"
msgstr "Naziv Zadatka"

#: frontend/src/components/Modals/AssignmentForm.vue:125
msgid "Assignment created successfully"
msgstr "Zadatak je uspješno kreiran"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:24
msgid "Assignment for Lesson {0} by {1} already exists."
msgstr "Zadatak za Lekciju {0} od {1} već postoji."

#: frontend/src/components/Assignment.vue:356
msgid "Assignment submitted successfully"
msgstr "Zadatak uspješno predan"

#: frontend/src/components/Modals/AssignmentForm.vue:138
msgid "Assignment updated successfully"
msgstr "Zadatak je uspješno ažuriran"

#. Description of the 'Question' (Small Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Assignment will appear at the bottom of the lesson."
msgstr "Zadatak će se pojaviti na dnu lekcije."

#: frontend/src/components/AppSidebar.vue:584
#: frontend/src/pages/Assignments.vue:211 lms/www/lms.py:273
msgid "Assignments"
msgstr "Dodjele"

#: lms/lms/doctype/lms_question/lms_question.py:43
msgid "At least one option must be correct for this question."
msgstr "Barem jedna opcija mora biti tačna za ovo pitanje."

#: frontend/src/components/Modals/LiveClassAttendance.vue:5
msgid "Attendance for Class - {0}"
msgstr "Prisustvo Nastavi - {0}"

#: frontend/src/components/Modals/LiveClassAttendance.vue:24
msgid "Attended for"
msgstr "Prisustvo za"

#. Label of the attendees (Int) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Attendees"
msgstr "Učesnici"

#. Label of the attire (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Attire Preference"
msgstr "Preferencija Odjeće"

#: frontend/src/pages/ProfileEvaluator.vue:137
msgid "Authorize Google Calendar Access"
msgstr "Autoriziraj pristup Google kalendaru"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Auto Assign"
msgstr "Automatski Dodjeli"

#. Label of the auto_recording (Select) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:73
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Auto Recording"
msgstr "Automatsko Snimanje"

#: frontend/src/pages/ProfileEvaluator.vue:224
msgid "Availability updated successfully"
msgstr "Dostupnost je uspješno ažurirana"

#: frontend/src/components/BatchFeedback.vue:43
msgid "Average Feedback Received"
msgstr "Prosječan broj primljenih povratnih informacija"

#: frontend/src/components/CourseCard.vue:55
#: frontend/src/pages/CourseDetail.vue:20
msgid "Average Rating"
msgstr "Prosječna Ocjena"

#: frontend/src/pages/Lesson.vue:166
msgid "Back to Course"
msgstr "Povratak na Kurs"

#. Label of the badge (Link) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge"
msgstr "Značka"

#. Label of the badge_description (Small Text) field in DocType 'LMS Badge
#. Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Description"
msgstr "Opis Značke"

#. Label of the badge_image (Attach) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Image"
msgstr "Slika Značke"

#. Label of the batch (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the batch (Link) field in DocType 'LMS Batch Feedback'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate Request'
#. Label of the batch_name (Link) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:32
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Batch"
msgstr "Grupa"

#. Label of the batch_confirmation_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Confirmation Template"
msgstr "Šablon Potvrde Grupe"

#. Name of a DocType
#: lms/lms/doctype/batch_course/batch_course.json
msgid "Batch Course"
msgstr "Grupni Kurs"

#. Label of the section_break_5 (Section Break) field in DocType 'LMS Batch
#. Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Batch Description"
msgstr "Opis Grupe"

#. Label of the batch_details (Text Editor) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:126
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/templates/emails/batch_confirmation.html:26
msgid "Batch Details"
msgstr "Detalji Grupe"

#. Label of the batch_details_raw (HTML Editor) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Batch Details Raw"
msgstr "Detalji Grupe"

#: frontend/src/components/Modals/EmailTemplateModal.vue:28
msgid "Batch Enrollment Confirmation"
msgstr "Potvrda Grupnog Upisa"

#. Name of a role
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Batch Evaluator"
msgstr "Ocjenjivač Grupe"

#. Label of the batch_name (Link) field in DocType 'LMS Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Batch Name"
msgstr "Naziv Grupe"

#. Label of the batch_old (Link) field in DocType 'Exercise Latest Submission'
#. Label of the batch_old (Link) field in DocType 'Exercise Submission'
#. Label of the batch_old (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Batch Old"
msgstr "Stara Grupa"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Batch
#. Old'
#. Label of the section_break_szgq (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Settings"
msgstr "Postavke Grupe"

#: lms/templates/emails/batch_confirmation.html:11
msgid "Batch Start Date:"
msgstr "Datum Početka Grupe:"

#: frontend/src/components/BatchStudents.vue:40
msgid "Batch Summary"
msgstr "Sažetak Grupe"

#. Label of the batch_title (Data) field in DocType 'LMS Certificate'
#. Label of the batch_title (Data) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Batch Title"
msgstr "Naziv Grupe"

#: lms/lms/doctype/lms_batch/lms_batch.py:41
msgid "Batch end date cannot be before the batch start date"
msgstr "Datum završetka grupe ne može biti prije datuma početka grupe"

#: lms/lms/api.py:245
msgid "Batch has already started."
msgstr "Grupa je već počela."

#: lms/lms/api.py:240
msgid "Batch is sold out."
msgstr "Grupa je rasprodata."

#: lms/lms/doctype/lms_batch/lms_batch.py:46
msgid "Batch start time cannot be greater than or equal to end time."
msgstr "Vrijeme početka grupe ne može biti kasnije ili isto vremenu završetka."

#: lms/templates/emails/batch_start_reminder.html:10
msgid "Batch:"
msgstr "Grupa:"

#. Label of the batches (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batches.vue:299 frontend/src/pages/Batches.vue:306
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:122
msgid "Batches"
msgstr "Grupe"

#. Label of the begin_date (Date) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Begin Date"
msgstr "Datum Početka"

#: lms/templates/emails/batch_confirmation.html:33
#: lms/templates/emails/batch_start_reminder.html:31
#: lms/templates/emails/certification.html:20
#: lms/templates/emails/live_class_reminder.html:28
msgid "Best Regards"
msgstr "Srdačan Pozdrav"

#. Label of the billing_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: frontend/src/pages/Billing.vue:8 frontend/src/pages/Billing.vue:357
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Details"
msgstr "Detalji Fakture"

#. Label of the billing_name (Data) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:70
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Name"
msgstr "Faktura Adresa"

#: frontend/src/components/Modals/EditProfile.vue:75
msgid "Bio"
msgstr "Bio"

#. Label of the body (Markdown Editor) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Body"
msgstr "Sadržaj"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Both Individual and Team Work"
msgstr "I Individualni i Timski Rad"

#. Label of the branch (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Branch"
msgstr "Podružnica"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:23
msgid "Business Owner"
msgstr "Poslovni Vlasnik"

#: frontend/src/components/CourseCardOverlay.vue:48
msgid "Buy this course"
msgstr "Kupi ovaj kurs"

#: lms/templates/emails/lms_message.html:11
msgid "By"
msgstr "Od"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "CGPA/4"
msgstr "CGPA/4"

#: frontend/src/components/UpcomingEvaluations.vue:57
#: frontend/src/components/UpcomingEvaluations.vue:177
msgid "Cancel"
msgstr "Otkaži"

#: frontend/src/components/UpcomingEvaluations.vue:171
msgid "Cancel this evaluation?"
msgstr "Otkaži ovo ocjenjivanje?"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Cancelled"
msgstr "Otkazano"

#. Label of the carrer_preference_details (Section Break) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Career Preference Details"
msgstr "Detalji Preferencije u Karijeri"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Casual Wear"
msgstr "Ležerna Odjeća"

#. Label of the category (Link) field in DocType 'LMS Batch'
#. Label of the category (Data) field in DocType 'LMS Category'
#. Label of the category (Link) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:192 frontend/src/pages/Batches.vue:55
#: frontend/src/pages/CertifiedParticipants.vue:35
#: frontend/src/pages/CourseForm.vue:36 frontend/src/pages/Courses.vue:51
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json lms/templates/signup-form.html:22
msgid "Category"
msgstr "Kategorija"

#: frontend/src/components/Settings/Categories.vue:39
msgid "Category Name"
msgstr "Naziv kategorije"

#: frontend/src/components/Settings/Categories.vue:133
msgid "Category added successfully"
msgstr "Kategorija je uspješno dodana"

#: frontend/src/components/Settings/Categories.vue:193
msgid "Category deleted successfully"
msgstr "Kategorija je uspješno izbrisana"

#: frontend/src/components/Settings/Categories.vue:173
msgid "Category updated successfully"
msgstr "Kategorija je uspješno ažurirana"

#. Label of the certificate (Link) field in DocType 'LMS Enrollment'
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certificate"
msgstr "Certifikat"

#. Label of the certification_template (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certificate Email Template"
msgstr "Šablon e-pošte Certifikata"

#: lms/templates/emails/certification.html:13
msgid "Certificate Link"
msgstr "Veza Certifikata"

#: frontend/src/components/CourseCardOverlay.vue:130
msgid "Certificate of Completion"
msgstr "Certifikat o Završetku"

#: frontend/src/components/Modals/Event.vue:311
msgid "Certificate saved successfully"
msgstr "Certifikat je uspješno spremljen"

#: frontend/src/pages/ProfileCertificates.vue:4
msgid "Certificates"
msgstr "Certifikati"

#: frontend/src/components/Modals/BulkCertificates.vue:120
msgid "Certificates generated successfully"
msgstr "Certifikati su uspješno generirani"

#. Label of the certification (Table) field in DocType 'User'
#. Name of a DocType
#. Label of the certification (Check) field in DocType 'LMS Batch'
#. Label of the certification_section (Section Break) field in DocType 'LMS
#. Enrollment'
#. Label of a Card Break in the LMS Workspace
#. Label of a Link in the LMS Workspace
#: frontend/src/components/AppSidebar.vue:588
#: frontend/src/components/CourseCard.vue:115
#: frontend/src/components/Modals/Event.vue:372
#: frontend/src/pages/BatchForm.vue:62 frontend/src/pages/Batches.vue:38
#: frontend/src/pages/CourseCertification.vue:10
#: frontend/src/pages/CourseCertification.vue:135
#: frontend/src/pages/Courses.vue:34 lms/fixtures/custom_field.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certification"
msgstr "Certifikacija"

#. Label of the certification_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Certification Details"
msgstr "Detalji Certifikacije"

#. Label of the certification_name (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Certification Name"
msgstr "Naziv Certifikacije"

#: frontend/src/components/BatchStudents.vue:17
msgid "Certified"
msgstr "Certificirano"

#. Label of the certified_members (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/CertifiedParticipants.vue:182
#: frontend/src/pages/CertifiedParticipants.vue:189
#: frontend/src/pages/Statistics.vue:40
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certified Members"
msgstr "Certificirani Članovi"

#. Label of the certified_participants (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:302
msgid "Certified Participants"
msgstr "Certificirani Učesnici"

#: lms/templates/assignment.html:13
msgid "Change"
msgstr "Promjeni"

#: frontend/src/components/Assignment.vue:342
msgid "Changes saved successfully"
msgstr "Promjene su uspješno spremljene"

#. Label of the chapter (Link) field in DocType 'Chapter Reference'
#. Label of the chapter (Link) field in DocType 'LMS Course Progress'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/chapter_reference/chapter_reference.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/workspace/lms/lms.json
msgid "Chapter"
msgstr "Poglavlje"

#. Name of a DocType
#: lms/lms/doctype/chapter_reference/chapter_reference.json
msgid "Chapter Reference"
msgstr "Referenca Poglavlja"

#: frontend/src/components/Modals/ChapterModal.vue:154
msgid "Chapter added successfully"
msgstr "Poglavlje je uspješno dodano"

#: frontend/src/components/CourseOutline.vue:299
msgid "Chapter deleted successfully"
msgstr "Poglavlje uspješno izbrisano"

#: frontend/src/components/Modals/ChapterModal.vue:196
msgid "Chapter updated successfully"
msgstr "Poglavlje je uspješno ažurirano"

#. Label of the chapters (Table) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Chapters"
msgstr "Poglavlja"

#: frontend/src/components/Quiz.vue:219 lms/templates/quiz/quiz.html:120
msgid "Check"
msgstr "Provjeri"

#: lms/templates/emails/mention_template.html:10
msgid "Check Discussion"
msgstr "Provjerite Diskusiju"

#: frontend/src/components/Modals/AssignmentForm.vue:55
msgid "Check Submissions"
msgstr "Provjeri Podnošenja"

#: lms/templates/certificates_section.html:24
msgid "Check out the {0} to know more about certification."
msgstr "Pogledaj {0} da biste saznali više o certifikaciji."

#: frontend/src/components/NoPermission.vue:19
msgid "Checkout Courses"
msgstr "Pogledaj Kurseve"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Choices"
msgstr "Izbori"

#: frontend/src/components/Quiz.vue:634 lms/templates/quiz/quiz.html:53
msgid "Choose all answers that apply"
msgstr "Odaberi sve odgovore koji odgvaraju"

#: frontend/src/components/Modals/Question.vue:19
msgid "Choose an existing question"
msgstr "Odaberi postojeće pitanje"

#: frontend/src/components/Controls/IconPicker.vue:27
msgid "Choose an icon"
msgstr "Odaberi ikonu"

#: frontend/src/components/Quiz.vue:635 lms/templates/quiz/quiz.html:53
msgid "Choose one answer"
msgstr "Odaberi jedan odgovor"

#. Label of the city (Data) field in DocType 'User'
#. Label of the location (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/Billing.vue:81 frontend/src/pages/JobForm.vue:34
#: lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "City"
msgstr "Grad"

#: lms/templates/emails/live_class_reminder.html:10
msgid "Class:"
msgstr "Razred:"

#: frontend/src/components/Controls/Link.vue:49
msgid "Clear"
msgstr "Očisti"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Clearly Defined Role"
msgstr "Jasno Definisana Uloga"

#: frontend/src/components/BatchFeedback.vue:10
msgid "Click here"
msgstr "Klikni ovdje"

#. Label of the client_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the client_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:36
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client ID"
msgstr "ID klijenta"

#. Label of the client_secret (Password) field in DocType 'LMS Zoom Settings'
#. Label of the client_secret (Password) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:49
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client Secret"
msgstr "Tajna klijenta"

#: frontend/src/components/Settings/Categories.vue:27
#: frontend/src/components/Settings/Evaluators.vue:24
#: frontend/src/components/Settings/Members.vue:24
msgid "Close"
msgstr "Zatvori"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Closed"
msgstr "Zatvoreno"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Cloud"
msgstr "Cloud"

#. Label of the code (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Code"
msgstr "Kod"

#. Name of a DocType
#. Label of the cohort (Link) field in DocType 'Cohort Join Request'
#. Label of the cohort (Link) field in DocType 'Cohort Mentor'
#. Label of the cohort (Link) field in DocType 'Cohort Staff'
#. Label of the cohort (Link) field in DocType 'Cohort Subgroup'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the cohort (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Cohort"
msgstr "Grupa"

#. Name of a DocType
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Cohort Join Request"
msgstr "Zahtjev za pridruživanje Grupi"

#. Name of a DocType
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Cohort Mentor"
msgstr "Mentor Grupe"

#. Name of a DocType
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Cohort Staff"
msgstr "Osoblje Grupe"

#. Name of a DocType
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Cohort Subgroup"
msgstr "Podgrupa Grupe"

#. Name of a DocType
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Cohort Web Page"
msgstr "Web stranica Grupe"

#. Label of the collaboration (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Collaboration Preference"
msgstr "Preference Saradnje"

#: frontend/src/components/AppSidebar.vue:142
msgid "Collapse"
msgstr "Sklopi"

#. Label of the college (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "College Name"
msgstr "Ime Koledža"

#. Label of the color (Color) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Color"
msgstr "Boja"

#: frontend/src/pages/BatchForm.vue:293 frontend/src/pages/CourseForm.vue:283
msgid "Comma separated keywords for SEO"
msgstr "Ključne riječi odvojene zarezom za SEO"

#. Label of the comments (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the comments (Small Text) field in DocType 'Exercise Submission'
#. Label of the comments (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the comments (Small Text) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Assignment.vue:164
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Comments"
msgstr "Komentari"

#: frontend/src/components/Assignment.vue:142
msgid "Comments by Evaluator"
msgstr "Komentari Ocjenjivača"

#. Description of the 'Meta Keywords' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Common keywords that will be used for all pages"
msgstr "Uobičajene ključne riječi koje će se koristiti za sve stranice"

#. Label of the company (Data) field in DocType 'LMS Job Application'
#. Label of the company (Data) field in DocType 'Work Experience'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Company"
msgstr "Kompanija"

#. Label of the section_break_6 (Section Break) field in DocType 'Job
#. Opportunity'
#: frontend/src/pages/JobForm.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Details"
msgstr "Detalji Kompanije"

#. Label of the company_email_address (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:75
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Email Address"
msgstr "Adresa e-pošte Kompanije"

#. Label of the company_logo (Attach Image) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:80
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Logo"
msgstr "Logo Kompanije"

#. Label of the company_name (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:62
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Name"
msgstr "Naziv Kompanije"

#. Label of the company_type (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Company Type"
msgstr "Tip Kompanije"

#. Label of the company_website (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:68
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Website"
msgstr "Web stranica Kompanije"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: frontend/src/components/Modals/BatchStudentProgress.vue:24
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/widgets/CourseCard.html:75 lms/templates/reviews.html:48
msgid "Complete"
msgstr "Završeno"

#: lms/templates/emails/lms_invite_request_approved.html:7
msgid "Complete Sign Up"
msgstr "Završi Registraciju"

#: lms/templates/emails/payment_reminder.html:15
msgid "Complete Your Enrollment"
msgstr "Završi Upis"

#: lms/lms/doctype/lms_payment/lms_payment.py:73
msgid "Complete Your Enrollment - Don't miss out!"
msgstr "Završi upis - ne propusti!"

#: frontend/src/components/VideoBlock.vue:140
msgid "Complete the upcoming quiz to continue watching the video. The quiz will open in {0} {1}."
msgstr "Završi predstojeći kviz da biste nastaviš gledati video. Kviz će se otvoriti za {0} {1}."

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/widgets/CourseCard.html:78
msgid "Completed"
msgstr "Završeno"

#. Label of the enable_certification (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:232
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Completion Certificate"
msgstr "Završni Certifikat"

#. Label of the condition (Code) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Condition"
msgstr "Uslov"

#: lms/lms/doctype/lms_badge/lms_badge.py:16
msgid "Condition must be in valid JSON format."
msgstr "Uvjet mora biti u važećem JSON formatu."

#: lms/lms/doctype/lms_badge/lms_badge.py:21
msgid "Condition must be valid python code."
msgstr "Uslov mora biti važeći Python kod."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:7
msgid "Conduct Evaluation"
msgstr "Provedi Ocjenjivanje"

#: frontend/src/pages/BatchForm.vue:141
msgid "Configurations"
msgstr "Konfiguracije"

#: frontend/src/components/UserDropdown.vue:180
msgid "Confirm"
msgstr "Potvrdi"

#. Label of the confirmation_email_sent (Check) field in DocType 'LMS Batch
#. Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "Confirmation Email Sent"
msgstr "E-pošta potvrde poslana"

#. Label of the confirmation_email_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Confirmation Email Template"
msgstr "Šablon e-pošte za potvrdu"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:29
msgid "Congratulations on getting certified!"
msgstr "Čestitamo na certificiranju!"

#: frontend/src/components/CourseCardOverlay.vue:57
#: frontend/src/pages/Lesson.vue:47
msgid "Contact the Administrator to enroll for this course."
msgstr "Kontaktirajte administratora da se upišete na ovaj kurs."

#. Label of the content (Text) field in DocType 'Course Lesson'
#. Label of the content (Rating) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/Modals/EmailTemplateModal.vue:44
#: frontend/src/components/Modals/EmailTemplateModal.vue:57
#: frontend/src/pages/LessonForm.vue:62
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Content"
msgstr "Sadržaj"

#: frontend/src/components/CourseCardOverlay.vue:30
msgid "Continue Learning"
msgstr "Nastavi sa Učenjem"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:178
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Contract"
msgstr "Ugovor"

#: lms/lms/utils.py:442
msgid "Cookie Policy"
msgstr "Politika Kolačića"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Corporate Organization"
msgstr "Korporativna Organizacija"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:179
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Correct"
msgstr "Tačno"

#: frontend/src/components/Modals/Question.vue:76
msgid "Correct Answer"
msgstr "Tačan Odgovor"

#. Label of the country (Link) field in DocType 'User'
#. Label of the country (Link) field in DocType 'Job Opportunity'
#. Label of the country (Link) field in DocType 'Payment Country'
#: frontend/src/pages/Billing.vue:92 frontend/src/pages/JobForm.vue:40
#: frontend/src/pages/Jobs.vue:57 lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Country"
msgstr "Zemlja"

#. Label of the course (Link) field in DocType 'Batch Course'
#. Label of the course (Link) field in DocType 'Cohort'
#. Label of the course (Link) field in DocType 'Cohort Mentor'
#. Label of the course (Link) field in DocType 'Cohort Staff'
#. Label of the course (Link) field in DocType 'Cohort Subgroup'
#. Label of the course (Link) field in DocType 'Course Chapter'
#. Label of the course (Link) field in DocType 'Course Lesson'
#. Label of the course (Link) field in DocType 'Exercise Latest Submission'
#. Label of the course (Link) field in DocType 'Exercise Submission'
#. Label of the course (Link) field in DocType 'LMS Assignment Submission'
#. Label of the course (Link) field in DocType 'LMS Batch Old'
#. Label of the course (Link) field in DocType 'LMS Certificate'
#. Label of the course (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the course (Link) field in DocType 'LMS Certificate Request'
#. Label of the course (Link) field in DocType 'LMS Course Interest'
#. Label of the course (Link) field in DocType 'LMS Course Mentor Mapping'
#. Label of the course (Link) field in DocType 'LMS Course Progress'
#. Label of the course (Link) field in DocType 'LMS Course Review'
#. Label of the course (Link) field in DocType 'LMS Enrollment'
#. Label of the course (Link) field in DocType 'LMS Exercise'
#. Label of the course (Link) field in DocType 'LMS Mentor Request'
#. Label of the course (Link) field in DocType 'LMS Program Course'
#. Label of the course (Link) field in DocType 'LMS Quiz'
#. Label of the course (Link) field in DocType 'LMS Quiz Submission'
#. Label of the course (Link) field in DocType 'Related Courses'
#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/BatchCourseModal.vue:20
#: frontend/src/components/Modals/BulkCertificates.vue:38
#: frontend/src/components/Modals/EvaluationModal.vue:20
#: frontend/src/components/Modals/Event.vue:24
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/related_courses/related_courses.json
#: lms/lms/report/course_progress_summary/course_progress_summary.js:9
#: lms/lms/report/course_progress_summary/course_progress_summary.py:51
#: lms/lms/workspace/lms/lms.json
msgid "Course"
msgstr "Kurs"

#. Name of a DocType
#. Label of the chapter (Link) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Chapter"
msgstr "Poglavlje Kursa"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Completed"
msgstr "Kurs je Završen"

#: frontend/src/pages/Statistics.vue:31
msgid "Course Completions"
msgstr "Završeni Kursevi"

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:26
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Course Creator"
msgstr "Kreator Kursa"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Data"
msgstr "Podaci o kursu"

#: frontend/src/pages/CourseForm.vue:181
msgid "Course Description"
msgstr "Opis Kursa"

#: frontend/src/pages/Statistics.vue:22
msgid "Course Enrollments"
msgstr "Upisi na Kurseve"

#. Name of a DocType
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Course Evaluator"
msgstr "Ocjenjivač Kursa"

#: frontend/src/pages/CourseForm.vue:90
msgid "Course Image"
msgstr "Slika Kursa"

#. Name of a DocType
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Course Instructor"
msgstr "Instruktor Kursa"

#. Name of a DocType
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Lesson"
msgstr "Lekcija Kursa"

#: lms/www/lms.py:87
msgid "Course List"
msgstr "Lista Kurseva"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:58
msgid "Course Name"
msgstr "Naziv Kursa"

#: frontend/src/pages/CourseDetail.vue:76 frontend/src/pages/CourseForm.vue:293
msgid "Course Outline"
msgstr "Pregled Kursa"

#. Name of a report
#: lms/lms/report/course_progress_summary/course_progress_summary.json
msgid "Course Progress Summary"
msgstr "Sažetak Napretka Kursa"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Course Settings"
msgstr "Postavke Kursa"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Stats"
msgstr "Statistika Kursa"

#. Label of the title (Data) field in DocType 'Batch Course'
#. Label of the course_title (Data) field in DocType 'Course Chapter'
#. Label of the course_title (Data) field in DocType 'LMS Certificate'
#. Label of the course_title (Data) field in DocType 'LMS Certificate Request'
#. Label of the course_title (Data) field in DocType 'LMS Program Course'
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "Course Title"
msgstr "Naziv Kursa"

#: frontend/src/pages/ProgramForm.vue:234
msgid "Course added to program"
msgstr "Kurs dodat programu"

#: frontend/src/pages/CourseForm.vue:523
msgid "Course created successfully"
msgstr "Kurs je uspješno kreiran"

#: frontend/src/pages/CourseForm.vue:560
msgid "Course deleted successfully"
msgstr "Kurs uspješno izbrisan"

#: frontend/src/pages/ProgramForm.vue:303
msgid "Course moved successfully"
msgstr "Kurs uspješno pomjeren"

#: frontend/src/pages/CourseForm.vue:543
msgid "Course updated successfully"
msgstr "Kurs je uspješno ažuriran"

#: lms/lms/doctype/lms_batch/lms_batch.py:54
#: lms/lms/doctype/lms_program/lms_program.py:19
msgid "Course {0} has already been added to this batch."
msgstr "Kurs {0} je već dodan ovoj grupi."

#. Label of the courses (Table) field in DocType 'LMS Batch'
#. Label of the show_courses (Check) field in DocType 'LMS Settings'
#. Label of the courses (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchCourses.vue:5
#: frontend/src/components/BatchOverlay.vue:37
#: frontend/src/components/BatchStudents.vue:25
#: frontend/src/components/Modals/BatchStudentProgress.vue:91
#: frontend/src/pages/BatchDetail.vue:44
#: frontend/src/pages/CourseCertification.vue:127
#: frontend/src/pages/Courses.vue:331 frontend/src/pages/Courses.vue:338
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Courses"
msgstr "Kursevi"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:28
msgid "Courses Completed"
msgstr "Završeni Kursevi"

#: frontend/src/components/BatchCourses.vue:154
msgid "Courses deleted successfully"
msgstr "Kursevi su uspješno izbrisani"

#. Label of the cover_image (Attach Image) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Cover Image"
msgstr "Naslovna Slika"

#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/Programs.vue:93
msgid "Create"
msgstr "Kreiraj"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.js:7
msgid "Create Certificate"
msgstr "Kreiraj Certifikat"

#: frontend/src/components/Controls/Link.vue:37
#: frontend/src/components/Controls/MultiSelect.vue:66
msgid "Create New"
msgstr "Kreiraj"

#: lms/templates/onboarding_header.html:19
msgid "Create a Course"
msgstr "Kreiraj Kurs"

#: frontend/src/components/Modals/LiveClassModal.vue:5
msgid "Create a Live Class"
msgstr "Kreiraj Razred Uživo"

#: frontend/src/components/AppSidebar.vue:575
msgid "Create a batch"
msgstr "Kreiraj grupu"

#: frontend/src/components/AppSidebar.vue:566
msgid "Create a course"
msgstr "Kreiraj kurs"

#: frontend/src/components/AppSidebar.vue:576
msgid "Create a live class"
msgstr "Kreiraj čas uživo"

#: frontend/src/components/Modals/AssignmentForm.vue:13
msgid "Create an Assignment"
msgstr "Kreiraj Zadatak"

#: frontend/src/components/AppSidebar.vue:500
msgid "Create your first batch"
msgstr "Kreiraj vašu prvu seriju"

#: frontend/src/components/AppSidebar.vue:431
msgid "Create your first course"
msgstr "Kreiraj vaš prvi kurs"

#: frontend/src/components/AppSidebar.vue:478
msgid "Create your first quiz"
msgstr "Kreiraj vašj prvi kviz"

#: frontend/src/pages/Assignments.vue:176 frontend/src/pages/Courses.vue:321
msgid "Created"
msgstr "Kreirano"

#: frontend/src/components/AppSidebar.vue:572
msgid "Creating a batch"
msgstr "Kreiranje grupe u toku"

#: frontend/src/components/AppSidebar.vue:563
msgid "Creating a course"
msgstr "Kreiranje kursa u toku"

#. Label of the currency (Link) field in DocType 'LMS Batch'
#. Label of the currency (Link) field in DocType 'LMS Course'
#. Label of the currency (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:272 frontend/src/pages/CourseForm.vue:262
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Currency"
msgstr "Valuta"

#. Label of the current_lesson (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Current Lesson"
msgstr "Trenutna Lekcija"

#: frontend/src/components/AppSidebar.vue:594
msgid "Custom Certificate Templates"
msgstr "Prilagođeni Šabloni Certifikata"

#. Label of the custom_component (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom HTML"
msgstr "Prilagođeni HTML"

#. Label of the custom_script (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom Script (JavaScript)"
msgstr "Prilagođena Skripta (JavaScript)"

#. Label of the custom_signup_content (HTML Editor) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Custom Signup Content"
msgstr "Prilagođeni sadržaj za Prijavu"

#. Label of the customisations_tab (Tab Break) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Customisations"
msgstr "Prilagodbe"

#. Label of the show_dashboard (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Dashboard"
msgstr "Nadzorna Tabla"

#. Label of the date (Date) field in DocType 'LMS Batch Timetable'
#. Label of the date (Date) field in DocType 'LMS Certificate Evaluation'
#. Label of the date (Date) field in DocType 'LMS Certificate Request'
#. Label of the date (Date) field in DocType 'LMS Live Class'
#. Label of the date (Date) field in DocType 'Scheduled Flow'
#: frontend/src/components/Modals/EvaluationModal.vue:26
#: frontend/src/components/Modals/Event.vue:40
#: frontend/src/components/Modals/LiveClassModal.vue:29
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/templates/quiz/quiz.html:149
msgid "Date"
msgstr "Datum"

#: frontend/src/pages/BatchForm.vue:69
msgid "Date and Time"
msgstr "Datum i Vrijeme"

#: lms/templates/emails/live_class_reminder.html:13
msgid "Date:"
msgstr "Datum:"

#. Label of the day (Select) field in DocType 'Evaluator Schedule'
#. Label of the day (Int) field in DocType 'LMS Batch Timetable'
#. Label of the day (Select) field in DocType 'LMS Certificate Request'
#: frontend/src/pages/ProfileEvaluator.vue:26
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Day"
msgstr "Dan"

#: lms/templates/emails/mentor_request_creation_email.html:2
#: lms/templates/emails/mentor_request_status_update_email.html:2
msgid "Dear"
msgstr "Poštovani"

#: lms/templates/emails/batch_confirmation.html:2
#: lms/templates/emails/batch_start_reminder.html:2
#: lms/templates/emails/certification.html:2
#: lms/templates/emails/live_class_reminder.html:2
msgid "Dear "
msgstr "Poštovani "

#: frontend/src/components/Modals/EmailTemplateModal.vue:66
msgid "Dear {{ member_name }},\\n\\nYou have been enrolled in our upcoming batch {{ batch_name }}.\\n\\nThanks,\\nFrappe Learning"
msgstr "Dragi/a {{ member_name }},\\n\\nUpisani ste u našu sljedeću grupu {{ batch_name }}.\\n\\nHvala,\\nFrappe Učenje"

#. Label of the default_currency (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Default Currency"
msgstr "Standard Valuta"

#. Label of the degree_type (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Degree Type"
msgstr "Tip Stepena"

#: frontend/src/components/CourseOutline.vue:253
#: frontend/src/components/CourseOutline.vue:311
#: frontend/src/pages/CourseForm.vue:573
msgid "Delete"
msgstr "Izbriši"

#: frontend/src/components/CourseOutline.vue:56
msgid "Delete Chapter"
msgstr "Izbriši Poglavlje"

#: frontend/src/pages/CourseForm.vue:567
msgid "Delete Course"
msgstr "Izbriši Kurs"

#: frontend/src/components/CourseOutline.vue:305
msgid "Delete this chapter?"
msgstr "Izbriši ovo Poglavlje?"

#: frontend/src/components/CourseOutline.vue:247
msgid "Delete this lesson?"
msgstr "Izbriši ovu Lekciju?"

#: frontend/src/pages/CourseForm.vue:568
msgid "Deleting the course will also delete all its chapters and lessons. Are you sure you want to delete this course?"
msgstr "Brisanjem kursa izbrisat će se i sva njegova poglavlja i lekcije. Jeste li sigurni da želite izbrisati ovaj kurs?"

#: frontend/src/components/CourseOutline.vue:306
msgid "Deleting this chapter will also delete all its lessons and permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "Brisanjem ovog poglavlja također će se izbrisati sve lekcije u njemu i trajno će se ukloniti iz kursa. Ova radnja se ne može poništiti. Jeste li sigurni da želite nastaviti?"

#: frontend/src/components/CourseOutline.vue:248
msgid "Deleting this lesson will permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "Brisanjem ove lekcije trajno će se ukloniti iz kursa. Ova radnja se ne može poništiti. Jeste li sigurni da želite nastaviti?"

#. Label of the description (Text Editor) field in DocType 'Job Opportunity'
#. Label of the description (Small Text) field in DocType 'Certification'
#. Label of the description (Markdown Editor) field in DocType 'Cohort'
#. Label of the description (Markdown Editor) field in DocType 'Cohort
#. Subgroup'
#. Label of the description (Small Text) field in DocType 'LMS Badge'
#. Label of the description (Small Text) field in DocType 'LMS Batch'
#. Label of the description (Markdown Editor) field in DocType 'LMS Batch Old'
#. Label of the description (Text Editor) field in DocType 'LMS Course'
#. Label of the description (Small Text) field in DocType 'LMS Exercise'
#. Label of the description (Text) field in DocType 'LMS Live Class'
#. Label of the description (Small Text) field in DocType 'Work Experience'
#: frontend/src/components/Modals/LiveClassModal.vue:80
#: frontend/src/pages/JobForm.vue:125
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Description"
msgstr "Opis"

#: frontend/src/components/Apps.vue:51
msgid "Desk"
msgstr "Radni Prostor"

#: frontend/src/components/Modals/DiscussionModal.vue:22
#: frontend/src/pages/BatchForm.vue:14 frontend/src/pages/CourseForm.vue:25
#: frontend/src/pages/QuizForm.vue:42
msgid "Details"
msgstr "Detalji"

#: frontend/src/pages/CourseForm.vue:172
msgid "Disable Self Enrollment"
msgstr "Onemogući Samostalni Upis"

#. Label of the disable_self_learning (Check) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Disable Self Learning"
msgstr "Onemogući Samoučenje"

#. Label of the disable_signup (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Disable Signup"
msgstr "Onemogući Prijavu"

#. Label of the disabled (Check) field in DocType 'Job Opportunity'
#: frontend/src/components/Settings/ZoomSettings.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Disabled"
msgstr "Onemogućeno"

#: frontend/src/components/DiscussionReplies.vue:57
#: lms/lms/widgets/NoPreviewModal.html:25 lms/templates/reviews.html:159
msgid "Discard"
msgstr "Odbaci"

#. Label of the show_discussions (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batch.vue:88
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Discussions"
msgstr "Diskusije"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Document"
msgstr "Dokument"

#: lms/templates/emails/payment_reminder.html:11
msgid "Don’t miss this opportunity to enhance your skills. Click below to complete your enrollment"
msgstr "Ne propusti priliku da unaprediš svoje veštine. Klikni ispod da završiš upis"

#. Label of the dream_companies (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Dream Companies"
msgstr "San Snova Kompanije"

#: lms/lms/doctype/lms_question/lms_question.py:33
msgid "Duplicate options found for this question."
msgstr "Pronađene su duple opcije za ovo pitanje."

#. Label of the duration (Data) field in DocType 'Cohort'
#. Label of the duration (Data) field in DocType 'LMS Batch Timetable'
#. Label of the duration (Int) field in DocType 'LMS Live Class'
#. Label of the duration (Int) field in DocType 'LMS Live Class Participant'
#: frontend/src/components/Modals/LiveClassModal.vue:36
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Duration"
msgstr "Trajanje"

#. Label of the duration (Data) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:63 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Duration (in minutes)"
msgstr "Trajanje (u minutama)"

#: frontend/src/components/Modals/LiveClassModal.vue:32
msgid "Duration of the live class in minutes"
msgstr "Trajanje predavanja uživo u minutama"

#. Label of the email (Link) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "E-Mail"
msgstr "E-pošta"

#. Label of the email (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "E-mail"
msgstr "E-pošta"

#: frontend/src/components/BatchOverlay.vue:116
#: frontend/src/components/CourseCardOverlay.vue:90
#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/JobDetail.vue:34 frontend/src/pages/Lesson.vue:135
#: frontend/src/pages/Profile.vue:36 frontend/src/pages/Programs.vue:53
msgid "Edit"
msgstr "Uredi"

#: frontend/src/components/Modals/AssignmentForm.vue:14
msgid "Edit Assignment"
msgstr "Uredi Zadatak"

#: frontend/src/components/CourseOutline.vue:49
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Edit Chapter"
msgstr "Uredi poglavlje"

#: frontend/src/components/Modals/EmailTemplateModal.vue:8
msgid "Edit Email Template"
msgstr "Uredi šablon e-pošte"

#: frontend/src/pages/Profile.vue:72
msgid "Edit Profile"
msgstr "Uredi Profil"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "Edit Zoom Account"
msgstr "Uredi Zoom Račun"

#: frontend/src/pages/QuizForm.vue:182
msgid "Edit the question"
msgstr "Uredite pitanje"

#. Label of the education (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education"
msgstr "Obrazovanje"

#. Name of a DocType
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Education Detail"
msgstr "Detalj Obrazovanja"

#. Label of the education_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education Details"
msgstr "Detalji Obrazovanja"

#: frontend/src/components/Settings/Evaluators.vue:33
#: frontend/src/components/Settings/Members.vue:33
#: lms/templates/signup-form.html:10
msgid "Email"
msgstr "E-pošta"

#: frontend/src/components/Modals/Event.vue:16
msgid "Email ID"
msgstr "ID e-pošte"

#. Label of the email_sent (Check) field in DocType 'LMS Course Interest'
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "Email Sent"
msgstr "E-pošta poslana"

#: frontend/src/pages/BatchForm.vue:154
msgid "Email Template"
msgstr "Šablon e-pošte"

#: frontend/src/components/Modals/EmailTemplateModal.vue:117
msgid "Email Template created successfully"
msgstr "Šablon e-pošte uspješno je kreiran"

#: frontend/src/components/Modals/EmailTemplateModal.vue:146
msgid "Email Template updated successfully"
msgstr "Šablon e-pošte uspješno ažuriran"

#. Label of the email_templates_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Email Templates"
msgstr "Šablon e-pošte"

#: frontend/src/components/Settings/EmailTemplates.vue:128
#: frontend/src/components/Settings/ZoomSettings.vue:161
msgid "Email Templates deleted successfully"
msgstr "Šabloni e-pošte uspješno izbrisani"

#. Label of the show_emails (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Emails"
msgstr "E-pošta"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:25
msgid "Employee"
msgstr "Personal"

#. Label of the enable (Check) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Enable"
msgstr "Omogući"

#: lms/lms/doctype/lms_settings/lms_settings.py:21
msgid "Enable Google API in Google Settings to send calendar invites for evaluations."
msgstr "Omogućite Google API u Google Postavkama za slanje kalendarskih pozivnica za ocjenjivanje."

#. Label of the enable_learning_paths (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Enable Learning Paths"
msgstr "Omogući Puteve Učenja"

#: frontend/src/components/Modals/ChapterModal.vue:24
msgid "Enable this only if you want to upload a SCORM package as a chapter."
msgstr "Omogućite ovo samo ako želite da učitate SCORM paket kao poglavlje."

#. Label of the enabled (Check) field in DocType 'LMS Badge'
#. Label of the enabled (Check) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:23
#: frontend/src/components/Settings/ZoomSettings.vue:53
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Enabled"
msgstr "Omogućeno"

#: frontend/src/components/Modals/BulkCertificates.vue:53
msgid "Enabling this will publish the certificate on the certified participants page."
msgstr "Ako ovo omogućite, certifikat će biti objavljen na stranici sa certificiranim učesnicima."

#. Label of the end_date (Date) field in DocType 'Cohort'
#. Label of the end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:82 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "End Date"
msgstr "Datum završetka"

#. Label of the end_date (Date) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "End Date (or expected)"
msgstr "Datum Završetka (ili očekivani)"

#. Label of the end_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the end_time (Time) field in DocType 'LMS Batch'
#. Label of the end_time (Time) field in DocType 'LMS Batch Old'
#. Label of the end_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the end_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:98
#: frontend/src/pages/ProfileEvaluator.vue:32
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "End Time"
msgstr "Vrijeme Završetka"

#: frontend/src/components/LiveClass.vue:89
msgid "Ended"
msgstr "Završeno"

#: frontend/src/components/BatchOverlay.vue:103
msgid "Enroll Now"
msgstr "Upišite se sada"

#: frontend/src/pages/Batches.vue:286 frontend/src/pages/Courses.vue:324
msgid "Enrolled"
msgstr "Upisan"

#: frontend/src/components/CourseCard.vue:46
#: frontend/src/components/CourseCardOverlay.vue:112
#: frontend/src/pages/CourseDetail.vue:33
msgid "Enrolled Students"
msgstr "Upisani Studenti"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:93
msgid "Enrollment Confirmation for {0}"
msgstr "Potvrda upisa za {0}"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:20
msgid "Enrollment Count"
msgstr "Broj Upisa"

#: lms/lms/utils.py:1906
msgid "Enrollment Failed"
msgstr "Upis nije uspio"

#. Label of the enrollments (Int) field in DocType 'LMS Course'
#. Label of a chart in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_course/lms_course.json lms/lms/workspace/lms/lms.json
msgid "Enrollments"
msgstr "Upisi"

#: lms/lms/doctype/lms_settings/lms_settings.py:26
msgid "Enter Client Id and Client Secret in Google Settings to send calendar invites for evaluations."
msgstr "Unesite Klijent Id i Klijent Tajnu u Google Postavke da pošaljete kalendarske pozivnice za ocjenjivanje."

#: frontend/src/components/Assignment.vue:113
msgid "Enter a URL"
msgstr "Unesi URL"

#: frontend/src/pages/QuizForm.vue:49
msgid "Enter a title and save the quiz to proceed"
msgstr "Unesi Naziv i spremi kviz da nastavite"

#: lms/templates/quiz/quiz.html:53
msgid "Enter the correct answer"
msgstr "Unesite tačan odgovor"

#: frontend/src/components/Modals/ZoomAccountModal.vue:163
msgid "Error creating Zoom Account"
msgstr "Greška pri kreiranju Zoom računa"

#: frontend/src/components/Modals/EmailTemplateModal.vue:122
msgid "Error creating email template"
msgstr "Greška pri kreiranju šablona e-pošte"

#: lms/lms/doctype/lms_batch/lms_batch.py:204
msgid "Error creating live class. Please try again. {0}"
msgstr "Greška pri kreiranju časa uživo. Pokušaj ponovo. {0}"

#: frontend/src/components/Settings/EmailTemplates.vue:133
#: frontend/src/components/Settings/ZoomSettings.vue:166
msgid "Error deleting email templates"
msgstr "Greška pri brisanju šablona e-pošte"

#: frontend/src/components/Modals/ZoomAccountModal.vue:200
msgid "Error updating Zoom Account"
msgstr "Greška pri ažuriranju Zoom računa"

#: frontend/src/components/Modals/EmailTemplateModal.vue:151
msgid "Error updating email template"
msgstr "Greška pri ažuriranju šablona e-pošte"

#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/Event.vue:365 lms/lms/workspace/lms/lms.json
msgid "Evaluation"
msgstr "Evaluacija"

#. Label of the section_break_6 (Section Break) field in DocType 'LMS
#. Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Evaluation Details"
msgstr "Detalji Ocjenjivanja"

#. Label of the evaluation_end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:115
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Evaluation End Date"
msgstr "Datum Yavršetka Ocjenjivanja"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Evaluation Request"
msgstr "Zahtjev za Ocjenu"

#: lms/lms/doctype/lms_batch/lms_batch.py:87
msgid "Evaluation end date cannot be less than the batch end date."
msgstr "Datum završetka ocjenjivanja ne može biti prije od datuma završetka grupe."

#: frontend/src/components/Modals/Event.vue:256
msgid "Evaluation saved successfully"
msgstr "Evaluacija je uspješno sačuvana"

#. Label of the evaluator (Link) field in DocType 'Batch Course'
#. Label of the evaluator (Link) field in DocType 'Course Evaluator'
#. Label of the evaluator (Link) field in DocType 'LMS Assignment Submission'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Request'
#. Label of the evaluator (Link) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BatchCourseModal.vue:37
#: frontend/src/components/Modals/BulkCertificates.vue:22
#: frontend/src/pages/CourseForm.vue:251 frontend/src/pages/ProfileRoles.vue:32
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/templates/upcoming_evals.html:33
msgid "Evaluator"
msgstr "Ocjenjivač"

#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Evaluator Name"
msgstr "Ime Ocjenjivača"

#. Name of a DocType
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
msgid "Evaluator Schedule"
msgstr "Raspored Ocjenjivača"

#: lms/lms/doctype/lms_course/lms_course.py:67
msgid "Evaluator is required for paid certificates."
msgstr "Ocjenjivač je obavezan za plaćene certifikate."

#. Label of the event (Select) field in DocType 'LMS Badge'
#. Label of the event (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Event"
msgstr "Događaj"

#: frontend/src/pages/BatchForm.vue:109
msgid "Example: IST (+5:30)"
msgstr "Primjer: IST (+5:30)"

#. Label of the exercise (Link) field in DocType 'Exercise Latest Submission'
#. Label of the exercise (Link) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise"
msgstr "Vježba"

#. Name of a DocType
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Exercise Latest Submission"
msgstr "Najnovija Prijava Vježbe "

#. Name of a DocType
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise Submission"
msgstr "Prijava Vježbe"

#. Label of the exercise_title (Data) field in DocType 'Exercise Latest
#. Submission'
#. Label of the exercise_title (Data) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise Title"
msgstr "Naziv Vježbe"

#: frontend/src/components/AppSidebar.vue:142
msgid "Expand"
msgstr "Proširi"

#. Label of the expiration_date (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Expiration Date"
msgstr "Datum Isteka"

#. Label of the expiry_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:33
#: frontend/src/components/Modals/Event.vue:126
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Expiry Date"
msgstr "Datum Isteka Roka"

#. Label of the explanation_1 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_3 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_4 (Small Text) field in DocType 'LMS Question'
#: frontend/src/components/Modals/Question.vue:72
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation"
msgstr "Objašnjenje"

#. Label of the explanation_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation "
msgstr "Objašnjenje "

#: lms/lms/web_template/course_cards/course_cards.html:15
#: lms/lms/web_template/recently_published_courses/recently_published_courses.html:16
msgid "Explore More"
msgstr "Istraži Više"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:357
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Fail"
msgstr "Neuspjeh"

#: lms/lms/doctype/lms_live_class/lms_live_class.py:139
msgid "Failed to fetch attendance data from Zoom for class {0}: {1}"
msgstr "Nije moguće preuzeti podatke o prisustvu sa Zooma za čas {0}: {1}"

#: frontend/src/utils/index.js:622
msgid "Failed to update meta tags {0}"
msgstr "Neuspješno ažuriranje meta oznaka {0}"

#. Label of the featured (Check) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:20
#: frontend/src/pages/CourseForm.vue:167
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Featured"
msgstr "Istaknuto"

#. Label of the feedback (Small Text) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/BatchFeedback.vue:30
#: frontend/src/pages/Batch.vue:146
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Feedback"
msgstr "Povratne Informacije"

#: frontend/src/components/Assignment.vue:64
msgid "Feel free to make edits to your submission if needed."
msgstr "Ako je potrebno, slobodno uredite svoju prijavu."

#. Label of the field_to_check (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Field To Check"
msgstr "Polje za provjeru"

#. Label of the major (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Field of Major/Study"
msgstr "Smjer/Studij"

#. Label of the file_type (Select) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "File Type"
msgstr "Tip Datoteke"

#: frontend/src/components/Modals/EditProfile.vue:59
#: frontend/src/components/Settings/Members.vue:39
msgid "First Name"
msgstr "Ime"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Fixed 9-5"
msgstr "Fiksno 9-5"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Flexible Time"
msgstr "Fleksibilno Vrijeme"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Formal Wear"
msgstr "Formalna Odjeća"

#: lms/lms/widgets/CourseCard.html:114
msgid "Free"
msgstr "Besplatno"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:179
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Freelance"
msgstr "Slobodnjak"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:27
msgid "Freelancer/Just looking"
msgstr "Slobodnjak/Samo Gledam"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "French (e.g. Distinction)"
msgstr "Francuski (npr. Razlikovanje)"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Friday"
msgstr "Petak"

#. Label of the unavailable_from (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:99
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "From"
msgstr "Od"

#. Label of the from_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "From Date"
msgstr "Od Datuma"

#. Label of the full_name (Data) field in DocType 'Course Evaluator'
#. Label of the full_name (Data) field in DocType 'Invite Request'
#. Label of the full_name (Data) field in DocType 'LMS Program Member'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/templates/signup-form.html:5
msgid "Full Name"
msgstr "Puno Ime"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:176
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Full Time"
msgstr "Puno Radno Vrijeme"

#. Name of a DocType
#. Label of the function (Data) field in DocType 'Function'
#. Label of the function (Link) field in DocType 'Preferred Function'
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Function"
msgstr "Funkcija"

#: frontend/src/pages/Billing.vue:43
msgid "GST Amount"
msgstr "Iznos PDV-a"

#: frontend/src/pages/Billing.vue:110
msgid "GST Number"
msgstr "PDV Broj"

#. Label of the gstin (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "GSTIN"
msgstr "GSTIN"

#. Label of the general_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "General"
msgstr "Općenito"

#: frontend/src/components/Modals/BulkCertificates.vue:5
#: frontend/src/pages/Batch.vue:12
msgid "Generate Certificates"
msgstr "Generiši Certifikate"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:15
msgid "Generate Google Meet Link"
msgstr "Generiraj Google Meet Vezu"

#: frontend/src/components/CourseCardOverlay.vue:77
msgid "Get Certificate"
msgstr "Preuzmi Certifikat"

#: frontend/src/components/CertificationLinks.vue:34
#: frontend/src/components/CertificationLinks.vue:50
#: frontend/src/pages/CertifiedParticipants.vue:11
msgid "Get Certified"
msgstr "Certificiraj se"

#: lms/templates/onboarding_header.html:8
msgid "Get Started"
msgstr "Započni"

#. Label of the github (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Github ID"
msgstr "Github ID"

#. Label of the google_meet_link (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Google Meet Link"
msgstr "Google Meet veza"

#. Label of the grade (Data) field in DocType 'Education Detail'
#: frontend/src/components/Assignment.vue:158
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade"
msgstr "Ocjena"

#. Label of the grade_assignment (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Grade Assignment"
msgstr "Dodjela Ocjena"

#. Label of the grade_type (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade Type"
msgstr "Tip Ocjene"

#: frontend/src/components/Assignment.vue:153
msgid "Grading"
msgstr "Ocjenjivanje"

#. Label of the grant_only_once (Check) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Grant only once"
msgstr "Odobri samo jednom"

#: lms/templates/signup-form.html:56
msgid "Have an account? Login"
msgstr "Imaš račun? Prijavi se"

#. Label of the headline (Data) field in DocType 'User'
#: frontend/src/components/Modals/EditProfile.vue:69
#: lms/fixtures/custom_field.json
msgid "Headline"
msgstr "Naslov"

#: lms/lms/widgets/HelloWorld.html:13
msgid "Hello"
msgstr "Zdravo"

#: frontend/src/components/AppSidebar.vue:128
msgid "Help"
msgstr "Pomoć"

#: lms/templates/courses_created.html:15
msgid "Help others learn something new by creating a course."
msgstr "Pomozite drugima da nauče nešto novo kreiranjem kursa."

#: frontend/src/components/BatchFeedback.vue:15
msgid "Help us improve by providing your feedback."
msgstr "Pomozi nam da se poboljšamo tako što ćete nam dati svoje povratne informacije."

#: lms/templates/reviews.html:101
msgid "Help us improve our course material."
msgstr "Pomozi nam da poboljšamo naš materijal za kurs."

#: frontend/src/pages/PersonaForm.vue:16
msgid "Help us understand your needs"
msgstr "Pomozi nam da razumijemo vaše potrebe"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:1
#: lms/templates/emails/certificate_request_notification.html:1
msgid "Hey {0}"
msgstr "Hej {0}"

#: lms/templates/emails/job_report.html:3
msgid "Hey,"
msgstr "Hej,"

#: lms/templates/emails/payment_reminder.html:2
msgid "Hi"
msgstr "Zdravo"

#: lms/templates/emails/lms_course_interest.html:3
msgid "Hi {0},"
msgstr "Zdravo {0},"

#: lms/templates/emails/lms_invite_request_approved.html:3
msgid "Hi,"
msgstr "Zdravo,"

#. Label of the hide_private (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Hide my Private Information from others"
msgstr "Sakrij moje privatne podatke od drugih"

#. Label of the hints (Small Text) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Hints"
msgstr "Nagovještaji"

#. Label of the host (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Host"
msgstr "Domaćin"

#. Label of the current (Check) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "I am currently working here"
msgstr "Trenutno radim ovdje"

#: lms/templates/emails/certification.html:6
msgid "I am delighted to inform you that you have successfully earned your certification for the {0} course. Congratulations!"
msgstr "Sa zadovoljstvom vas mogu obavijestiti da ste uspješno stekli certifikat za {0} kurs. Čestitamo!"

#. Label of the looking_for_job (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "I am looking for a job"
msgstr "Tražim posao"

#: frontend/src/pages/ProfileEvaluator.vue:94
msgid "I am unavailable"
msgstr "Ja sam nedostupan/nedostupna"

#: frontend/src/pages/QuizForm.vue:385
msgid "ID"
msgstr "ID"

#. Label of the icon (Data) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:28
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Icon"
msgstr "Ikona"

#. Label of the user_category (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Identify User Category"
msgstr "Identifikuj Kategoriju Korisnika"

#: frontend/src/components/LessonHelp.vue:11
msgid "If Include in Preview is enabled for a lesson then the lesson will also be accessible to non logged in users."
msgstr "Ako je Uključi u Pregled omogućen za lekciju, lekcija će također biti dostupna korisnicima koji nisu prijavljeni."

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "If you are not any more interested to mentor the course"
msgstr "Ako više niste zainteresovani da vodite kurs"

#: frontend/src/components/Quiz.vue:23
msgid "If you fail to do so, the quiz will be automatically submitted when the timer ends."
msgstr "Ako to ne učinite, kviz će biti automatski dostavljen kada se tajmer završi."

#: lms/templates/emails/payment_reminder.html:19
msgid "If you have any questions or need assistance, feel free to reach out to our support team."
msgstr "Ako imate bilo kakvih pitanja ili vam je potrebna pomoć, slobodno se obratite našem timu za podršku."

#: lms/templates/emails/batch_confirmation.html:29
#: lms/templates/emails/batch_start_reminder.html:27
#: lms/templates/emails/live_class_reminder.html:24
msgid "If you have any questions or require assistance, feel free to contact us."
msgstr "Ako imate bilo kakvih pitanja ili vam je potrebna pomoć, slobodno nas kontaktirajte."

#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Batch'
#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "If you set an amount here, then the USD equivalent setting will not get applied."
msgstr "Ako ovdje postavite iznos, postavka u USD neće biti primijenjena."

#: lms/lms/doctype/lms_quiz/lms_quiz.py:61
msgid "If you want open ended questions then make sure each question in the quiz is of open ended type."
msgstr "Ako želite otvorena pitanja, provjerite je li svako pitanje u kvizu otvorenog tipa."

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Label of the image (Code) field in DocType 'Exercise Latest Submission'
#. Label of the image (Code) field in DocType 'Exercise Submission'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#. Label of the image (Attach Image) field in DocType 'LMS Badge'
#. Label of the image (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Image"
msgstr "Slika"

#: frontend/src/components/Modals/EditCoverImage.vue:58
#: frontend/src/components/UnsplashImageBrowser.vue:52
msgid "Image search powered by"
msgstr "Pretraživanje slika pokreće"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:215
msgid "Image: Corrupted Data Stream"
msgstr "Slika: Oštećen Tok Podataka"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: frontend/src/components/Modals/Event.vue:349
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "In Progress"
msgstr "U Toku"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Inactive"
msgstr "Neaktivan"

#. Label of the include_in_preview (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Include In Preview"
msgstr "Uključi u Pregled"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Incomplete"
msgstr "Nepotpuno"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:184
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Incorrect"
msgstr "Netačno"

#. Label of the index_ (Int) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index"
msgstr "Indeks"

#. Label of the index_label (Data) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index Label"
msgstr "Index Oznaka"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Individual Work"
msgstr "Individualni Rad"

#. Name of a DocType
#. Label of the industry (Data) field in DocType 'Industry'
#. Label of the industry (Link) field in DocType 'Preferred Industry'
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Industry"
msgstr "Industrija"

#. Label of the institution_name (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Institution Name"
msgstr "Naziv Institucije"

#. Label of the instructor (Link) field in DocType 'Cohort'
#. Label of the instructor (Link) field in DocType 'Course Instructor'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Instructor"
msgstr "Instruktor"

#. Label of the instructor_content (Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Content"
msgstr "Instruktor Sadržaj"

#. Label of the instructor_notes (Markdown Editor) field in DocType 'Course
#. Lesson'
#: frontend/src/pages/Lesson.vue:199 frontend/src/pages/LessonForm.vue:42
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Notes"
msgstr "Instruktor Napomene"

#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Batch'
#. Label of the instructors (Rating) field in DocType 'LMS Batch Feedback'
#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:27 frontend/src/pages/CourseForm.vue:44
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Instructors"
msgstr "Instruktori"

#: lms/templates/assignment.html:17
msgid "Instructors Comments"
msgstr "Komentari Instruktora"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Interest"
msgstr "Kamata"

#: frontend/src/components/AppSidebar.vue:555
#: frontend/src/components/AppSidebar.vue:558
msgid "Introduction"
msgstr "Uvod"

#: lms/lms/doctype/invite_request/invite_request.py:83
msgid "Invalid Invite Code."
msgstr "Nevažeći Pozivni Kod"

#: lms/lms/doctype/course_lesson/course_lesson.py:20
msgid "Invalid Quiz ID"
msgstr "Nevažeći ID Kviza"

#: lms/lms/doctype/course_lesson/course_lesson.py:34
msgid "Invalid Quiz ID in content"
msgstr "Nevažeći ID Kviza"

#. Label of the invite_code (Data) field in DocType 'Cohort Subgroup'
#. Label of the invite_code (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Code"
msgstr "Pozivni Kod"

#. Label of the invite_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Email"
msgstr "Pozivna e-pošta"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Invite Only"
msgstr "Samo po Pozivu"

#. Name of a DocType
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Request"
msgstr "Pozivni Zahtjev"

#: frontend/src/components/AppSidebar.vue:489
msgid "Invite your team and students"
msgstr "Pozovi vaš tim i učenike"

#. Label of the is_correct (Check) field in DocType 'LMS Option'
#. Label of the is_correct_1 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_2 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_3 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_4 (Check) field in DocType 'LMS Question'
#. Label of the is_correct (Check) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_option/lms_option.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Is Correct"
msgstr "Tačno"

#. Label of the is_scorm_package (Check) field in DocType 'Course Chapter'
#. Label of the is_scorm_package (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Is SCORM Package"
msgstr "SCORM Paket"

#. Label of the issue_date (Date) field in DocType 'Certification'
#. Label of the issue_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:28
#: frontend/src/components/Modals/Event.vue:121
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Issue Date"
msgstr "Datum Izdavanja"

#: frontend/src/components/AppSidebar.vue:591
msgid "Issue a Certificate"
msgstr "Izdaj Certifikat"

#. Label of the issued_on (Date) field in DocType 'LMS Badge Assignment'
#: frontend/src/pages/CourseCertification.vue:27
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Issued On"
msgstr "Izdato"

#: frontend/src/pages/ProfileAbout.vue:56
#: frontend/src/pages/ProfileCertificates.vue:17
#: lms/templates/certificates_section.html:11
msgid "Issued on"
msgstr "Izdato"

#. Label of the items_in_sidebar_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Items in Sidebar"
msgstr "Stavke na Bočnoj Traci"

#: frontend/src/pages/ProgramForm.vue:277
msgid "Items removed successfully"
msgstr "Stavke su uspješno uklonjene"

#: lms/templates/signup-form.html:6
msgid "Jane Doe"
msgstr "Jane Doe"

#. Label of the job (Link) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job"
msgstr "Posao"

#. Label of the subtitle (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Subtitle"
msgstr "Podnaslov Table Posao"

#. Label of the title (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Title"
msgstr "Naziv Table za Posao"

#: frontend/src/pages/JobForm.vue:14
msgid "Job Details"
msgstr "Detalji Posla"

#: lms/www/lms.py:176
msgid "Job Openings"
msgstr "Otvorena Radna Mjesta"

#. Name of a DocType
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Job Opportunity"
msgstr "Prilika za Posao"

#. Name of a DocType
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Settings"
msgstr "Postavke Posla"

#. Label of the job_title (Data) field in DocType 'Job Opportunity'
#. Label of the job_title (Data) field in DocType 'LMS Job Application'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job Title"
msgstr "Naziv Posla"

#. Label of the jobs (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/JobDetail.vue:10 frontend/src/pages/Jobs.vue:8
#: frontend/src/pages/Jobs.vue:185
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Jobs"
msgstr "Poslovi"

#: frontend/src/components/LiveClass.vue:78
#: lms/templates/upcoming_evals.html:15
msgid "Join"
msgstr "Pridružite se"

#: frontend/src/components/UpcomingEvaluations.vue:90
msgid "Join Call"
msgstr "Pridružite se Pozivu"

#: frontend/src/components/Modals/Event.vue:74
msgid "Join Meeting"
msgstr "Pridružite se Sastanku"

#. Label of the join_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Join URL"
msgstr "URL Pridruživanja"

#. Label of the joined_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Joined At"
msgstr "Pridružio/la se u"

#: frontend/src/components/Modals/LiveClassAttendance.vue:18
msgid "Joined at"
msgstr "Pridružio/la se u"

#. Name of a Workspace
#: lms/lms/workspace/lms/lms.json
msgid "LMS"
msgstr "LMS"

#. Name of a DocType
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "LMS Assessment"
msgstr "Procjena"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "LMS Assignment"
msgstr "Zadatak"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "LMS Assignment Submission"
msgstr "Podnošenje Zadatka"

#. Name of a DocType
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "LMS Badge"
msgstr "Značka"

#. Name of a DocType
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "LMS Badge Assignment"
msgstr "Dodjela Značke"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Batch"
msgstr "Grupa"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "LMS Batch Enrollment"
msgstr "Grupni Upis"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "LMS Batch Feedback"
msgstr "Povratne Informacije Grupe"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "LMS Batch Old"
msgstr "Stara Grupa"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "LMS Batch Timetable"
msgstr "Raspored Grupe"

#. Name of a DocType
#: lms/lms/doctype/lms_category/lms_category.json
msgid "LMS Category"
msgstr "Kategorija"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "LMS Certificate"
msgstr "Certifikat"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "LMS Certificate Evaluation"
msgstr "Evaluacija Certifikata"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "LMS Certificate Request"
msgstr "Zahtjev za Certifikat"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Course"
msgstr "Kurs"

#. Name of a DocType
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "LMS Course Interest"
msgstr "Kurs Interesovanja"

#. Name of a DocType
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "LMS Course Mentor Mapping"
msgstr "Kurs Mentor Mapiranje"

#. Name of a DocType
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "LMS Course Progress"
msgstr "Napredak Kursa"

#. Name of a DocType
#: lms/lms/doctype/lms_course_review/lms_course_review.json
msgid "LMS Course Review"
msgstr "Pregled Kursa"

#. Name of a DocType
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "LMS Enrollment"
msgstr "Upis"

#. Name of a DocType
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "LMS Exercise"
msgstr "Vježba"

#. Name of a DocType
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "LMS Job Application"
msgstr "Prijava za Posao"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "LMS Live Class"
msgstr "Live Class"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "LMS Live Class Participant"
msgstr "Učesnik Live Nastave"

#. Name of a DocType
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "LMS Mentor Request"
msgstr "Zahtjev za Mentora"

#. Name of a DocType
#: lms/lms/doctype/lms_option/lms_option.json
msgid "LMS Option"
msgstr "Opcija"

#. Name of a DocType
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Payment"
msgstr "Plaćanje"

#. Name of a DocType
#: lms/lms/doctype/lms_program/lms_program.json
msgid "LMS Program"
msgstr "Program"

#. Name of a DocType
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "LMS Program Course"
msgstr "Kurs Programa"

#. Name of a DocType
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "LMS Program Member"
msgstr "Član Programa"

#. Name of a DocType
#: lms/lms/doctype/lms_question/lms_question.json
msgid "LMS Question"
msgstr "Pitanje"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "LMS Quiz"
msgstr "Kviz"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "LMS Quiz Question"
msgstr "Pitanje Kviza"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "LMS Quiz Result"
msgstr "Rezultat Kviza"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "LMS Quiz Submission"
msgstr "Podnošenje Kviza"

#. Name of a DocType
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LMS Settings"
msgstr "Postavke"

#. Name of a DocType
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "LMS Sidebar Item"
msgstr "Stavka Bočne Trake"

#. Name of a DocType
#: lms/lms/doctype/lms_source/lms_source.json
msgid "LMS Source"
msgstr "Izvor"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/user_skill/user_skill.json
msgid "LMS Student"
msgstr "Student"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "LMS Timetable Legend"
msgstr "Legenda Vremenske Tabele"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "LMS Timetable Template"
msgstr "Šablon Vremenske Tabele"

#. Name of a DocType
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "LMS Zoom Settings"
msgstr "Zoom Postavke"

#. Label of the label (Data) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Label"
msgstr "Oznaka"

#: frontend/src/components/Modals/EditProfile.vue:64
msgid "Last Name"
msgstr "Prezime"

#. Label of the latest_submission (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Latest Submission"
msgstr "Posljednja Prijava"

#. Label of the launch_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Launch File"
msgstr "Pokreni Datoteku"

#. Label of the left_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Left At"
msgstr "Napustio/la u"

#: frontend/src/components/Modals/LiveClassAttendance.vue:21
msgid "Left at"
msgstr "Napustio/la u"

#. Label of the lesson (Link) field in DocType 'Exercise Latest Submission'
#. Label of the lesson (Link) field in DocType 'Exercise Submission'
#. Label of the lesson (Link) field in DocType 'Lesson Reference'
#. Label of the lesson (Link) field in DocType 'LMS Assignment Submission'
#. Label of the lesson (Link) field in DocType 'LMS Course Progress'
#. Label of the lesson (Link) field in DocType 'LMS Exercise'
#. Label of the lesson (Link) field in DocType 'LMS Quiz'
#. Label of the lesson (Link) field in DocType 'Scheduled Flow'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lesson_reference/lesson_reference.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/lms/workspace/lms/lms.json
msgid "Lesson"
msgstr "Lekcija"

#. Name of a DocType
#: lms/lms/doctype/lesson_reference/lesson_reference.json
msgid "Lesson Reference"
msgstr "Referenca Lekcije"

#. Label of the lesson_title (Data) field in DocType 'Scheduled Flow'
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Lesson Title"
msgstr "Naziv Lekcije"

#: frontend/src/pages/LessonForm.vue:416
msgid "Lesson created successfully"
msgstr "Lekcija je uspješno kreirana"

#: frontend/src/components/CourseOutline.vue:226
msgid "Lesson deleted successfully"
msgstr "Lekcija je uspješno izbrisana"

#: frontend/src/components/CourseOutline.vue:241
msgid "Lesson moved successfully"
msgstr "Lekcija je uspješno premještena"

#: frontend/src/pages/LessonForm.vue:440
msgid "Lesson updated successfully"
msgstr "Lekcija je uspješno ažurirana"

#. Label of the lessons (Table) field in DocType 'Course Chapter'
#. Group in Course Chapter's connections
#. Label of the lessons (Int) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:37
#: frontend/src/components/CourseCardOverlay.vue:105
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Lessons"
msgstr "Lekcije"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:36
msgid "Lessons Completed"
msgstr "Završene Lekcije"

#: lms/templates/onboarding_header.html:11
msgid "Lets start setting up your content on the LMS so that you can reclaim time and focus on growth."
msgstr "Počnimo s postavljanjem vašeg sadržaja tako da možete povratiti vrijeme i fokusirati se na rast."

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Letter Grade (e.g. A, B-)"
msgstr "Slovna Ocjena (npr. A, B-)"

#. Label of the limit_questions_to (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:108 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Limit Questions To"
msgstr "Ograniči pitanja na"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:38
msgid "Limit cannot be greater than or equal to the number of questions in the quiz."
msgstr "Ograničenje ne može biti veće ili jednako broju pitanja u kvizu."

#: frontend/src/pages/ProfileAbout.vue:74
msgid "LinkedIn"
msgstr "LinkedIn"

#. Label of the linkedin (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "LinkedIn ID"
msgstr "LinkedIn ID"

#. Group in Cohort's connections
#. Group in Cohort Subgroup's connections
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Links"
msgstr "Veze"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#: frontend/src/pages/Courses.vue:307 lms/lms/doctype/cohort/cohort.json
msgid "Live"
msgstr "Uživo"

#. Label of the live_class (Link) field in DocType 'LMS Live Class Participant'
#. Label of the show_live_class (Check) field in DocType 'LMS Settings'
#: frontend/src/components/LiveClass.vue:14
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Live Class"
msgstr "Razred Uživo"

#. Label of the livecode_url (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LiveCode URL"
msgstr "URL LiveCode"

#: frontend/src/components/Settings/Members.vue:109
#: frontend/src/pages/Assignments.vue:69 frontend/src/pages/Batches.vue:80
#: frontend/src/pages/CertifiedParticipants.vue:98
#: frontend/src/pages/Courses.vue:75
#: frontend/src/pages/QuizSubmissionList.vue:39
#: frontend/src/pages/Quizzes.vue:55
msgid "Load More"
msgstr "Učitaj Još"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Local"
msgstr "Lokal"

#. Label of the location (Data) field in DocType 'Education Detail'
#. Label of the location (Data) field in DocType 'Work Experience'
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Location"
msgstr "Lokacija"

#. Label of the location_preference (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Location Preference"
msgstr "Preferenca Lokacije"

#: frontend/src/components/NoPermission.vue:28
#: frontend/src/components/QuizBlock.vue:9 frontend/src/pages/Batch.vue:196
#: frontend/src/pages/Lesson.vue:53
msgid "Login"
msgstr "Prijava"

#: frontend/src/components/UserDropdown.vue:174
msgid "Login to Frappe Cloud?"
msgstr "Prijavi se na Frappe Cloud?"

#: frontend/src/pages/JobDetail.vue:63
msgid "Login to apply"
msgstr "Prijavite se kako biste se prijavili"

#: lms/templates/emails/payment_reminder.html:23
msgid "Looking forward to seeing you enrolled!"
msgstr "Radujemo se vašem upisu!"

#. Label of the default_home (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Make LMS the default home"
msgstr "Postavi LMS kao Početnu Web Stranicu"

#: frontend/src/components/Modals/AnnouncementModal.vue:5
#: frontend/src/pages/Batch.vue:16
msgid "Make an Announcement"
msgstr "Objavi"

#: frontend/src/pages/Billing.vue:123
msgid "Make sure to enter the correct billing name as the same will be used in your invoice."
msgstr "Unesi ispravanu faktura adresu jer će se koristiti u vašoj fakturi."

#: frontend/src/components/BatchOverlay.vue:69
msgid "Manage Batch"
msgstr "Upravljaj Grupom"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Manager"
msgstr "Upravitelj"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:24
msgid "Manager (Sales/Marketing/Customer)"
msgstr "Upravitelj (Prodaja/Marketing/Klijent)"

#. Label of the manifest_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Manifest File"
msgstr "Datoteka Manifesta"

#: frontend/src/components/Quiz.vue:110
msgid "Mark"
msgstr "Označi"

#: frontend/src/pages/Notifications.vue:12
msgid "Mark all as read"
msgstr "Označi sve kao pročitano"

#. Label of the marks (Int) field in DocType 'LMS Quiz Question'
#. Label of the marks (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Modals/Question.vue:40
#: frontend/src/components/Modals/Question.vue:103
#: frontend/src/components/Quiz.vue:110 frontend/src/pages/QuizForm.vue:395
#: frontend/src/pages/QuizSubmission.vue:64
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:59
msgid "Marks"
msgstr "Bodovi"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:41
msgid "Marks for question number {0} cannot be greater than the marks allotted for that question."
msgstr "Bodovi za pitanje broj {0} ne mogu biti veći od bodova dodijeljenih za to pitanje."

#. Label of the marks_out_of (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/pages/QuizSubmission.vue:67
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Marks out of"
msgstr "Bodova od mogućih"

#. Label of the max_attempts (Int) field in DocType 'LMS Quiz'
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Max Attempts"
msgstr "Maksimalnih Pokušaja"

#: frontend/src/pages/QuizForm.vue:58
msgid "Maximum Attempts"
msgstr "Maksimalnih Pokušaja"

#. Label of the medium (Select) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:187
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Medium"
msgstr "Srednje"

#. Label of the medium (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Medium ID"
msgstr "Medium ID"

#: lms/templates/emails/batch_confirmation.html:16
#: lms/templates/emails/batch_start_reminder.html:19
msgid "Medium:"
msgstr "Srednje:"

#. Label of the meeting_id (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Meeting ID"
msgstr "ID Sastanka"

#. Label of the member (Link) field in DocType 'Exercise Latest Submission'
#. Label of the member (Link) field in DocType 'Exercise Submission'
#. Label of the member (Link) field in DocType 'LMS Assignment Submission'
#. Label of the member (Link) field in DocType 'LMS Badge Assignment'
#. Label of the member (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the member (Link) field in DocType 'LMS Batch Feedback'
#. Label of the member (Link) field in DocType 'LMS Certificate'
#. Label of the member (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the member (Link) field in DocType 'LMS Certificate Request'
#. Label of the member (Link) field in DocType 'LMS Course Progress'
#. Label of the member (Link) field in DocType 'LMS Enrollment'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#. Label of the member (Link) field in DocType 'LMS Live Class Participant'
#. Label of the member (Link) field in DocType 'LMS Mentor Request'
#. Label of the member (Link) field in DocType 'LMS Payment'
#. Label of the member (Link) field in DocType 'LMS Program Member'
#. Label of the member (Link) field in DocType 'LMS Quiz Submission'
#. Label of the member (Link) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/LiveClassAttendance.vue:14
#: frontend/src/components/Modals/ZoomAccountModal.vue:42
#: frontend/src/components/Settings/ZoomSettings.vue:178
#: frontend/src/pages/AssignmentSubmissionList.vue:14
#: frontend/src/pages/QuizSubmission.vue:31
#: frontend/src/pages/QuizSubmissionList.vue:91
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:64
msgid "Member"
msgstr "Član"

#. Label of the member_cohort (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Cohort"
msgstr "Grupa Člana"

#. Label of the member_email (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Email"
msgstr "E-pošta Člana"

#. Label of the member_image (Attach Image) field in DocType 'LMS Batch
#. Feedback'
#. Label of the member_image (Attach Image) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Member Image"
msgstr "Slika Člana"

#. Label of the member_name (Data) field in DocType 'LMS Assignment Submission'
#. Label of the member_name (Data) field in DocType 'LMS Badge Assignment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Feedback'
#. Label of the member_name (Data) field in DocType 'LMS Certificate'
#. Label of the member_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the member_name (Data) field in DocType 'LMS Certificate Request'
#. Label of the member_name (Data) field in DocType 'LMS Course Progress'
#. Label of the member_name (Data) field in DocType 'LMS Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_name (Data) field in DocType 'LMS Mentor Request'
#. Label of the member_name (Data) field in DocType 'LMS Quiz Submission'
#. Label of the member_name (Data) field in DocType 'LMS Zoom Settings'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:71
msgid "Member Name"
msgstr "Ime Člana"

#. Label of the member_subgroup (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Subgroup"
msgstr "Podgrupa Člana"

#. Label of the member_type (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Member Type"
msgstr "Tip Člana"

#. Label of the member_username (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Member Username"
msgstr "Korisničko Ime Člana"

#: frontend/src/pages/ProgramForm.vue:256
msgid "Member added to program"
msgstr "Član dodan u program"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:25
msgid "Member already enrolled in this batch"
msgstr "Član je već upisan u ovu grupu"

#: lms/lms/doctype/lms_program/lms_program.py:29
msgid "Member {0} has already been added to this batch."
msgstr "Član {0} je već dodan u ovu grupu."

#. Group in LMS Batch Old's connections
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Members"
msgstr "Članovi"

#. Label of the membership (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Membership"
msgstr "Članstvo"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Label of the mentor (Link) field in DocType 'LMS Course Mentor Mapping'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Mentor"
msgstr "Mentor"

#. Label of the mentor_name (Data) field in DocType 'LMS Course Mentor Mapping'
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "Mentor Name"
msgstr "Ime Mentora"

#. Label of the mentor_request_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Mentor Request"
msgstr "Zahtjev Mentora"

#. Label of the mentor_request_creation (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:11
#: lms/patches/create_mentor_request_email_templates.py:18
#: lms/patches/create_mentor_request_email_templates.py:28
msgid "Mentor Request Creation Template"
msgstr "Šablon Kreiranje Zahtjeva za Mentora"

#. Label of the mentor_request_status_update (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:31
#: lms/patches/create_mentor_request_email_templates.py:38
#: lms/patches/create_mentor_request_email_templates.py:48
msgid "Mentor Request Status Update Template"
msgstr "Status Šablona Kreiranja Zahtjeva za Mentora"

#. Label of the meta_description (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:284 frontend/src/pages/CourseForm.vue:274
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Description"
msgstr "Meta Opis"

#. Label of the meta_image (Attach Image) field in DocType 'LMS Batch'
#. Label of the meta_image (Attach Image) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:200
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Image"
msgstr "Meta Slika"

#. Label of the meta_keywords (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:290 frontend/src/pages/CourseForm.vue:280
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Keywords"
msgstr "Meta Ključne Riječi"

#: frontend/src/pages/BatchForm.vue:279 frontend/src/pages/CourseForm.vue:269
msgid "Meta Tags"
msgstr "Meta tagovi"

#: lms/lms/api.py:1454
msgid "Meta tags should be a list."
msgstr "Meta oznake trebaju biti lista."

#. Label of the milestone (Check) field in DocType 'LMS Batch Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Milestone"
msgstr "Prekretnica"

#: lms/lms/doctype/lms_question/lms_question.py:48
msgid "Minimum two options are required for multiple choice questions."
msgstr "Za pitanja sa više odgovora potrebne su najmanje dvije opcije."

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:20
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Moderator"
msgstr "Moderator"

#: lms/lms/doctype/lms_badge/lms_badge.js:40
msgid "Modified By"
msgstr "Izmijenio"

#: lms/lms/api.py:218
msgid "Module Name is incorrect or does not exist."
msgstr "Naziv modula je netačan ili ne postoji."

#: lms/lms/api.py:214
msgid "Module is incorrect."
msgstr "Modul je netačan."

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Monday"
msgstr "Ponedjeljak"

#: frontend/src/components/AppSidebar.vue:599
msgid "Monetization"
msgstr "Monetizacija"

#: frontend/src/components/AppSidebar.vue:39
msgid "More"
msgstr "Više"

#. Label of the multiple (Check) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Multiple Correct Answers"
msgstr "Više Tačnih Odgovora"

#: frontend/src/pages/ProfileEvaluator.vue:4
msgid "My availability"
msgstr "Moja Dostupnost"

#: frontend/src/pages/ProfileEvaluator.vue:127
msgid "My calendar"
msgstr "Moj Kalendar"

#: frontend/src/components/Modals/EmailTemplateModal.vue:24
msgid "Name"
msgstr "Naziv"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: frontend/src/components/Settings/Categories.vue:27
#: frontend/src/components/Settings/EmailTemplates.vue:17
#: frontend/src/components/Settings/Evaluators.vue:24
#: frontend/src/components/Settings/Members.vue:24
#: frontend/src/components/Settings/ZoomSettings.vue:17
#: frontend/src/pages/Assignments.vue:19 frontend/src/pages/Batches.vue:17
#: frontend/src/pages/Courses.vue:17 frontend/src/pages/Courses.vue:310
#: frontend/src/pages/Programs.vue:14 lms/lms/doctype/lms_badge/lms_badge.json
msgid "New"
msgstr "Novi"

#: lms/www/lms.py:151
msgid "New Batch"
msgstr "Nova Grupa"

#: frontend/src/pages/CourseForm.vue:661 lms/www/lms.py:95
msgid "New Course"
msgstr "Novi Kurs"

#: frontend/src/components/Modals/EmailTemplateModal.vue:7
msgid "New Email Template"
msgstr "Novi šablon e-pošte"

#: frontend/src/pages/Jobs.vue:23
msgid "New Job"
msgstr "Novi Posao"

#: lms/job/doctype/lms_job_application/lms_job_application.py:27
msgid "New Job Applicant"
msgstr "Novi Kandidat za Posao"

#: frontend/src/pages/Programs.vue:90
msgid "New Program"
msgstr "Novi Program"

#: frontend/src/pages/ProgramForm.vue:133
msgid "New Program Course"
msgstr "Novi Kurs Programa"

#: frontend/src/pages/ProgramForm.vue:134
msgid "New Program Member"
msgstr "Novi Član Programa"

#: frontend/src/pages/QuizForm.vue:123
msgid "New Question"
msgstr "Novo Pitanje"

#: frontend/src/pages/QuizForm.vue:451 frontend/src/pages/QuizForm.vue:459
#: frontend/src/pages/Quizzes.vue:19
msgid "New Quiz"
msgstr "Novi Kviz"

#: lms/www/new-sign-up.html:3
msgid "New Sign Up"
msgstr "Nova Registracija"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "New Zoom Account"
msgstr "Novi Zoom račun"

#: lms/lms/utils.py:610
msgid "New comment in batch {0}"
msgstr "Novi komentar u grupi {0}"

#: lms/lms/utils.py:603
msgid "New reply on the topic {0} in course {1}"
msgstr "Novi odgovor na temu {0} na kursu {1}"

#: frontend/src/components/Discussions.vue:8
#: frontend/src/components/Discussions.vue:63
msgid "New {0}"
msgstr "Novi {0}"

#: frontend/src/components/Quiz.vue:227 frontend/src/pages/Lesson.vue:154
msgid "Next"
msgstr "Sljedeći"

#: lms/templates/quiz/quiz.html:125
msgid "Next Question"
msgstr "Sljedeće Pitanje"

#: frontend/src/components/Assessments.vue:75 lms/templates/assessments.html:58
msgid "No Assessments"
msgstr "Nema Procjena"

#: lms/templates/notifications.html:26
msgid "No Notifications"
msgstr "Nema Obavještenja"

#: frontend/src/components/Quiz.vue:297
msgid "No Quiz submissions found"
msgstr "Nije pronađen nijedna Kviz prijava"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "No Recording"
msgstr "Nema Snimanja"

#: lms/templates/upcoming_evals.html:43
msgid "No Upcoming Evaluations"
msgstr "Nema Predstojećih Ocjenjivanja"

#: frontend/src/components/Annoucements.vue:24
msgid "No announcements"
msgstr "Nema Objava"

#: lms/templates/certificates_section.html:23
msgid "No certificates"
msgstr "Bez Certifikata"

#: frontend/src/components/BatchCourses.vue:67
msgid "No courses added"
msgstr "Nema dodanih kurseva"

#: lms/templates/courses_created.html:14
msgid "No courses created"
msgstr "Nema kreiranih kurseva"

#: frontend/src/pages/Programs.vue:81
msgid "No courses in this program"
msgstr "Nema kurseva u ovom programu"

#: lms/templates/courses_under_review.html:14
msgid "No courses under review"
msgstr "Nema kurseva pod recenzijom"

#: frontend/src/components/BatchFeedback.vue:60
msgid "No feedback received yet."
msgstr "Još uvijek nema povratnih informacija."

#: frontend/src/pages/ProfileAbout.vue:12
msgid "No introduction"
msgstr "Nema Uvoda"

#: frontend/src/components/LiveClass.vue:97
msgid "No live classes scheduled"
msgstr "Nema zakazanih časova uživo"

#: frontend/src/components/Modals/QuizInVideo.vue:93
msgid "No quizzes added yet."
msgstr "Još nije dodat nijedan kviz."

#: frontend/src/components/Modals/EvaluationModal.vue:62
msgid "No slots available for this date."
msgstr "Nema slobodnih termina za ovaj datum."

#: frontend/src/components/Modals/AnnouncementModal.vue:90
msgid "No students in this batch"
msgstr "Nema učenika u ovoj grupi"

#: frontend/src/pages/AssignmentSubmissionList.vue:67
msgid "No submissions"
msgstr "Nema prijava"

#: frontend/src/components/EmptyState.vue:5 lms/templates/course_list.html:13
msgid "No {0}"
msgstr "Bez {0}"

#: lms/templates/quiz/quiz.html:147
msgid "No."
msgstr "Br."

#: lms/lms/user.py:29
msgid "Not Allowed"
msgstr "Nije dozvoljeno"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Applicable"
msgstr "Nije Primjenjivo"

#: lms/templates/assessments.html:48
msgid "Not Attempted"
msgstr "Nije pokušano"

#: lms/lms/widgets/NoPreviewModal.html:6
msgid "Not Available for Preview"
msgstr "Nije Dostupno za Pregled"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Graded"
msgstr "Nije Ocijenjeno"

#: frontend/src/components/NoPermission.vue:7 frontend/src/pages/Batch.vue:164
msgid "Not Permitted"
msgstr "Nije dozvoljeno"

#: frontend/src/components/Assignment.vue:36
#: frontend/src/components/Settings/BrandSettings.vue:10
#: frontend/src/components/Settings/PaymentSettings.vue:9
#: frontend/src/components/Settings/SettingDetails.vue:10
#: frontend/src/pages/QuizSubmission.vue:9
msgid "Not Saved"
msgstr "Nespremljeno"

#: frontend/src/pages/Notifications.vue:53
msgid "Nothing to see here."
msgstr "Ovde nema šta da se vidi."

#. Label of the notifications (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Notifications"
msgstr "Obavještenja"

#: lms/lms/widgets/NoPreviewModal.html:30
msgid "Notify me when available"
msgstr "Obavijesti me kada bude dostupno"

#: frontend/src/components/BatchStudents.vue:48
msgid "Number of Students"
msgstr "Broj Studenata"

#: frontend/src/pages/BatchForm.vue:150
msgid "Number of seats available"
msgstr "Broj slobodnih mjesta"

#. Label of the sb_00 (Section Break) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "OAuth Client ID"
msgstr "OAuth ID klijenta"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Office close to Home"
msgstr "Ured blizu Kuće"

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Offline"
msgstr "Van Mreže"

#: lms/templates/emails/certification.html:16
msgid "Once again, congratulations on this significant accomplishment."
msgstr "Još jednom čestitamo na ovom značajnom postignuću."

#: frontend/src/components/Assignment.vue:60
msgid "Once the moderator grades your submission, you'll find the details here."
msgstr "Kada moderator ocijeni vašu prijavu, detalje ćete pronaći ovdje."

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Online"
msgstr "Na Mreži"

#: frontend/src/pages/ProgramForm.vue:157
msgid "Only courses for which self learning is disabled can be added to program."
msgstr "Programu se mogu dodati samo kursevi za koje je samostalno učenje onemogućeno."

#: lms/templates/assignment.html:6
msgid "Only files of type {0} will be accepted."
msgstr "Samo datoteke tipa {0} će biti prihvaćeni."

#: frontend/src/pages/CourseForm.vue:597 frontend/src/utils/index.js:509
msgid "Only image file is allowed."
msgstr "Dozvoljena je samo datoteka slike."

#: frontend/src/components/Modals/ChapterModal.vue:218
msgid "Only zip files are allowed"
msgstr "Dozvoljene su samo zip datoteke"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: frontend/src/pages/QuizForm.vue:17
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Open"
msgstr "Otvori"

#: lms/templates/emails/assignment_submission.html:8
msgid "Open Assignment"
msgstr "Otvoreni Zadatak"

#: lms/templates/emails/lms_message.html:13
msgid "Open Course"
msgstr "Otvoreni Kurs"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Open Ended"
msgstr "Otvoreni Završen"

#. Label of the option (Data) field in DocType 'LMS Option'
#: frontend/src/components/Modals/Question.vue:67
#: lms/lms/doctype/lms_option/lms_option.json
msgid "Option"
msgstr "Opcija"

#. Label of the option_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 1"
msgstr "Opcija 1"

#. Label of the option_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 2"
msgstr "Opcija 2"

#. Label of the option_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 3"
msgstr "Opcija 3"

#. Label of the option_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 4"
msgstr "Opcija 4"

#: frontend/src/components/Modals/Question.vue:56
msgid "Options"
msgstr "Opcije"

#. Label of the order_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Order ID"
msgstr "ID Naloga"

#. Label of the organization (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Organization"
msgstr "Organizacija"

#: frontend/src/pages/Billing.vue:32
msgid "Original Amount"
msgstr "Originalni Iznos"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:28
msgid "Others"
msgstr "Ostalo"

#: lms/lms/doctype/lms_badge/lms_badge.js:39
msgid "Owner"
msgstr "Odgovorni"

#. Label of the pan (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "PAN"
msgstr "PAN"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "PDF"
msgstr "PDF"

#. Label of the pages (Table) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Pages"
msgstr "Stranice"

#. Label of the paid_batch (Check) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:260
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Paid Batch"
msgstr "Plaćena Grupa"

#. Label of the paid_certificate (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:237
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Certificate"
msgstr "Plaćeni Certifikat"

#: frontend/src/components/CourseCardOverlay.vue:139
msgid "Paid Certificate after Evaluation"
msgstr "Plaćeni Certifikat nakon Ocjenjivanja"

#. Label of the paid_course (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:227
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Course"
msgstr "Plaćeni Kurs"

#: frontend/src/pages/Billing.vue:115
msgid "Pan Number"
msgstr "Pan Broj"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:177
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Part Time"
msgstr "Honorarno"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Partially Complete"
msgstr "Djelomično Završeno"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:353
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Pass"
msgstr "Uspješno"

#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz'
#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizForm.vue:72 frontend/src/pages/Quizzes.vue:131
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Passing Percentage"
msgstr "Procentualna Prolaznost"

#. Label of the password (Password) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Password"
msgstr "Lozinka"

#: frontend/src/pages/CourseForm.vue:197
msgid "Paste the youtube link of a short video introducing the course"
msgstr "Zalijepite youtube vezu kratkog videa koji predstavlja kurs"

#. Label of the payment (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the payment (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Payment"
msgstr "Plaćanje"

#. Name of a DocType
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Payment Country"
msgstr "Zemlja Plaćanja"

#. Label of the payment_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Details"
msgstr "Detalji Plaćanja"

#. Label of the payment_gateway (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Gateway"
msgstr "Platni Prolaz"

#. Label of the payment_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment ID"
msgstr "ID Plaćanja"

#. Label of the payment_received (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Received"
msgstr "Plaćanje Primljeno"

#. Label of the payment_reminder_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Reminder Template"
msgstr "Šablon Podsjetnika Plaćanja"

#. Label of the payment_settings_tab (Tab Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Settings"
msgstr "Postavke Plaćanja"

#: frontend/src/pages/Billing.vue:21
msgid "Payment for "
msgstr "Plaćanje za "

#. Label of the payment_for_certificate (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Certificate"
msgstr "Plaćanje za Certifikat"

#. Label of the payment_for_document (Dynamic Link) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document"
msgstr "Plaćanje za Dokument"

#. Label of the payment_for_document_type (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document Type"
msgstr "Plaćanje za Tip Dokumenta"

#. Label of the payments_app_is_not_installed (HTML) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payments app is not installed"
msgstr "Aplikacija plaćanja nije instalirana"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Modals/Event.vue:345
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Pending"
msgstr "Na čekanju"

#. Label of the percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:44
#: frontend/src/pages/QuizSubmissionList.vue:102
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Percentage"
msgstr "Procenat"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Percentage (e.g. 70%)"
msgstr "Procenat (npr. 70%)"

#: frontend/src/components/Modals/BatchStudentProgress.vue:44
msgid "Percentage/Status"
msgstr "Procentualno/Status"

#. Label of the persona_captured (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Persona Captured"
msgstr "Persona Uhvaćena"

#: frontend/src/pages/Billing.vue:99
msgid "Phone Number"
msgstr "Broj Telefona"

#: lms/lms/doctype/lms_settings/lms_settings.py:34
msgid "Please add <a href='{0}'>{1}</a> for <a href='{2}'>{3}</a> to send calendar invites for evaluations."
msgstr "Dodaj <a href='{0}'>{1}</a> za <a href='{2}'>{3}</a> za slanje kalendarskih pozivnica za ocjenjivanje."

#: frontend/src/components/LiveClass.vue:8
msgid "Please add a zoom account to the batch to create live classes."
msgstr "Dodaj Zoom račun u grupu kako biste kreirali časove uživo."

#: lms/lms/user.py:75
msgid "Please ask your administrator to verify your sign-up"
msgstr "Zamoli administratora da potvrdi vašu registraciju"

#: lms/lms/user.py:73
msgid "Please check your email for verification"
msgstr "Provjeri e-poštu za potvrdu"

#: lms/templates/emails/community_course_membership.html:7
msgid "Please click on the following button to set your new password"
msgstr "Klikni na sljedeće dugme da postavite novu lozinku"

#: lms/lms/utils.py:2040 lms/lms/utils.py:2044
msgid "Please complete the previous courses in the program to enroll in this course."
msgstr "Završite prethodne kurseve u programu da biste se upisali na ovaj kurs."

#: lms/lms/doctype/lms_batch/lms_batch.py:211
msgid "Please enable the zoom account to use this feature."
msgstr "Omogući Zoom račun da biste koristili ovu funkciju."

#: frontend/src/components/CourseOutline.vue:328
msgid "Please enroll for this course to view this lesson"
msgstr "Prijavi se za ovaj kurs da prikažete ovu lekciju"

#: frontend/src/components/Quiz.vue:16
msgid "Please ensure that you complete all the questions in {0} minutes."
msgstr "Popuni sva pitanja za {0} minuta."

#: frontend/src/components/Modals/LiveClassModal.vue:186
msgid "Please enter a title."
msgstr "Unesi Naziv"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:31
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:84
msgid "Please enter a valid URL."
msgstr "Unesi važeći URL."

#: frontend/src/components/Modals/LiveClassModal.vue:198
msgid "Please enter a valid time in the format HH:mm."
msgstr "Unesi važeće vrijeme u formatu HH:mm."

#: frontend/src/components/Modals/QuizInVideo.vue:181
msgid "Please enter a valid timestamp"
msgstr "Unesi važeću vremensku oznaku"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:78
msgid "Please enter the URL for assignment submission."
msgstr "Unesi URL za podnošenje zadatka."

#: lms/templates/quiz/quiz.js:176
msgid "Please enter your answer"
msgstr "Unesi svoj odgovor"

#: lms/lms/doctype/lms_batch/lms_batch.py:63
msgid "Please install the Payments App to create a paid batch. Refer to the documentation for more details. {0}"
msgstr "Instaliraj aplikaciju plaćanja da kreirate plaćenu grupu. Za više detalja pogledajte dokumentaciju. {0}"

#: lms/lms/doctype/lms_course/lms_course.py:55
msgid "Please install the Payments App to create a paid course. Refer to the documentation for more details. {0}"
msgstr "Instaliraj aplikaciju plaćanja da kreirate plaćeni kurs. Za više detalja pogledajte dokumentaciju. {0}"

#: frontend/src/pages/Billing.vue:254
msgid "Please let us know where you heard about us from."
msgstr "Recite nam odakle ste čuli za nas."

#: frontend/src/components/QuizBlock.vue:5
msgid "Please login to access the quiz."
msgstr "Prijavi se da pristupiš kvizu."

#: frontend/src/components/NoPermission.vue:25 frontend/src/pages/Batch.vue:175
msgid "Please login to access this page."
msgstr "Prijavi se da pristupiš ovoj stranici."

#: lms/lms/api.py:210
msgid "Please login to continue with payment."
msgstr "Prijavi se da nastaviš s plaćanjem."

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:7
#: lms/templates/emails/certificate_request_notification.html:7
msgid "Please prepare well and be on time for the evaluations."
msgstr "Dobro se pripremi i dođi na vrijeme za ocjenjivanje."

#: frontend/src/components/UpcomingEvaluations.vue:98
msgid "Please schedule an evaluation to get certified."
msgstr "Zakaži ocjenjvanje kako biste dobili certifikat."

#: frontend/src/components/Modals/LiveClassModal.vue:189
msgid "Please select a date."
msgstr "Odaberi Datum."

#: frontend/src/components/Modals/LiveClassModal.vue:213
msgid "Please select a duration."
msgstr "Odaberi trajanje."

#: frontend/src/components/Modals/LiveClassModal.vue:210
msgid "Please select a future date and time."
msgstr "Odaberi budući datum i vrijeme."

#: frontend/src/components/Modals/QuizInVideo.vue:186
msgid "Please select a quiz"
msgstr "Odaberi kviz"

#: frontend/src/components/Modals/LiveClassModal.vue:192
msgid "Please select a time."
msgstr "Odaberi vrijeme."

#: frontend/src/components/Modals/LiveClassModal.vue:195
msgid "Please select a timezone."
msgstr "Odaberi vremensku zonu."

#: frontend/src/components/Quiz.vue:523
msgid "Please select an option"
msgstr "Odaberi opciju"

#: lms/templates/emails/job_report.html:6
msgid "Please take appropriate action at {0}"
msgstr "Poduzmi odgovarajuće mjere na {0}"

#: frontend/src/components/Modals/ChapterModal.vue:175
msgid "Please upload a SCORM package"
msgstr "Učitaj SCORM Paket"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:81
msgid "Please upload the assignment file."
msgstr "Učitaj datoteku zadatka."

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Point of Score (e.g. 70)"
msgstr "Bodova (npr. 70)"

#: frontend/src/components/Modals/Question.vue:62
msgid "Possibilities"
msgstr "Mogućnosti"

#: frontend/src/components/Modals/Question.vue:88
msgid "Possibility"
msgstr "Mogućnost"

#. Label of the possibility_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 1"
msgstr "Mogući Odgovor 1"

#. Label of the possibility_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 2"
msgstr "Mogući Odgovor 2"

#. Label of the possibility_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 3"
msgstr "Mogući Odgovor 3"

#. Label of the possibility_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 4"
msgstr "Mogući Odgovor 4"

#: frontend/src/components/DiscussionReplies.vue:54
#: frontend/src/components/DiscussionReplies.vue:89
msgid "Post"
msgstr "Objava"

#: frontend/src/pages/Billing.vue:95
msgid "Postal Code"
msgstr "Poštanski Broj"

#: frontend/src/components/AppSidebar.vue:122
msgid "Powered by Learning"
msgstr "Pokreće se od Učenje"

#. Name of a DocType
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Preferred Function"
msgstr "Preferirana Funkcija"

#. Label of the preferred_functions (Table MultiSelect) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Functions"
msgstr "Preferirane Funkcije"

#. Label of the preferred_industries (Table MultiSelect) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Industries"
msgstr "Preferirane Industrije"

#. Name of a DocType
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Preferred Industry"
msgstr "Preferirana Industrija"

#. Label of the preferred_location (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Location"
msgstr "Preferirana Lokacija"

#. Label of the image (Attach Image) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Preview Image"
msgstr "Pregled slike"

#: frontend/src/pages/CourseForm.vue:195
msgid "Preview Video"
msgstr "Pregledaj Video"

#: frontend/src/pages/Lesson.vue:119
msgid "Previous"
msgstr "Prethodna"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:255
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Pricing"
msgstr "Određivanje Cijena"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:221
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Pricing and Certification"
msgstr "Cijene i Certifikati"

#. Label of the exception_country (Table MultiSelect) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Primary Countries"
msgstr "Primarne Zemlje"

#. Label of the subgroup (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Primary Subgroup"
msgstr "Primarna Podgrupa"

#: lms/lms/utils.py:441
msgid "Privacy Policy"
msgstr "Pravila Privatnosti"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Private"
msgstr "Privatno"

#. Description of the 'Hide my Private Information from others' (Check) field
#. in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Private Information includes your Grade and Work Environment Preferences"
msgstr "Privatni Podaci uključuju vašu Ocjenu i Preference Radnog Okruženja"

#: frontend/src/pages/Billing.vue:129
msgid "Proceed to Payment"
msgstr "Prijeđi na Plaćanje"

#. Label of the profession (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Profession"
msgstr "Profesija"

#: frontend/src/components/Modals/EditProfile.vue:37
msgid "Profile Image"
msgstr "Profilna Slika"

#: frontend/src/pages/ProgramForm.vue:155
msgid "Program Course"
msgstr "Kurs Programa"

#. Label of the program_courses (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:17
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Courses"
msgstr "Kursevi Programa"

#: frontend/src/pages/ProgramForm.vue:170
msgid "Program Member"
msgstr "Član Programa"

#. Label of the program_members (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:79
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Members"
msgstr "Članovi Programa"

#: frontend/src/pages/Programs.vue:206 frontend/src/pages/Programs.vue:212
#: lms/www/lms.py:295
msgid "Programs"
msgstr "Programi"

#. Label of the progress (Float) field in DocType 'LMS Enrollment'
#. Label of the progress (Int) field in DocType 'LMS Program Member'
#: frontend/src/components/Modals/BatchStudentProgress.vue:94
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "Progress"
msgstr "Napredak"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:77
msgid "Progress (%)"
msgstr "Napredak (%)"

#: frontend/src/components/BatchStudents.vue:41
msgid "Progress of students in courses and assessments"
msgstr "Napredak studenata u kursevima i ocjenjivanju"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Public"
msgstr "Javno"

#. Label of the published (Check) field in DocType 'LMS Certificate'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Publish on Participant Page"
msgstr "Objavi na Stranici Učesnika"

#. Label of the published (Check) field in DocType 'LMS Batch'
#. Label of the published (Check) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BulkCertificates.vue:51
#: frontend/src/components/Modals/Event.vue:108
#: frontend/src/pages/BatchForm.vue:52 frontend/src/pages/CourseForm.vue:150
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published"
msgstr "Objavljeno"

#: frontend/src/pages/Statistics.vue:10
#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:4
msgid "Published Courses"
msgstr "Objavljeni Kursevi"

#. Label of the published_on (Date) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:154
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published On"
msgstr "Objavljeno dana"

#. Label of the purchased_certificate (Check) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Purchased Certificate"
msgstr "Kupljeni Certifikat"

#. Label of the question (Small Text) field in DocType 'Course Lesson'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the question (Text Editor) field in DocType 'LMS Question'
#. Label of the question (Link) field in DocType 'LMS Quiz Question'
#. Label of the question (Text) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Assignment.vue:20
#: frontend/src/components/Modals/AssignmentForm.vue:32
#: frontend/src/components/Modals/Question.vue:27
#: frontend/src/pages/QuizForm.vue:390 frontend/src/pages/QuizSubmission.vue:56
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:104
msgid "Question"
msgstr "Pitanje"

#: lms/templates/quiz/quiz.html:62
msgid "Question "
msgstr "Pitanje "

#. Label of the question_detail (Text) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Question Detail"
msgstr "Detalj Pitanja"

#. Label of the question_name (Link) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Question Name"
msgstr "Naziv Pitanja"

#: frontend/src/components/Modals/Question.vue:281
msgid "Question added successfully"
msgstr "Pitanje je uspješno dodano"

#: frontend/src/components/Modals/Question.vue:331
msgid "Question updated successfully"
msgstr "Pitanje je uspješno ažurirano"

#: frontend/src/components/Quiz.vue:102
msgid "Question {0}"
msgstr "Pitanje {0}"

#: frontend/src/components/Quiz.vue:204
msgid "Question {0} of {1}"
msgstr "Pitanje {0} od {1}"

#. Label of the questions (Table) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:117 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Questions"
msgstr "Pitanja"

#: frontend/src/pages/QuizForm.vue:432
msgid "Questions deleted successfully"
msgstr "Pitanja su uspješno izbrisana"

#. Label of the quiz (Link) field in DocType 'LMS Quiz Submission'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Modals/QuizInVideo.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:216
#: frontend/src/pages/QuizSubmission.vue:26 frontend/src/utils/quiz.js:24
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/workspace/lms/lms.json
msgid "Quiz"
msgstr "Kviz"

#. Label of the quiz_id (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz ID"
msgstr "Kviz ID"

#. Label of a Link in the LMS Workspace
#: frontend/src/pages/QuizPage.vue:57 lms/lms/workspace/lms/lms.json
msgid "Quiz Submission"
msgstr "Podnošenje Kviza"

#: frontend/src/pages/QuizSubmission.vue:131
#: frontend/src/pages/QuizSubmissionList.vue:111
#: frontend/src/pages/QuizSubmissionList.vue:116
msgid "Quiz Submissions"
msgstr "Prijave za Kviz"

#: frontend/src/components/Quiz.vue:241
msgid "Quiz Summary"
msgstr "Sažetak Kviza"

#. Label of the quiz_title (Data) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Quiz Title"
msgstr "Naziv Kviza"

#: frontend/src/pages/QuizForm.vue:344
msgid "Quiz created successfully"
msgstr "Kviz je uspješno kreiran"

#: lms/plugins.py:96
msgid "Quiz is not available to Guest users. Please login to continue."
msgstr "Kviz nije dostupan gostima. Prijavi se da nastaviš."

#: frontend/src/pages/QuizForm.vue:363
msgid "Quiz updated successfully"
msgstr "Kviz je uspješno ažuriran"

#. Description of the 'Quiz ID' (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz will appear at the bottom of the lesson."
msgstr "Kviz će se pojaviti na dnu lekcije."

#: frontend/src/components/AppSidebar.vue:583
#: frontend/src/pages/QuizForm.vue:443 frontend/src/pages/Quizzes.vue:142
#: frontend/src/pages/Quizzes.vue:152 lms/www/lms.py:251
msgid "Quizzes"
msgstr "Kvizovi"

#: frontend/src/components/Modals/QuizInVideo.vue:35
msgid "Quizzes in this video"
msgstr "Kvizovi u ovom videu"

#. Label of the rating (Rating) field in DocType 'LMS Certificate Evaluation'
#. Label of the rating (Data) field in DocType 'LMS Course'
#. Label of the rating (Rating) field in DocType 'LMS Course Review'
#: frontend/src/components/CourseCardOverlay.vue:121
#: frontend/src/components/Modals/Event.vue:86
#: frontend/src/components/Modals/ReviewModal.vue:18
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/templates/reviews.html:125
msgid "Rating"
msgstr "Procjena"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.py:17
msgid "Rating cannot be 0"
msgstr "Ocjena ne može biti 0"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Ready"
msgstr "Spreman"

#. Label of the reference_docname (Dynamic Link) field in DocType 'LMS Batch
#. Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Reference DocName"
msgstr "Referentni naziv dokumenta"

#. Label of the reference_doctype (Link) field in DocType 'LMS Batch Timetable'
#. Label of the reference_doctype (Link) field in DocType 'LMS Timetable
#. Legend'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Reference DocType"
msgstr "Referentni DocType"

#. Label of the reference_doctype (Link) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Reference Document Type"
msgstr "Referentna vrsta dokumenta"

#: lms/templates/emails/community_course_membership.html:17
msgid "Regards"
msgstr "Pozdrav"

#: frontend/src/components/BatchOverlay.vue:89
msgid "Register Now"
msgstr "Registrirajte se Sada"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Registered"
msgstr "Registriran(a)"

#: lms/lms/user.py:36
msgid "Registered but disabled"
msgstr "Registrovan, ali onemogućen"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Rejected"
msgstr "Odbijeno"

#. Label of the related_courses (Table) field in DocType 'LMS Course'
#. Name of a DocType
#: frontend/src/components/RelatedCourses.vue:5
#: frontend/src/pages/CourseForm.vue:206
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/related_courses/related_courses.json
msgid "Related Courses"
msgstr "Povezani Kursevi"

#: frontend/src/pages/BatchForm.vue:236 frontend/src/pages/CourseForm.vue:127
msgid "Remove"
msgstr "Ukloni"

#: frontend/src/components/Modals/AnnouncementModal.vue:27
msgid "Reply To"
msgstr "Odgovori"

#: lms/lms/widgets/RequestInvite.html:7
msgid "Request Invite"
msgstr "Zatraži Poziv"

#: lms/patches/create_mentor_request_email_templates.py:20
msgid "Request for Mentorship"
msgstr "Zahtjev za Mentorstvo"

#. Label of the required_role (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Required Role"
msgstr "Obavezna Uloga"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Restricted"
msgstr "Ograničeno"

#. Label of the result (Table) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Result"
msgstr "Rezultat"

#. Label of the resume (Attach) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Resume"
msgstr "Nastavi"

#: frontend/src/components/Quiz.vue:75 frontend/src/components/Quiz.vue:278
msgid "Resume Video"
msgstr "Nastavi Video"

#. Label of the review (Small Text) field in DocType 'LMS Course Review'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Modals/ReviewModal.vue:20
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/workspace/lms/lms.json lms/templates/reviews.html:143
msgid "Review"
msgstr "Recenzija"

#: lms/templates/reviews.html:100
msgid "Review the course"
msgstr "Pregledaj Kurs"

#. Label of the reviewed_by (Link) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Reviewed By"
msgstr "Pregledao"

#: lms/templates/reviews.html:4
msgid "Reviews"
msgstr "Recenzije"

#. Label of the role (Select) field in DocType 'Cohort Staff'
#. Label of the role (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Role"
msgstr "Uloga"

#. Label of the role (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Role Preference"
msgstr "Preferenca Uloge"

#: frontend/src/pages/ProfileRoles.vue:105
msgid "Role updated successfully"
msgstr "Uloga je uspješno ažurirana"

#: frontend/src/components/AppSidebar.vue:611
msgid "Roles"
msgstr "Uloge"

#. Label of the route (Data) field in DocType 'LMS Sidebar Item'
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Route"
msgstr "Ruta"

#: lms/lms/doctype/lms_batch/lms_batch.py:139
msgid "Row #{0} Date cannot be outside the batch duration."
msgstr "Red #{0} Datum ne može biti izvan trajanja grupe."

#: lms/lms/doctype/lms_batch/lms_batch.py:134
msgid "Row #{0} End time cannot be outside the batch duration."
msgstr "Red #{0} Vrijeme završetka ne može biti izvan trajanja grupe."

#: lms/lms/doctype/lms_batch/lms_batch.py:116
msgid "Row #{0} Start time cannot be greater than or equal to end time."
msgstr "Red #{0} Vrijeme početka ne može biti kasnije ili isto vremenu završetka."

#: lms/lms/doctype/lms_batch/lms_batch.py:125
msgid "Row #{0} Start time cannot be outside the batch duration."
msgstr "Red #{0} Vrijeme početka ne može biti izvan trajanja grupe."

#: lms/lms/doctype/lms_quiz/lms_quiz.py:32
msgid "Rows {0} have the duplicate questions."
msgstr "Redovi {0} imaju dupla pitanja."

#: lms/templates/livecode/extension_footer.html:21
msgid "Run"
msgstr "Izvrši"

#. Label of the scorm_section (Section Break) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM"
msgstr "SCORM"

#. Label of the scorm_package (Link) field in DocType 'Course Chapter'
#: frontend/src/components/Modals/ChapterModal.vue:22
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package"
msgstr "SCORM Paket"

#. Label of the scorm_package_path (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package Path"
msgstr "SCORM Paket Put"

#. Label of the seo_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "SEO"
msgstr "SEO"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Saturday"
msgstr "Subota"

#: frontend/src/components/AssessmentPlugin.vue:34
#: frontend/src/components/Assignment.vue:46
#: frontend/src/components/Controls/CodeEditor.vue:25
#: frontend/src/components/Modals/AssignmentForm.vue:59
#: frontend/src/components/Modals/EmailTemplateModal.vue:12
#: frontend/src/components/Modals/Event.vue:101
#: frontend/src/components/Modals/Event.vue:129
#: frontend/src/components/Modals/ZoomAccountModal.vue:10
#: frontend/src/pages/BatchForm.vue:8 frontend/src/pages/CourseForm.vue:17
#: frontend/src/pages/JobForm.vue:8 frontend/src/pages/LessonForm.vue:14
#: frontend/src/pages/ProgramForm.vue:7 frontend/src/pages/QuizForm.vue:34
#: frontend/src/pages/QuizSubmission.vue:14
msgid "Save"
msgstr "Spremi"

#. Label of the schedule (Table) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Schedule"
msgstr "Raspored"

#: frontend/src/components/Modals/EvaluationModal.vue:5
#: frontend/src/components/UpcomingEvaluations.vue:11
msgid "Schedule Evaluation"
msgstr "Raspored Ocjenjivanja"

#. Name of a DocType
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Scheduled Flow"
msgstr "Raspoređeni Protok"

#. Label of the scope (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Scope"
msgstr "Obim"

#. Label of the score (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:39
#: frontend/src/pages/QuizSubmissionList.vue:96
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/templates/quiz/quiz.html:148
msgid "Score"
msgstr "Bodovi"

#. Label of the score_out_of (Int) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Score Out Of"
msgstr "Rezultat Od Mogućih"

#: frontend/src/components/Settings/Evaluators.vue:15
#: frontend/src/components/Settings/Members.vue:15
#: frontend/src/pages/Jobs.vue:41
msgid "Search"
msgstr "Traži"

#: frontend/src/pages/CertifiedParticipants.vue:23
msgid "Search by Name"
msgstr "Traži po Imenu"

#: frontend/src/pages/Batches.vue:45 frontend/src/pages/Courses.vue:41
msgid "Search by Title"
msgstr "Traži po Nazivu"

#: frontend/src/pages/Assignments.vue:37
msgid "Search by title"
msgstr "Traži po Nazivu"

#: frontend/src/components/Controls/IconPicker.vue:36
msgid "Search for an icon"
msgstr "Traži Ikonu"

#. Label of the seat_count (Int) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:147
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Seat Count"
msgstr "Broj Mjesta"

#: frontend/src/components/BatchCard.vue:18
#: frontend/src/components/BatchOverlay.vue:17
msgid "Seat Left"
msgstr "Preostalo Mjesto"

#: lms/lms/doctype/lms_batch/lms_batch.py:103
msgid "Seat count cannot be negative."
msgstr "Broj sjedišta ne može biti negativan."

#: frontend/src/components/BatchCard.vue:15
#: frontend/src/components/BatchOverlay.vue:14
msgid "Seats Left"
msgstr "Preostalih Mjesta"

#: frontend/src/components/Modals/Question.vue:98
msgid "Select a question"
msgstr "Odaberi Pitanje"

#: frontend/src/components/AssessmentPlugin.vue:21
msgid "Select a quiz"
msgstr "Odaberi Kviz"

#: frontend/src/components/Modals/EvaluationModal.vue:40
msgid "Select a slot"
msgstr "Odaberi termin"

#: frontend/src/components/AssessmentPlugin.vue:28
msgid "Select an assignment"
msgstr "Odaberi Zadatak"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.js:7
msgid "Send Confirmation Email"
msgstr "Pošalji potvrdnu e-poštu"

#. Label of the send_calendar_invite_for_evaluations (Check) field in DocType
#. 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Send calendar invite for evaluations"
msgstr "Pošalji kalendarsku pozivnicu za ocjenjivanje"

#. Label of the sessions_on (Data) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Sessions On Days"
msgstr "Časovi na Dane"

#: lms/templates/emails/community_course_membership.html:1
msgid "Set your Password"
msgstr "Postavite svoju Lozinku"

#: frontend/src/components/AppSidebar.vue:559
msgid "Setting up"
msgstr "Postavljanje"

#: frontend/src/components/AppSidebar.vue:604
msgid "Setting up payment gateway"
msgstr "Postavljanje Platnog Prolaza"

#: frontend/src/components/AppSidebar.vue:609
#: frontend/src/components/Settings/Settings.vue:7
#: frontend/src/pages/BatchForm.vue:46 frontend/src/pages/CourseForm.vue:143
#: frontend/src/pages/ProfileRoles.vue:4 frontend/src/pages/QuizForm.vue:79
msgid "Settings"
msgstr "Postavke"

#: frontend/src/pages/ProfileAbout.vue:62
msgid "Share on"
msgstr "Dijeli"

#: frontend/src/pages/BatchForm.vue:35
msgid "Short Description"
msgstr "Kratki Opis"

#. Label of the short_introduction (Small Text) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:80
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Short Introduction"
msgstr "Kratki Uvod"

#: frontend/src/pages/BatchForm.vue:38
msgid "Short description of the batch"
msgstr "Kratak opis Grupe"

#. Label of the show_answer (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Show Answer"
msgstr "Prikaži Odgovor"

#. Label of the show_answers (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:85 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Answers"
msgstr "Prikaži Odgovore"

#. Label of the show_submission_history (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:90 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Submission History"
msgstr "Prikaži Istoriju Prijava"

#. Label of the column_break_2 (Column Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show Tab in Batch"
msgstr "Prikaži Karticu u Grupi"

#. Label of the show_usd_equivalent (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show USD Equivalent"
msgstr "Prikaži Protuvrijednost u USD"

#. Label of the show_day_view (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show day view in timetable"
msgstr "Prikaži dnevni prikaz u rasporedu"

#. Label of the show_live_class (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Show live class"
msgstr "Prikaži čas uživo"

#. Label of the shuffle_questions (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:103 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Shuffle Questions"
msgstr "Nasumična Pitanja"

#: frontend/src/pages/QuizForm.vue:97
msgid "Shuffle Settings"
msgstr "Nasumične Postavke"

#. Label of the sidebar_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar"
msgstr "Bočna traka"

#. Label of the sidebar_items (Table) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar Items"
msgstr "Stavke bočne trake"

#: lms/lms/user.py:29
msgid "Sign Up is disabled"
msgstr "Prijava je onemogućena"

#: lms/templates/signup-form.html:53
msgid "Sign up"
msgstr "Prijavi se"

#. Label of the signup_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Signup Email"
msgstr "E-pošta za Prijavu"

#. Label of the signup_settings_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Signup Settings"
msgstr "Postavke Prijave"

#. Label of a chart in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Signups"
msgstr "Prijave"

#. Label of the skill (Table MultiSelect) field in DocType 'User'
#. Label of the skill (Data) field in DocType 'User Skill'
#: lms/fixtures/custom_field.json lms/lms/doctype/user_skill/user_skill.json
msgid "Skill"
msgstr "Vještina"

#. Label of the skill_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Skill Details"
msgstr "Detalji Vještina"

#. Label of the skill_name (Link) field in DocType 'Skills'
#: lms/lms/doctype/skills/skills.json
msgid "Skill Name"
msgstr "Naziv Vještine"

#. Name of a DocType
#: lms/lms/doctype/skills/skills.json
msgid "Skills"
msgstr "Vještine"

#: frontend/src/pages/PersonaForm.vue:51 lms/templates/onboarding_header.html:6
msgid "Skip"
msgstr "Preskoči"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:63
msgid "Slot Times are overlapping for some schedules."
msgstr "Vremena termina se preklapaju za neke rasporede."

#: frontend/src/pages/ProfileEvaluator.vue:201
msgid "Slot added successfully"
msgstr "Termin uspješno dodan"

#: frontend/src/pages/ProfileEvaluator.vue:240
msgid "Slot deleted successfully"
msgstr "Termin uspješno izbrisan"

#. Label of the slug (Data) field in DocType 'Cohort'
#. Label of the slug (Data) field in DocType 'Cohort Subgroup'
#. Label of the slug (Data) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Slug"
msgstr "Slug"

#: frontend/src/components/BatchCard.vue:25
#: frontend/src/components/BatchOverlay.vue:24
msgid "Sold Out"
msgstr "Rasprodano"

#. Label of the solution (Code) field in DocType 'Exercise Latest Submission'
#. Label of the solution (Code) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Solution"
msgstr "Rješenje"

#. Label of the source (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the source (Link) field in DocType 'LMS Payment'
#. Label of the source (Data) field in DocType 'LMS Source'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_source/lms_source.json
msgid "Source"
msgstr "Izvor"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Staff"
msgstr "Osoblje"

#. Label of the stage (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Stage"
msgstr "Faza"

#: frontend/src/components/LiveClass.vue:70 frontend/src/components/Quiz.vue:71
#: lms/templates/quiz/quiz.html:39
msgid "Start"
msgstr "Start"

#. Label of the start_date (Date) field in DocType 'Education Detail'
#. Label of the start_date (Date) field in DocType 'LMS Batch'
#. Label of the start_date (Date) field in DocType 'LMS Batch Old'
#: frontend/src/pages/BatchForm.vue:75
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Start Date"
msgstr "Start Datum"

#: lms/templates/emails/batch_start_reminder.html:13
msgid "Start Date:"
msgstr "Datum Početka:"

#: frontend/src/components/CourseCardOverlay.vue:67
#: frontend/src/pages/Lesson.vue:39 frontend/src/pages/SCORMChapter.vue:28
#: lms/templates/emails/lms_course_interest.html:9
msgid "Start Learning"
msgstr "Počni Učiti"

#. Label of the start_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the start_time (Time) field in DocType 'LMS Batch'
#. Label of the start_time (Time) field in DocType 'LMS Batch Old'
#. Label of the start_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the start_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:91
#: frontend/src/pages/ProfileEvaluator.vue:29
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Start Time"
msgstr "Vrijeme Početka"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:34
msgid "Start Time cannot be greater than End Time"
msgstr "Vrijeme Početka ne može biti kasnije od Vremena Završetka"

#. Label of the start_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Start URL"
msgstr "Početni URL"

#: frontend/src/components/Quiz.vue:71
msgid "Start the Quiz"
msgstr "Započni kviz"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Startup Organization"
msgstr "Pokretačka Organizacija"

#: frontend/src/pages/Billing.vue:83
msgid "State"
msgstr "Zemlja"

#. Label of the tab_4_tab (Tab Break) field in DocType 'LMS Course'
#. Label of the statistics (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:5
#: frontend/src/pages/Statistics.vue:225
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:204
msgid "Statistics"
msgstr "Statistika"

#. Label of the status (Select) field in DocType 'Job Opportunity'
#. Label of the status (Select) field in DocType 'Cohort'
#. Label of the status (Select) field in DocType 'Cohort Join Request'
#. Label of the status (Select) field in DocType 'Exercise Latest Submission'
#. Label of the status (Select) field in DocType 'Exercise Submission'
#. Label of the status (Select) field in DocType 'Invite Request'
#. Label of the status (Select) field in DocType 'LMS Assignment Submission'
#. Label of the status (Select) field in DocType 'LMS Batch Old'
#. Label of the status (Select) field in DocType 'LMS Certificate Evaluation'
#. Label of the status (Select) field in DocType 'LMS Certificate Request'
#. Label of the status (Select) field in DocType 'LMS Course'
#. Label of the status (Select) field in DocType 'LMS Course Progress'
#. Label of the status (Select) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Modals/Event.vue:91
#: frontend/src/components/Settings/ZoomSettings.vue:182
#: frontend/src/pages/AssignmentSubmissionList.vue:19
#: frontend/src/pages/JobForm.vue:46
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Status"
msgstr "Status"

#: lms/templates/assessments.html:17
msgid "Status/Score"
msgstr "Status/Rezultat"

#. Option for the 'User Category' (Select) field in DocType 'User'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: frontend/src/pages/ProfileRoles.vue:38 lms/fixtures/custom_field.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/templates/signup-form.html:26
msgid "Student"
msgstr "Student"

#: frontend/src/components/CourseReviews.vue:11
msgid "Student Reviews"
msgstr "Studentske Recenzije"

#. Label of the show_students (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:11
#: frontend/src/components/BatchStudents.vue:67
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Students"
msgstr "Studenti"

#: frontend/src/components/BatchStudents.vue:285
msgid "Students deleted successfully"
msgstr "Studenti su uspješno izbrisani"

#. Description of the 'Paid Batch' (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Students will be enrolled in a paid batch once they complete the payment"
msgstr "Studenti će biti upisani u plaćenu grupu kada završe uplatu"

#. Label of the subgroup (Link) field in DocType 'Cohort Join Request'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the subgroup (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Subgroup"
msgstr "Podgrupa"

#: frontend/src/components/Modals/AnnouncementModal.vue:20
#: frontend/src/components/Modals/EmailTemplateModal.vue:31
msgid "Subject"
msgstr "Predmet"

#: frontend/src/components/Modals/AnnouncementModal.vue:93
msgid "Subject is required"
msgstr "Predmet je obavezan"

#: frontend/src/components/Assignment.vue:32
msgid "Submission"
msgstr "Predaja"

#: frontend/src/pages/QuizForm.vue:30
msgid "Submission List"
msgstr "List Predaje"

#: frontend/src/components/Modals/AssignmentForm.vue:27
msgid "Submission Type"
msgstr "Tip Podnošenja"

#: frontend/src/components/Assignment.vue:13
#: frontend/src/components/Assignment.vue:16
msgid "Submission by"
msgstr "Predaja od"

#: frontend/src/components/Modals/AssessmentModal.vue:9
#: frontend/src/components/Modals/BatchCourseModal.vue:9
#: frontend/src/components/Modals/EvaluationModal.vue:9
#: frontend/src/components/Modals/Question.vue:109
#: frontend/src/components/Quiz.vue:232 lms/templates/assignment.html:9
#: lms/templates/livecode/extension_footer.html:25
#: lms/templates/quiz/quiz.html:128 lms/templates/reviews.html:163
#: lms/www/new-sign-up.html:32
msgid "Submit"
msgstr "Potvrdi"

#: frontend/src/components/BatchFeedback.vue:35
msgid "Submit Feedback"
msgstr "Podnesi Povratne Informacije"

#: frontend/src/pages/PersonaForm.vue:43
msgid "Submit and Continue"
msgstr "Podnesi i Nastavi"

#: frontend/src/components/Modals/JobApplicationModal.vue:23
msgid "Submit your resume to proceed with your application for this position. Upon submission, it will be shared with the job poster."
msgstr "Pošalji svoj životopis kako biste nastavili sa prijavom za ovu poziciju. Nakon prijave, bit će podijeljen s objavom za posao."

#: lms/templates/livecode/extension_footer.html:85
#: lms/templates/livecode/extension_footer.html:115
msgid "Submitted {0}"
msgstr "Predano {0}"

#. Label of the summary (Small Text) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:97
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Summary"
msgstr "Sažetak"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Sunday"
msgstr "Nedjelja"

#: lms/lms/api.py:1076
msgid "Suspicious pattern found in {0}: {1}"
msgstr "Sumnjiva mustra pronađena u {0}: {1}"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/job_settings/job_settings.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/user_skill/user_skill.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "System Manager"
msgstr "Upravitelj Sistema"

#. Label of the tags (Data) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:51
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Tags"
msgstr "Oznake"

#: lms/templates/emails/community_course_membership.html:18
#: lms/templates/emails/mentor_request_creation_email.html:8
#: lms/templates/emails/mentor_request_status_update_email.html:7
msgid "Team School"
msgstr "Timska Škola"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Team Work"
msgstr "Timski Rad"

#. Label of the template (Link) field in DocType 'Cohort Web Page'
#. Label of the template (Link) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:43
#: frontend/src/components/Modals/Event.vue:112
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Template"
msgstr "Šablon"

#: lms/lms/user.py:40
msgid "Temporarily Disabled"
msgstr "Privremeno Onemogućeno"

#: lms/lms/utils.py:440
msgid "Terms of Use"
msgstr "Uslovi Korišćenja"

#. Label of the test_results (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the test_results (Small Text) field in DocType 'Exercise
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Test Results"
msgstr "Rezultati Testa"

#. Label of the tests (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Tests"
msgstr "Testovi"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Text"
msgstr "Tekst"

#: frontend/src/components/BatchFeedback.vue:6
msgid "Thank you for providing your feedback."
msgstr "Hvala vam na povratnim informacijama."

#: lms/templates/emails/lms_course_interest.html:17
#: lms/templates/emails/lms_invite_request_approved.html:15
#: lms/templates/emails/mentor_request_creation_email.html:7
#: lms/templates/emails/mentor_request_status_update_email.html:6
msgid "Thanks and Regards"
msgstr "Hvala i Pozdrav"

#: lms/lms/utils.py:1938
msgid "The batch is full. Please contact the Administrator."
msgstr "Grupa je popunjena. Kontaktiraj Administratora."

#: lms/templates/emails/batch_start_reminder.html:6
msgid "The batch you have enrolled for is starting tomorrow. Please be prepared and be on time for the session."
msgstr "Grupa za koju ste se prijavili počinje sutra. Pripremite se i dođete na vrijeme."

#: lms/templates/emails/lms_course_interest.html:5
msgid "The course {0} is now available on {1}."
msgstr "Kurs {0} je sada dostupan na {1}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:53
msgid "The evaluator of this course is unavailable from {0} to {1}. Please select a date after {1}"
msgstr "Ocjenjivač ovog kursa je nedostupan od {0} do {1}. Odaberi datum nakon {1}"

#: lms/templates/quiz/quiz.html:24
msgid "The quiz has a time limit. For each question you will be given {0} seconds."
msgstr "Kviz ima vremensko ograničenje. Za svako pitanje dobit ćete {0} sekundi."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:71
msgid "The slot is already booked by another participant."
msgstr "Termin je već rezervirao drugi učesnik."

#: lms/patches/create_mentor_request_email_templates.py:40
msgid "The status of your application has changed."
msgstr "Status vaše prijave je promijenjen."

#: frontend/src/components/CreateOutline.vue:12
msgid "There are no chapters in this course. Create and manage chapters from here."
msgstr "U ovom kursu nema poglavlja. Kreiraj i upravljaj poglavljima odavde."

#: lms/lms/doctype/lms_batch/lms_batch.py:107
msgid "There are no seats available in this batch."
msgstr "Nema slobodnih mjesta u ovoj grupi."

#: frontend/src/components/BatchStudents.vue:155
msgid "There are no students in this batch."
msgstr "U ovoj grupi nema studenata."

#: frontend/src/pages/AssignmentSubmissionList.vue:70
msgid "There are no submissions for this assignment."
msgstr "Ne postoje prijave za ovaj zadatak."

#: frontend/src/components/EmptyState.vue:11
msgid "There are no {0} currently. Keep an eye out, fresh learning experiences are on the way!"
msgstr "Trenutno nema {0}. Pratite nas, nova iskustva učenja su uskoro!"

#: lms/templates/course_list.html:14
msgid "There are no {0} on this site."
msgstr "Na ovoj stranici nema {0}."

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:44
msgid "There has been an update on your submission for assignment {0}"
msgstr "Došlo je do ažuriranja vaše prijave za zadatak {0}"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:59
msgid "There has been an update on your submission. You have got a score of {0} for the quiz {1}"
msgstr "Došlo je do ažuriranja vaše prijave. Dobili ste ocjenu {0} za kviz {1}"

#. Description of the 'section_break_ubxi' (Section Break) field in DocType
#. 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "These customisations will work on the main batch page."
msgstr "Ove prilagodbe će raditi na glavnoj stranici grupe."

#: frontend/src/pages/Badge.vue:14
msgid "This badge has been awarded to {0} on {1}."
msgstr "Ova značka je dodijeljena {0} {1}."

#. Label of the expire (Check) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "This certificate does no expire"
msgstr "Ovaj certifikat ne ističe"

#: frontend/src/components/LiveClass.vue:83
msgid "This class has ended"
msgstr "Ovaj čas je završen"

#: frontend/src/components/CourseCardOverlay.vue:100
msgid "This course has:"
msgstr "Ovaj kurs ima:"

#: lms/lms/utils.py:1781
msgid "This course is free."
msgstr "Ovaj kurs je besplatan."

#. Description of the 'Meta Description' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This description will be shown on lists and pages without meta description"
msgstr "Ovaj opis će biti prikazan na listama i stranicama bez meta opisa"

#. Description of the 'Meta Image' (Attach Image) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This image will be shown on lists and pages that don't have an image by default"
msgstr "Ova slika će biti prikazana na listama i stranicama koje nemaju sliku prema zadanim postavkama"

#: frontend/src/pages/Lesson.vue:24
msgid "This lesson is locked"
msgstr "Ova lekcija je zaključana"

#: frontend/src/pages/Lesson.vue:29
msgid "This lesson is not available for preview. Please enroll in the course to access it."
msgstr "Ova lekcija nije dostupna za pregled. Prijavi se na kurs da biste mu pristupili."

#: lms/lms/widgets/NoPreviewModal.html:16
msgid "This lesson is not available for preview. Please join the course to access it."
msgstr "Ova lekcija nije dostupna za pregled. Pridruži se kursu da biste mu pristupili."

#: frontend/src/components/Quiz.vue:11 lms/templates/quiz/quiz.html:6
msgid "This quiz consists of {0} questions."
msgstr "Ovaj kviz se sastoji od {0} pitanja."

#: frontend/src/components/AppSidebar.vue:75
#: frontend/src/components/AppSidebar.vue:115
msgid "This site is being updated. You will not be able to make any changes. Full access will be restored shortly."
msgstr "Ova stranica se ažurira. Nećete moći vršiti nikakve promjene. Puni pristup će uskoro biti vraćen."

#: frontend/src/components/VideoBlock.vue:5
msgid "This video contains {0} {1}:"
msgstr "Ovaj video sadrži {0} {1}:"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Thursday"
msgstr "Četvrtak"

#. Label of the time (Time) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:48
#: frontend/src/components/Modals/LiveClassModal.vue:52
#: frontend/src/components/Quiz.vue:48
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Time"
msgstr "Vrijeme"

#. Label of the time (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Time Preference"
msgstr "Vremenska Preferanca"

#: frontend/src/components/Modals/QuizInVideo.vue:13
msgid "Time in Video"
msgstr "Vrijeme u videu"

#: frontend/src/components/Modals/QuizInVideo.vue:220
msgid "Time in Video (minutes)"
msgstr "Vrijeme u videu (minute)"

#: frontend/src/components/Modals/QuizInVideo.vue:173
msgid "Time in video exceeds the total duration of the video."
msgstr "Vrijeme u videu premašuje ukupno trajanje videa."

#: frontend/src/components/Modals/LiveClassModal.vue:44
msgid "Time must be in 24 hour format (HH:mm). Example 11:30 or 22:00"
msgstr "Vrijeme mora biti u 24-satnom formatu (HH:mm). Primjer 11:30 ili 22:00"

#. Label of the schedule_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Timetable Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable"
msgstr "Vremenska Tabela"

#. Label of the timetable_legends (Table) field in DocType 'LMS Batch'
#. Label of the timetable_legends (Table) field in DocType 'LMS Timetable
#. Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable Legends"
msgstr "Vremenska Legenda"

#. Label of the timetable_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Timetable Template"
msgstr "Vremenski Šablon"

#. Label of the timezone (Data) field in DocType 'LMS Batch'
#. Label of the timezone (Data) field in DocType 'LMS Certificate Request'
#. Label of the timezone (Data) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:59
#: frontend/src/pages/BatchForm.vue:107
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Timezone"
msgstr "Vremenska Zona"

#: lms/templates/emails/batch_confirmation.html:21
#: lms/templates/emails/batch_start_reminder.html:16
#: lms/templates/emails/live_class_reminder.html:16
msgid "Timings:"
msgstr "Vremena:"

#. Label of the title (Data) field in DocType 'Cohort'
#. Label of the title (Data) field in DocType 'Cohort Subgroup'
#. Label of the title (Data) field in DocType 'Cohort Web Page'
#. Label of the title (Data) field in DocType 'Course Chapter'
#. Label of the title (Data) field in DocType 'Course Lesson'
#. Label of the title (Data) field in DocType 'LMS Assignment'
#. Label of the title (Data) field in DocType 'LMS Badge'
#. Label of the title (Data) field in DocType 'LMS Batch'
#. Label of the title (Data) field in DocType 'LMS Batch Old'
#. Label of the title (Data) field in DocType 'LMS Course'
#. Label of the title (Data) field in DocType 'LMS Exercise'
#. Label of the title (Data) field in DocType 'LMS Live Class'
#. Label of the title (Data) field in DocType 'LMS Program'
#. Label of the title (Data) field in DocType 'LMS Quiz'
#. Label of the title (Data) field in DocType 'LMS Sidebar Item'
#. Label of the title (Data) field in DocType 'LMS Timetable Template'
#. Label of the title (Data) field in DocType 'Work Experience'
#: frontend/src/components/Modals/AssignmentForm.vue:20
#: frontend/src/components/Modals/DiscussionModal.vue:18
#: frontend/src/components/Modals/LiveClassModal.vue:23
#: frontend/src/pages/Assignments.vue:165 frontend/src/pages/BatchForm.vue:20
#: frontend/src/pages/CourseForm.vue:30 frontend/src/pages/JobForm.vue:20
#: frontend/src/pages/ProgramForm.vue:11 frontend/src/pages/Programs.vue:101
#: frontend/src/pages/QuizForm.vue:48 frontend/src/pages/Quizzes.vue:120
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Title"
msgstr "Naziv"

#: frontend/src/components/Modals/ChapterModal.vue:172
msgid "Title is required"
msgstr "Naziv je obavezan"

#. Label of the unavailable_to (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:112
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "To"
msgstr "Do"

#. Label of the to_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "To Date"
msgstr "Do Datuma"

#: lms/lms/utils.py:1792
msgid "To join this batch, please contact the Administrator."
msgstr "Da biste se pridružili ovoj grupi, kontaktirajte administratora."

#: lms/lms/user.py:41
msgid "Too many users signed up recently, so the registration is disabled. Please try back in an hour"
msgstr "Nedavno se prijavilo previše korisnika, pa je registracija onemogućena. Pokušajte ponovo za sat vremena"

#: frontend/src/pages/Billing.vue:53
msgid "Total"
msgstr "Ukupno"

#. Label of the total_marks (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:67 frontend/src/pages/Quizzes.vue:125
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Total Marks"
msgstr "Ukupno Bodova"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:12
msgid "Total Signups"
msgstr "Ukupno Prijava"

#: frontend/src/components/Modals/FeedbackModal.vue:11
msgid "Training Feedback"
msgstr "Povratna Informacija Obuke"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Travel"
msgstr "Putovanja"

#: frontend/src/components/Quiz.vue:274 lms/templates/quiz/quiz.html:131
msgid "Try Again"
msgstr "Pokušaj ponovo"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Tuesday"
msgstr "Utorak"

#: frontend/src/pages/ProfileAbout.vue:86
msgid "Twitter"
msgstr "Twitter"

#. Label of the type (Select) field in DocType 'Job Opportunity'
#. Label of the type (Select) field in DocType 'LMS Assignment'
#. Label of the type (Select) field in DocType 'LMS Assignment Submission'
#. Label of the type (Select) field in DocType 'LMS Question'
#. Label of the type (Select) field in DocType 'LMS Quiz Question'
#: frontend/src/components/Modals/AssessmentModal.vue:22
#: frontend/src/components/Modals/Question.vue:44
#: frontend/src/pages/Assignments.vue:43 frontend/src/pages/Assignments.vue:170
#: frontend/src/pages/JobForm.vue:25 frontend/src/pages/Jobs.vue:65
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/templates/assessments.html:14
msgid "Type"
msgstr "Tip"

#: frontend/src/utils/markdownParser.js:11
msgid "Type '/' for commands or select text to format"
msgstr "Upišite '/' za naredbe ili odaberite tekst za format"

#: frontend/src/components/Quiz.vue:636
msgid "Type your answer"
msgstr "Unesi svoj odgovor"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "UK Grading  (e.g. 1st, 2:2)"
msgstr "UK Ocjenjivanje (npr. 1., 2:2)"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "URL"
msgstr "URL"

#. Label of the uuid (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "UUID"
msgstr "UUID"

#. Label of the unavailability_section (Section Break) field in DocType 'Course
#. Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Unavailability"
msgstr "Nedostupnost"

#: frontend/src/pages/ProfileEvaluator.vue:259
msgid "Unavailability updated successfully"
msgstr "Nedostupnost je uspješno ažurirana"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:29
msgid "Unavailable From Date cannot be greater than Unavailable To Date"
msgstr "Nedostupno Od Datuma ne može biti kasnije od Nedostupno Do Datuma"

#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Under Review"
msgstr "Pod Recenzijom"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Unlisted"
msgstr "Neizlistane"

#: frontend/src/pages/Batches.vue:284 frontend/src/pages/Courses.vue:322
msgid "Unpublished"
msgstr "Neobjavljeno"

#: frontend/src/components/Modals/EditCoverImage.vue:60
#: frontend/src/components/UnsplashImageBrowser.vue:54
msgid "Unsplash"
msgstr "Unsplash"

#. Label of the unsplash_access_key (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Unsplash Access Key"
msgstr "Unsplash Pristupni Ključ"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Unstructured Role"
msgstr "Nestrukturirana Uloga"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#. Label of the upcoming (Check) field in DocType 'LMS Course'
#: frontend/src/pages/Batches.vue:282 frontend/src/pages/CourseForm.vue:162
#: frontend/src/pages/Courses.vue:313 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Upcoming"
msgstr "Nadolazeći"

#: frontend/src/pages/Batch.vue:187
msgid "Upcoming Batches"
msgstr "Nadolazeće Grupe"

#: frontend/src/components/UpcomingEvaluations.vue:5
#: lms/templates/upcoming_evals.html:3
msgid "Upcoming Evaluations"
msgstr "Nadolazeća Ocjenjivanja"

#: frontend/src/components/Settings/BrandSettings.vue:24
#: frontend/src/components/Settings/PaymentSettings.vue:27
#: frontend/src/components/Settings/SettingDetails.vue:23
msgid "Update"
msgstr "Ažuriraj"

#: lms/templates/emails/community_course_membership.html:11
msgid "Update Password"
msgstr "Ažuriraj Lozinku"

#: frontend/src/pages/BatchForm.vue:217 frontend/src/pages/CourseForm.vue:108
msgid "Upload"
msgstr "Učitaj"

#: frontend/src/components/Assignment.vue:81
msgid "Upload File"
msgstr "Učitaj Datoteku"

#: frontend/src/components/Assignment.vue:80
msgid "Uploading {0}%"
msgstr "Prijenos {0}%"

#: frontend/src/components/Modals/EmailTemplateModal.vue:38
msgid "Use HTML"
msgstr "Koristi HTML"

#. Label of the user (Link) field in DocType 'LMS Job Application'
#. Label of the email (Link) field in DocType 'Cohort Staff'
#. Label of the user (Link) field in DocType 'LMS Course Interest'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "User"
msgstr "Korisnik"

#. Label of the user_category (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:17
msgid "User Category"
msgstr "Kategorija Korisnika"

#. Label of the user_field (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "User Field"
msgstr "Korisničko polje"

#. Label of the user_image (Attach Image) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "User Image"
msgstr "Slika korisnika"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "User Input"
msgstr "Unos Korisnika"

#. Name of a DocType
#: lms/lms/doctype/user_skill/user_skill.json
msgid "User Skill"
msgstr "Vještina Korisnika"

#: lms/job/doctype/job_opportunity/job_opportunity.py:40
msgid "User {0} has reported the job post {1}"
msgstr "Korisnik {0} je prijavio posao {1}"

#. Label of the username (Data) field in DocType 'Course Evaluator'
#. Label of the username (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Username"
msgstr "Korisničko Ime"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Users"
msgstr "Korisnici"

#. Label of the answer (Small Text) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Users Response"
msgstr "Odgovor Korisnika"

#: lms/templates/signup-form.html:83
msgid "Valid email and name required"
msgstr "Važeća adresa e-pošte i ime je obavezna"

#. Label of the value (Rating) field in DocType 'LMS Batch Feedback'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Value"
msgstr "Vrijednost"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Value Change"
msgstr "Promjena Vrijednosti"

#. Label of the video_link (Data) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Video Embed Link"
msgstr "Ugradiva Video Veza"

#: frontend/src/pages/Notifications.vue:39
msgid "View"
msgstr "Pogled"

#: frontend/src/components/CertificationLinks.vue:10
#: frontend/src/components/Modals/Event.vue:67
msgid "View Certificate"
msgstr "Prikaži Certifikat"

#: frontend/src/components/BatchFeedback.vue:56
msgid "View all feedback"
msgstr "Pogledaj sve povratne informacije"

#. Label of the visibility (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Visibility"
msgstr "Vidljivost"

#: frontend/src/components/BatchOverlay.vue:69
msgid "Visit Batch"
msgstr "Posjeti Grupu"

#: frontend/src/pages/JobDetail.vue:41
msgid "Visit Website"
msgstr "Posjeti Web Stranicu"

#: lms/templates/emails/batch_confirmation.html:25
msgid "Visit the following link to view your "
msgstr "Posjeti sljedeću vezu da vidite svoje "

#: lms/templates/emails/batch_start_reminder.html:23
#: lms/templates/emails/live_class_reminder.html:20
msgid "Visit your batch"
msgstr "Posjeti svoju grupu"

#. Label of the internship (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Volunteering or Internship"
msgstr "Volontiranje ili Stažiranje"

#: lms/templates/emails/batch_confirmation.html:6
msgid "We are pleased to inform you that you have been enrolled in our upcoming batch. Congratulations!"
msgstr "Sa zadovoljstvom vas obavještavamo da ste upisani u našu nadolazeću grupu. Čestitamo!"

#: lms/templates/emails/payment_reminder.html:7
msgid "We have a limited number of seats, and they won't be available for long!"
msgstr "Imamo ograničen broj mjesta i neće još dugo biti slobodna!"

#: lms/templates/emails/payment_reminder.html:4
msgid "We noticed that you started enrolling in the"
msgstr "Primijetili smo da ste se počeli s upisom"

#. Label of the web_page (Link) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:23
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Web Page"
msgstr "Web Stranica"

#: frontend/src/components/Modals/PageModal.vue:80
msgid "Web page added to sidebar"
msgstr "Web stranica je dodana u bočnu traku"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Wednesday"
msgstr "Srijeda"

#: lms/lms/doctype/invite_request/invite_request.py:40
#: lms/templates/emails/lms_invite_request_approved.html:4
msgid "Welcome to {0}!"
msgstr "Dobrodošli u {0}!"

#: frontend/src/pages/PersonaForm.vue:32
msgid "What best describes your role?"
msgstr "Šta najbolje opisuje vašu ulogu?"

#: frontend/src/components/LessonHelp.vue:6
msgid "What does include in preview mean?"
msgstr "Šta znači uključi u pregled?"

#: frontend/src/pages/PersonaForm.vue:21
msgid "What is your use case for Frappe Learning?"
msgstr "Koji je vaš slučaj upotrebe Frappe Učenja?"

#: lms/templates/courses_under_review.html:15
msgid "When a course gets submitted for review, it will be listed here."
msgstr "Kada se kurs pošalje na pregled, biće naveden ovdje."

#: frontend/src/pages/Billing.vue:106
msgid "Where did you hear about us?"
msgstr "Gdje ste čuli za nas?"

#: lms/templates/emails/certification.html:10
msgid "With this certification, you can now showcase your updated skills and share your achievement with your colleagues and on LinkedIn. To access your certificate, please click on the link provided below. Make sure you are logged in to the portal."
msgstr "Uz ovaj certifikat, sada možete pokazati svoje ažurirane vještine i podijeliti svoja postignuća sa svojim kolegama i na LinkedIn. Da biste pristupili svom certifikatu, kliknite na donji link. Provjerite jeste li prijavljeni na portal."

#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Withdrawn"
msgstr "Povučen"

#. Label of the work_environment (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Environment"
msgstr "Radno Okruženje"

#. Label of the work_experience (Table) field in DocType 'User'
#. Name of a DocType
#: lms/fixtures/custom_field.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Work Experience"
msgstr "Radno Iskustvo"

#. Label of the work_experience_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Experience Details"
msgstr "Detalji Radnog Iskustva"

#: frontend/src/components/CourseReviews.vue:8
#: frontend/src/components/Modals/ReviewModal.vue:5
#: lms/templates/reviews.html:117
msgid "Write a Review"
msgstr "Napiši Recenziju"

#: lms/templates/reviews.html:31 lms/templates/reviews.html:103
#: lms/templates/reviews_cta.html:3 lms/templates/reviews_cta.html:7
msgid "Write a review"
msgstr "Napiši Recenziju"

#: frontend/src/components/Assignment.vue:123
msgid "Write your answer here"
msgstr "Napišite svoj odgovor ovdje"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:95
msgid "You already have an evaluation on {0} at {1} for the course {2}."
msgstr "Već imate ocjenjivanje na {0} na {1} za kurs {2}."

#: frontend/src/pages/CourseCertification.vue:14
msgid "You are already certified for this course. Click on the card below to open your certificate."
msgstr "Već ste certificirani za ovaj kurs. Klikni na karticu ispod da otvorite svoj certifikat."

#: lms/lms/api.py:234
msgid "You are already enrolled for this batch."
msgstr "Već ste upisani za ovu grupu."

#: lms/lms/api.py:226
msgid "You are already enrolled for this course."
msgstr "Već ste upisani za ovaj kurs."

#: frontend/src/pages/Batch.vue:169
msgid "You are not a member of this batch. Please checkout our upcoming batches."
msgstr "Niste član ove grupe. Provjerite naše nadolazeće grupe."

#: lms/lms/doctype/lms_batch_old/lms_batch_old.py:20
msgid "You are not a mentor of the course {0}"
msgstr "Niste mentor kursa {0}"

#: frontend/src/pages/SCORMChapter.vue:22
msgid "You are not enrolled in this course. Please enroll to access this lesson."
msgstr "Niste upisani na ovaj kurs. Prijavi se za pristup ovoj lekciji."

#: lms/templates/emails/lms_course_interest.html:13
#: lms/templates/emails/lms_invite_request_approved.html:11
msgid "You can also copy-paste following link in your browser"
msgstr "Takođ možete kopirati i zalijepiti sljedeću vezu u svoj preglednik"

#: lms/templates/quiz/quiz.html:18
msgid "You can attempt this quiz only {0} {1}"
msgstr "Ovaj kviz možete pokušati samo {0} {1}"

#: frontend/src/components/Quiz.vue:37
msgid "You can attempt this quiz {0}."
msgstr "Možete isprobati ovaj kviz {0}."

#: lms/templates/emails/job_application.html:6
msgid "You can find their resume attached to this email."
msgstr "Njihov životopis možete pronaći u prilogu ove e-pošte."

#: frontend/src/pages/ProfileEvaluator.vue:14
msgid "You cannot change the availability when the site is being updated."
msgstr "Ne možete promijeniti dostupnost dok se stranica ažurira."

#: frontend/src/pages/ProfileRoles.vue:12
msgid "You cannot change the roles in read-only mode."
msgstr "Ne možete mijenjati uloge u načinu rada samo za čitanje."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:115
msgid "You cannot schedule evaluations after {0}."
msgstr "Ne možete zakazati ocjenjivanje nakon {0}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:104
msgid "You cannot schedule evaluations for past slots."
msgstr "Ne možete zakazati ocjenjivanje za prošle termine."

#: frontend/src/components/NoPermission.vue:11
msgid "You do not have permission to access this page."
msgstr "Nemate dozvolu za pristup ovoj stranici."

#: lms/templates/notifications.html:27
msgid "You don't have any notifications."
msgstr "Nemate nikakva obavještenja."

#: lms/templates/quiz/quiz.js:137
msgid "You got"
msgstr "Imaš"

#: frontend/src/components/Quiz.vue:255
#, python-format
msgid "You got {0}% correct answers with a score of {1} out of {2}"
msgstr "Imate {0}% tačnih odgovora sa rezultatom {1} od {2}"

#: lms/templates/emails/live_class_reminder.html:6
msgid "You have a live class scheduled tomorrow. Please be prepared and be on time for the session."
msgstr "Sutra imate zakazan čas uživo. Pripremi i dođi na vrijeme za čas."

#: lms/job/doctype/lms_job_application/lms_job_application.py:22
msgid "You have already applied for this job."
msgstr "Već ste se prijavili za ovaj posao."

#: frontend/src/components/Quiz.vue:86 lms/templates/quiz/quiz.html:43
msgid "You have already exceeded the maximum number of attempts allowed for this quiz."
msgstr "Već ste premašili maksimalan broj pokušaja dozvoljen za ovaj kviz."

#: lms/lms/api.py:258
msgid "You have already purchased the certificate for this course."
msgstr "Već ste kupili sertifikat za ovaj kurs."

#: lms/lms/doctype/lms_course_review/lms_course_review.py:17
msgid "You have already reviewed this course"
msgstr "Već ste pregledali ovaj kurs"

#: frontend/src/pages/JobDetail.vue:57
msgid "You have applied"
msgstr "Prijavili ste se"

#: frontend/src/components/BatchOverlay.vue:159
msgid "You have been enrolled in this batch"
msgstr "Upisani ste u ovu grupu"

#: frontend/src/components/CourseCardOverlay.vue:187
msgid "You have been enrolled in this course"
msgstr "Upisani ste na ovaj kurs"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:30
msgid "You have exceeded the maximum number of attempts ({0}) for this quiz"
msgstr "Premašili ste maksimalan broj pokušaja ({0}) za ovaj kviz"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:56
msgid "You have got a score of {0} for the quiz {1}"
msgstr "Imate ocjenu {0} za kviz {1}"

#: lms/lms/widgets/NoPreviewModal.html:12
msgid "You have opted to be notified for this course. You will receive an email when the course becomes available."
msgstr "Odlučili ste da budete obaviješteni o ovom kursu. Primit ćete e-poštu kada kurs postane dostupan."

#: frontend/src/components/CourseCardOverlay.vue:175
msgid "You need to login first to enroll for this course"
msgstr "Prvo se prijavite da biste se upisali na ovaj kurs"

#: frontend/src/components/Quiz.vue:7
msgid "You will have to complete the quiz to continue the video"
msgstr "Morat ćete završiti kviz kako biste nastavili gledati video"

#: frontend/src/components/Quiz.vue:30 lms/templates/quiz/quiz.html:11
#, python-format
msgid "You will have to get {0}% correct answers in order to pass the quiz."
msgstr "Trebate da imate {0}% tačnih odgovora da biste prošli kviz."

#: lms/templates/emails/mentor_request_creation_email.html:4
msgid "You've applied to become a mentor for this course. Your request is currently under review."
msgstr "Prijavili ste se da postanete mentor za ovaj kurs. Vaš zahtjev se trenutno razmatra."

#: frontend/src/components/Assignment.vue:58
msgid "You've successfully submitted the assignment."
msgstr "Uspješno ste predali zadatak."

#. Label of the youtube (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video URL"
msgstr "URL YouTube Videa"

#. Description of the 'YouTube Video URL' (Data) field in DocType 'Course
#. Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video will appear at the top of the lesson."
msgstr "YouTube video će se pojaviti na vrhu lekcije."

#: lms/www/new-sign-up.html:56
msgid "Your Account has been successfully created!"
msgstr "Vaš račun je uspješno kreiran!"

#: lms/lms/doctype/lms_batch/lms_batch.py:362
msgid "Your batch {0} is starting tomorrow"
msgstr "Vaša grupa {0} počinje sutra"

#: frontend/src/pages/ProfileEvaluator.vue:134
msgid "Your calendar is set."
msgstr "Vaš kalendar je postavljen."

#: lms/lms/doctype/lms_live_class/lms_live_class.py:90
msgid "Your class on {0} is today"
msgstr "Vaš čas {0} je danas"

#: frontend/src/components/Modals/EmailTemplateModal.vue:35
msgid "Your enrollment in {{ batch_name }} is confirmed"
msgstr "Vaš upis u {{ batch_name }} je potvrđen"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:3
#: lms/templates/emails/certificate_request_notification.html:3
msgid "Your evaluation for the course {0} has been scheduled on {1} at {2} {3}."
msgstr "Vaša ocjena za kurs {0} je zakazana za {1} u {2} {3}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:125
msgid "Your evaluation slot has been booked"
msgstr "Vaš termin za ocjenjivanje je rezervisan"

#: lms/templates/emails/certificate_request_notification.html:5
msgid "Your evaluator is {0}"
msgstr "Vaš ocjenjivač je {0}"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "Your request to join us as a mentor for the course"
msgstr "Vaš zahtjev da nam se pridružite kao mentor za kurs"

#: lms/templates/quiz/quiz.js:140
msgid "Your score is"
msgstr "Tvoj rezultat je"

#: frontend/src/components/Quiz.vue:248
msgid "Your submission has been successfully saved. The instructor will review and grade it shortly, and you'll be notified of your final result."
msgstr "Vaša prijava je uspješno sačuvana. Instruktor će ga uskoro pregledati i ocijeniti, a vi ćete biti obaviješteni o vašem konačnom rezultatu."

#: frontend/src/pages/Lesson.vue:8
msgid "Zen Mode"
msgstr "Zen Način"

#. Label of the zoom_account (Link) field in DocType 'LMS Batch'
#. Label of the zoom_account (Link) field in DocType 'LMS Live Class'
#: frontend/src/pages/BatchForm.vue:164
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Zoom Account"
msgstr "Zoom račun"

#: frontend/src/components/Modals/ZoomAccountModal.vue:158
msgid "Zoom Account created successfully"
msgstr "Zoom račun uspješno kreiran"

#: frontend/src/components/Modals/ZoomAccountModal.vue:195
msgid "Zoom Account updated successfully"
msgstr "Zoom račun uspješno ažuriran"

#. Name of a DocType
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Zoom Settings"
msgstr "Zoom Postavke"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activities"
msgstr "aktivnosti"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activity"
msgstr "aktivnost"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicant"
msgstr "podnositelj"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicants"
msgstr "podnositelji"

#: frontend/src/components/VideoBlock.vue:15
msgid "at {0} minutes"
msgstr "za {0} minuta"

#: lms/templates/emails/payment_reminder.html:4
msgid "but didn’t complete your payment"
msgstr "ali niste izvršili uplatu"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "cancel your application"
msgstr "otkaži svoju prijavu"

#: frontend/src/pages/CertifiedParticipants.vue:79
msgid "certificate"
msgstr "certifikat"

#: frontend/src/pages/CertifiedParticipants.vue:78
msgid "certificates"
msgstr "certifikati"

#: frontend/src/pages/CertifiedParticipants.vue:18
msgid "certified members"
msgstr "certificirani članovi"

#: frontend/src/pages/Lesson.vue:92 frontend/src/pages/Lesson.vue:249
msgid "completed"
msgstr "završeno"

#: lms/templates/quiz/quiz.js:137
msgid "correct answers"
msgstr "tačne odgovore"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "has been"
msgstr "je bio"

#: frontend/src/components/StudentHeatmap.vue:8
msgid "in the last"
msgstr "u zadnje"

#: lms/templates/signup-form.html:12
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/pages/Programs.vue:31
msgid "member"
msgstr "član"

#: frontend/src/pages/Programs.vue:31
msgid "members"
msgstr "članovi"

#: frontend/src/components/Modals/LiveClassAttendance.vue:57
msgid "minutes"
msgstr "minuta"

#: lms/templates/quiz/quiz.html:106
msgid "of"
msgstr "od"

#: lms/templates/quiz/quiz.js:141
msgid "out of"
msgstr "od"

#: frontend/src/pages/QuizForm.vue:391
msgid "question_detail"
msgstr "pitanje_detalj"

#: lms/templates/reviews.html:25
msgid "ratings"
msgstr "ocjene"

#: frontend/src/components/Settings/Categories.vue:19
msgid "saving..."
msgstr "spremanje u toku..."

#: lms/templates/reviews.html:43
msgid "stars"
msgstr "zvijezde"

#: frontend/src/components/BatchFeedback.vue:12
msgid "to view your feedback."
msgstr "da biste vidjeli svoje povratne informacije."

#: frontend/src/components/StudentHeatmap.vue:10
msgid "weeks"
msgstr "sedmice"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "you can"
msgstr "možeš"

#: frontend/src/pages/Assignments.vue:29
msgid "{0} Assignments"
msgstr "{0} Zadataka"

#: frontend/src/pages/Jobs.vue:32
msgid "{0} Open Jobs"
msgstr "{0} Otvoreni Poslovi"

#: frontend/src/pages/Quizzes.vue:25
msgid "{0} Quizzes"
msgstr "{0} Kvizova"

#: lms/lms/api.py:866 lms/lms/api.py:874
msgid "{0} Settings not found"
msgstr "{0} Postavke nisu pronađene"

#: lms/templates/emails/job_application.html:2
msgid "{0} has applied for the job position {1}"
msgstr "{0} se prijavio za posao {1}"

#: lms/templates/emails/job_report.html:4
msgid "{0} has reported a job post for the following reason."
msgstr "{0} je prijavio posao iz sljedećeg razloga."

#: lms/templates/emails/assignment_submission.html:2
msgid "{0} has submitted the assignment {1}"
msgstr "{0} je poslao zadatak {1}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:57
msgid "{0} is already a Student of {1} course through {2} batch"
msgstr "{0} je već Student {1} kursa kroz {2} grupu"

#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.py:16
msgid "{0} is already a mentor for course {1}"
msgstr "{0} je već mentor za kurs {1}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:30
msgid "{0} is already a {1} of the course {2}"
msgstr "{0} je već {1} kursa {2}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:91
msgid "{0} is already certified for the batch {1}"
msgstr "{0} je već certificiran za grupu {1}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:72
msgid "{0} is already certified for the course {1}"
msgstr "{0} je već certificiran za kurs {1}"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:5
msgid "{0} is your evaluator"
msgstr "{0} je vaš ocjenjivač"

#: lms/lms/utils.py:687
msgid "{0} mentioned you in a comment"
msgstr "{0} vas je spomenuo u komentaru"

#: lms/templates/emails/mention_template.html:2
msgid "{0} mentioned you in a comment in your batch."
msgstr "{0} vas je spomenuo u komentaru u vašoj grupi."

#: lms/lms/utils.py:640 lms/lms/utils.py:646
msgid "{0} mentioned you in a comment in {1}"
msgstr "{0} vas je spomenuo u komentaru u {1}"

#: lms/lms/utils.py:462
msgid "{0}k"
msgstr "{0}k"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Active"
msgstr "{} Aktivan"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Completed"
msgstr "{} Završeno"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Enrolled"
msgstr "{} Upisan"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Granted"
msgstr "{} Odobreno"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Passed"
msgstr "{} Prošlo"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Published"
msgstr "{} Objavljeno"

