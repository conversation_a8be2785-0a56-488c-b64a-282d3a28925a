msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-06-20 16:04+0000\n"
"PO-Revision-Date: 2025-06-23 16:55\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Serbian (Cyrillic)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: sr\n"
"X-Crowdin-File: /[frappe.lms] develop/lms/locale/main.pot\n"
"X-Crowdin-File-ID: 90\n"
"Language: sr_SP\n"

#: lms/templates/emails/assignment_submission.html:5
msgid " Please evaluate and grade it."
msgstr " Молимо Вас да прегледате и оцените."

#: frontend/src/pages/Programs.vue:39
#, python-format
msgid "% completed"
msgstr "% завршено"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/lms-settings/LMS%20Settings\">LMS Settings</a>"
msgstr "<a href=\"/app/lms-settings/LMS%20Settings\">LMS подешавања</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/web-page/new-web-page-1\">Setup a Home Page</a>"
msgstr "<a href=\"/app/web-page/new-web-page-1\">Поставите почетну страницу</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses\">Visit LMS Portal</a>"
msgstr "<a href=\"/lms/courses\">Посетите LMS портал</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses/new/edit\">Create a Course</a>"
msgstr "<a href=\"/lms/courses/new/edit\">Креирај обуку</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"https://docs.frappe.io/learning\">Documentation</a>"
msgstr "<a href=\"https://docs.frappe.io/learning\">Документација</a>"

#: frontend/src/components/Modals/EmailTemplateModal.vue:50
msgid "<p>Dear {{ member_name }},</p>\\n\\n<p>You have been enrolled in our upcoming batch {{ batch_name }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappe Learning</p>"
msgstr "<p>Здраво {{ member_name }},</p>\\n\\n<p>Уписани сте у нашу предстојећу групу {{ batch_name }}.</p>\\n\\n<p>Хвала Вам,,</p>\\n<p>Frappe Learning</p>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Get Started</b></span>"
msgstr "<span class=\"h4\"><b>Започните</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Master</b></span>"
msgstr "<span class=\"h4\"><b>Мастер подаци</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span style=\"font-size: 18px;\"><b>Statistics</b></span>"
msgstr "<span style=\"font-size: 18px;\"><b>Статистика</b></span>"

#: lms/lms/doctype/lms_course/lms_course.py:63
msgid "A course cannot have both paid certificate and certificate of completion."
msgstr "Обука не може имати истовремено плаћени сертификат и сертификат о похађању."

#: frontend/src/pages/CourseForm.vue:82
msgid "A one line introduction to the course that appears on the course card"
msgstr "Кратак опис курса који се појављује на картици обуке"

#: frontend/src/pages/ProfileAbout.vue:4
msgid "About"
msgstr "О"

#: frontend/src/pages/Batch.vue:101
msgid "About this batch"
msgstr "О овој групи"

#. Label of the verify_terms (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Acceptance for Terms and/or Policies"
msgstr "Прихватање услова и/или правила"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Accepted"
msgstr "Прихваћено"

#: frontend/src/components/Settings/ZoomSettings.vue:174
msgid "Account"
msgstr "Рачун"

#. Label of the account_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the account_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:55
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Account ID"
msgstr "ИД налога"

#. Label of the account_name (Data) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:30
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Account Name"
msgstr "Назив рачуна"

#: frontend/src/pages/ProfileAbout.vue:17
msgid "Achievements"
msgstr "Достигнућа"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Active"
msgstr "Активан"

#: frontend/src/pages/Statistics.vue:16
msgid "Active Members"
msgstr "Активни чланови"

#: frontend/src/components/Assessments.vue:11
#: frontend/src/components/BatchCourses.vue:11
#: frontend/src/components/BatchStudents.vue:73
#: frontend/src/components/LiveClass.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:29
#: frontend/src/components/Settings/Categories.vue:43
#: frontend/src/components/Settings/Evaluators.vue:39
#: frontend/src/components/Settings/Members.vue:44
#: frontend/src/pages/ProgramForm.vue:30 frontend/src/pages/ProgramForm.vue:92
#: frontend/src/pages/ProgramForm.vue:137
msgid "Add"
msgstr "Додај"

#: frontend/src/components/CourseOutline.vue:18
#: frontend/src/components/CreateOutline.vue:18
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Add Chapter"
msgstr "Додај поглавље"

#: frontend/src/components/CourseOutline.vue:133
msgid "Add Lesson"
msgstr "Додај лекцију"

#: frontend/src/components/VideoBlock.vue:121
msgid "Add Quiz to Video"
msgstr "Додај квиз у видео-снимак"

#: frontend/src/pages/ProfileEvaluator.vue:89
msgid "Add Slot"
msgstr "Додај термин"

#: lms/templates/onboarding_header.html:26
msgid "Add a Chapter"
msgstr "Додај поглавље"

#: lms/templates/onboarding_header.html:33
msgid "Add a Lesson"
msgstr "Додај лекцију"

#: frontend/src/components/Modals/StudentModal.vue:5
msgid "Add a Student"
msgstr "Додај студента"

#: frontend/src/components/AppSidebar.vue:567
msgid "Add a chapter"
msgstr "Додај поглавље"

#: frontend/src/components/Modals/BatchCourseModal.vue:5
msgid "Add a course"
msgstr "Додај обуку"

#: frontend/src/pages/CourseForm.vue:67
msgid "Add a keyword and then press enter"
msgstr "Додај кључну реч, а затим притисни ентер"

#: frontend/src/components/AppSidebar.vue:568
msgid "Add a lesson"
msgstr "Додај лекцију"

#: frontend/src/components/Modals/Question.vue:163
#: frontend/src/pages/QuizForm.vue:183
msgid "Add a new question"
msgstr "Додај ново питање"

#: frontend/src/components/AssessmentPlugin.vue:11
msgid "Add a quiz to your lesson"
msgstr "Додајте квиз у своју лекцију"

#: frontend/src/components/Modals/AssessmentModal.vue:5
msgid "Add an assessment"
msgstr "Додај процену"

#: frontend/src/components/AssessmentPlugin.vue:14
msgid "Add an assignment to your lesson"
msgstr "Додајте задатак у своју лекцију"

#: lms/lms/doctype/lms_question/lms_question.py:66
msgid "Add at least one possible answer for this question: {0}"
msgstr "Додајте бар један могући одговор за ово питање: {0}"

#: frontend/src/components/AppSidebar.vue:531
msgid "Add courses to your batch"
msgstr "Додајте обуке у Вашу групу"

#: frontend/src/components/Modals/QuizInVideo.vue:5
msgid "Add quiz to this video"
msgstr "Додај квиз у овај видео-снимак"

#: frontend/src/components/AppSidebar.vue:510
msgid "Add students to your batch"
msgstr "Додајте студенте у своју групу"

#: frontend/src/components/Modals/PageModal.vue:6
msgid "Add web page to sidebar"
msgstr "Додајте веб-страницу у бочну траку"

#: frontend/src/components/Assignment.vue:68
msgid "Add your assignment as {0}"
msgstr "Додајте свој задатак као {0}"

#: frontend/src/components/AppSidebar.vue:443
msgid "Add your first chapter"
msgstr "Додајте Ваше прво поглавље"

#: frontend/src/components/AppSidebar.vue:459
msgid "Add your first lesson"
msgstr "Додајте Вашу прву лекцију"

#. Label of the address (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:64
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Address"
msgstr "Адреса"

#: frontend/src/pages/Billing.vue:74
msgid "Address Line 1"
msgstr "Адреса, ред 1"

#: frontend/src/pages/Billing.vue:78
msgid "Address Line 2"
msgstr "Адреса, ред 2"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Admin"
msgstr "Администратор"

#. Name of a role
#: frontend/src/pages/Batches.vue:273 lms/lms/doctype/lms_badge/lms_badge.json
msgid "All"
msgstr "Све"

#: frontend/src/pages/Batches.vue:26
msgid "All Batches"
msgstr "Све групе"

#: frontend/src/pages/Courses.vue:26 lms/lms/widgets/BreadCrumb.html:3
msgid "All Courses"
msgstr "Додај обуке"

#: lms/templates/quiz/quiz.html:141
msgid "All Submissions"
msgstr "Све поднесено"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:44
msgid "All questions should have the same marks if the limit is set."
msgstr "Сва питања треба да имају исте оцене уколико је постављено ограничење."

#. Label of the allow_guest_access (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Allow Guest Access"
msgstr "Дозволи приступ гостима"

#. Label of the allow_posting (Check) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Allow Job Posting From Website"
msgstr "Дозволи огласе за посао са веб-сајта"

#. Label of the allow_self_enrollment (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow Self Enrollment"
msgstr "Дозволи самостални упис"

#. Label of the allow_future (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow accessing future dates"
msgstr "Дозволи приступ будућим датумима"

#: frontend/src/pages/BatchForm.vue:57
msgid "Allow self enrollment"
msgstr "Дозволи самостални упис"

#: lms/lms/user.py:34
msgid "Already Registered"
msgstr "Већ регистрован"

#. Label of the amount (Currency) field in DocType 'LMS Batch'
#. Label of the course_price (Currency) field in DocType 'LMS Course'
#. Label of the amount (Currency) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:265 frontend/src/pages/CourseForm.vue:245
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount"
msgstr "Износ"

#. Label of the amount_usd (Currency) field in DocType 'LMS Batch'
#. Label of the amount_usd (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Amount (USD)"
msgstr "Износ (USD)"

#: lms/lms/doctype/lms_batch/lms_batch.py:70
msgid "Amount and currency are required for paid batches."
msgstr "Износ и валута су неопходни за плаћене групе."

#: lms/lms/doctype/lms_course/lms_course.py:74
msgid "Amount and currency are required for paid certificates."
msgstr "Износ и валута су неопходни за плаћене сертификате."

#: lms/lms/doctype/lms_course/lms_course.py:71
msgid "Amount and currency are required for paid courses."
msgstr "Износ и валута су неопходни за плаћене обуке."

#. Label of the amount_with_gst (Currency) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount with GST"
msgstr "Износ са GST"

#: frontend/src/components/Modals/AnnouncementModal.vue:33
msgid "Announcement"
msgstr "Саопштење"

#: frontend/src/components/Modals/AnnouncementModal.vue:101
msgid "Announcement has been sent successfully"
msgstr "Саопштење је успешно послато"

#: frontend/src/components/Modals/AnnouncementModal.vue:96
msgid "Announcement is required"
msgstr "Саопштење је неопходно"

#. Label of the answer (Text Editor) field in DocType 'LMS Assignment'
#. Label of the answer (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the answer (Code) field in DocType 'LMS Exercise'
#: frontend/src/pages/QuizSubmission.vue:60
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Answer"
msgstr "Одговор"

#: frontend/src/pages/CourseForm.vue:112 frontend/src/pages/CourseForm.vue:131
msgid "Appears on the course card in the course list"
msgstr "Приказује се на картици обуке у листи обука"

#: frontend/src/pages/BatchForm.vue:240
msgid "Appears when the batch URL is shared on any online platform"
msgstr "Приказује се када је URL групе подељен на било којој онлајн платфорни"

#: frontend/src/pages/BatchForm.vue:221
msgid "Appears when the batch URL is shared on socials"
msgstr "Приказује се када је URL групе подељен на друштвеним мрежама"

#: frontend/src/pages/JobDetail.vue:51
msgid "Apply"
msgstr "Примени"

#. Label of the apply_gst (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply GST for India"
msgstr "Примени GST за Индију"

#. Label of the apply_rounding (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply Rounding on Equivalent"
msgstr "Примени заокруживање на еквивалент"

#: frontend/src/components/Modals/JobApplicationModal.vue:6
msgid "Apply for this job"
msgstr "Пријавите се за овај посао"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Approved"
msgstr "Одобрено"

#: frontend/src/components/Apps.vue:13
msgid "Apps"
msgstr "Апликације"

#: frontend/src/pages/Batches.vue:283
msgid "Archived"
msgstr "Архивирано"

#: frontend/src/components/UpcomingEvaluations.vue:172
msgid "Are you sure you want to cancel this evaluation? This action cannot be undone."
msgstr "Да ли сте сигурни да желите да откажете ово оцењивање? Ова радња се не може поништити."

#: frontend/src/components/UserDropdown.vue:175
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Да ли сте сигурни да желите да се пријавите на своју Frappe Cloud контролну таблу?"

#. Label of the assessment_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the assessment (Table) field in DocType 'LMS Batch'
#: frontend/src/components/Modals/AssessmentModal.vue:27
#: frontend/src/components/Modals/BatchStudentProgress.vue:41
#: lms/lms/doctype/lms_batch/lms_batch.json lms/templates/assessments.html:11
msgid "Assessment"
msgstr "Процена"

#. Label of the assessment_name (Dynamic Link) field in DocType 'LMS
#. Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Name"
msgstr "Назив процене"

#. Label of the assessment_type (Link) field in DocType 'LMS Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Type"
msgstr "Врста процене"

#: frontend/src/components/Modals/AssessmentModal.vue:91
msgid "Assessment added successfully"
msgstr "Процена је успешно додата"

#: lms/lms/doctype/lms_batch/lms_batch.py:80
msgid "Assessment {0} has already been added to this batch."
msgstr "Процена {0} је већ додата овој групи."

#. Label of the show_assessments (Check) field in DocType 'LMS Settings'
#: frontend/src/components/AppSidebar.vue:580
#: frontend/src/components/Assessments.vue:5
#: frontend/src/components/BatchStudents.vue:32
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/templates/assessments.html:3
msgid "Assessments"
msgstr "Процене"

#: lms/lms/doctype/lms_badge/lms_badge.js:50
msgid "Assign"
msgstr "Додели"

#. Label of the section_break_16 (Section Break) field in DocType 'Course
#. Lesson'
#. Label of the assignment (Link) field in DocType 'LMS Assignment Submission'
#: frontend/src/pages/AssignmentSubmissionList.vue:12
#: frontend/src/utils/assignment.js:24
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/templates/assignment.html:3
msgid "Assignment"
msgstr "Задатак"

#. Label of the assignment_attachment (Attach) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Attachment"
msgstr "Прилог задатка"

#: frontend/src/pages/AssignmentSubmissionList.vue:223
msgid "Assignment Submissions"
msgstr "Подношења задатака"

#. Label of the assignment_title (Data) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Title"
msgstr "Наслов задатка"

#: frontend/src/components/Modals/AssignmentForm.vue:125
msgid "Assignment created successfully"
msgstr "Задатак је успешно креиран"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:24
msgid "Assignment for Lesson {0} by {1} already exists."
msgstr "Задаци за лекцију {0} од стране {1} већ постоје."

#: frontend/src/components/Assignment.vue:356
msgid "Assignment submitted successfully"
msgstr "Задатак је успешно поднет"

#: frontend/src/components/Modals/AssignmentForm.vue:138
msgid "Assignment updated successfully"
msgstr "Задатак је успешно ажуриран"

#. Description of the 'Question' (Small Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Assignment will appear at the bottom of the lesson."
msgstr "Задатак ће се приказивати на дну у оквиру лекције."

#: frontend/src/components/AppSidebar.vue:584
#: frontend/src/pages/Assignments.vue:211 lms/www/lms.py:273
msgid "Assignments"
msgstr "Додељени задаци"

#: lms/lms/doctype/lms_question/lms_question.py:43
msgid "At least one option must be correct for this question."
msgstr "Бар једна опција мора бити тачна за ово питање."

#: frontend/src/components/Modals/LiveClassAttendance.vue:5
msgid "Attendance for Class - {0}"
msgstr "Присуство на предавању - {0}"

#: frontend/src/components/Modals/LiveClassAttendance.vue:24
msgid "Attended for"
msgstr "Присуствовано у трајању од"

#. Label of the attendees (Int) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Attendees"
msgstr "Присутни"

#. Label of the attire (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Attire Preference"
msgstr "Преференција одевања"

#: frontend/src/pages/ProfileEvaluator.vue:137
msgid "Authorize Google Calendar Access"
msgstr "Одобри приступ Гоогле Цалендар-у"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Auto Assign"
msgstr "Аутоматска додела"

#. Label of the auto_recording (Select) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:73
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Auto Recording"
msgstr "Аутоматско снимање"

#: frontend/src/pages/ProfileEvaluator.vue:224
msgid "Availability updated successfully"
msgstr "Доступност је успешно ажурирана"

#: frontend/src/components/BatchFeedback.vue:43
msgid "Average Feedback Received"
msgstr "Просечна оцена повратних информација"

#: frontend/src/components/CourseCard.vue:55
#: frontend/src/pages/CourseDetail.vue:20
msgid "Average Rating"
msgstr "Просечна оцена"

#: frontend/src/pages/Lesson.vue:166
msgid "Back to Course"
msgstr "Назад на обуку"

#. Label of the badge (Link) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge"
msgstr "Беџ"

#. Label of the badge_description (Small Text) field in DocType 'LMS Badge
#. Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Description"
msgstr "Опис беџа"

#. Label of the badge_image (Attach) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Image"
msgstr "Слика беџа"

#. Label of the batch (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the batch (Link) field in DocType 'LMS Batch Feedback'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate Request'
#. Label of the batch_name (Link) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:32
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Batch"
msgstr "Група"

#. Label of the batch_confirmation_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Confirmation Template"
msgstr "Шаблон потврде групе"

#. Name of a DocType
#: lms/lms/doctype/batch_course/batch_course.json
msgid "Batch Course"
msgstr "Групна обука"

#. Label of the section_break_5 (Section Break) field in DocType 'LMS Batch
#. Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Batch Description"
msgstr "Опис групе"

#. Label of the batch_details (Text Editor) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:126
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/templates/emails/batch_confirmation.html:26
msgid "Batch Details"
msgstr "Детаљи групе"

#. Label of the batch_details_raw (HTML Editor) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Batch Details Raw"
msgstr "Необрађени детаљи групе"

#: frontend/src/components/Modals/EmailTemplateModal.vue:28
msgid "Batch Enrollment Confirmation"
msgstr "Потврда уписа у групу"

#. Name of a role
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Batch Evaluator"
msgstr "Особа за оцењивање групе"

#. Label of the batch_name (Link) field in DocType 'LMS Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Batch Name"
msgstr "Назив групе"

#. Label of the batch_old (Link) field in DocType 'Exercise Latest Submission'
#. Label of the batch_old (Link) field in DocType 'Exercise Submission'
#. Label of the batch_old (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Batch Old"
msgstr "Стара група"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Batch
#. Old'
#. Label of the section_break_szgq (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Settings"
msgstr "Подешавања групе"

#: lms/templates/emails/batch_confirmation.html:11
msgid "Batch Start Date:"
msgstr "Датум почетка групе:"

#: frontend/src/components/BatchStudents.vue:40
msgid "Batch Summary"
msgstr "Резиме групе"

#. Label of the batch_title (Data) field in DocType 'LMS Certificate'
#. Label of the batch_title (Data) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Batch Title"
msgstr "Наслов групе"

#: lms/lms/doctype/lms_batch/lms_batch.py:41
msgid "Batch end date cannot be before the batch start date"
msgstr "Датум завршетка групе не може бити пре датума почетка групе"

#: lms/lms/api.py:245
msgid "Batch has already started."
msgstr "Група је већ започела."

#: lms/lms/api.py:240
msgid "Batch is sold out."
msgstr "Група је распродата."

#: lms/lms/doctype/lms_batch/lms_batch.py:46
msgid "Batch start time cannot be greater than or equal to end time."
msgstr "Време почетка групе не може бити веће или једнако времену завршетка групе."

#: lms/templates/emails/batch_start_reminder.html:10
msgid "Batch:"
msgstr "Група:"

#. Label of the batches (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batches.vue:299 frontend/src/pages/Batches.vue:306
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:122
msgid "Batches"
msgstr "Групе"

#. Label of the begin_date (Date) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Begin Date"
msgstr "Датум почетка"

#: lms/templates/emails/batch_confirmation.html:33
#: lms/templates/emails/batch_start_reminder.html:31
#: lms/templates/emails/certification.html:20
#: lms/templates/emails/live_class_reminder.html:28
msgid "Best Regards"
msgstr "Срдачан поздрав"

#. Label of the billing_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: frontend/src/pages/Billing.vue:8 frontend/src/pages/Billing.vue:357
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Details"
msgstr "Детаљи фактурисања"

#. Label of the billing_name (Data) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:70
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Name"
msgstr "Назив за фактурисање"

#: frontend/src/components/Modals/EditProfile.vue:75
msgid "Bio"
msgstr "Биографија"

#. Label of the body (Markdown Editor) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Body"
msgstr "Садржај"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Both Individual and Team Work"
msgstr "И самосталан и тимски рад"

#. Label of the branch (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Branch"
msgstr "Организациона јединица"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:23
msgid "Business Owner"
msgstr "Власник бизниса"

#: frontend/src/components/CourseCardOverlay.vue:48
msgid "Buy this course"
msgstr "Купите ову обуку"

#: lms/templates/emails/lms_message.html:11
msgid "By"
msgstr "Од стране"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "CGPA/4"
msgstr "CGPA/4"

#: frontend/src/components/UpcomingEvaluations.vue:57
#: frontend/src/components/UpcomingEvaluations.vue:177
msgid "Cancel"
msgstr "Откажи"

#: frontend/src/components/UpcomingEvaluations.vue:171
msgid "Cancel this evaluation?"
msgstr "Откажите оцењивање?"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Cancelled"
msgstr "Отказано"

#. Label of the carrer_preference_details (Section Break) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Career Preference Details"
msgstr "Детаљи о каријерним преференцијама"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Casual Wear"
msgstr "Лежерна одећа"

#. Label of the category (Link) field in DocType 'LMS Batch'
#. Label of the category (Data) field in DocType 'LMS Category'
#. Label of the category (Link) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:192 frontend/src/pages/Batches.vue:55
#: frontend/src/pages/CertifiedParticipants.vue:35
#: frontend/src/pages/CourseForm.vue:36 frontend/src/pages/Courses.vue:51
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json lms/templates/signup-form.html:22
msgid "Category"
msgstr "Категорија"

#: frontend/src/components/Settings/Categories.vue:39
msgid "Category Name"
msgstr "Назив категорије"

#: frontend/src/components/Settings/Categories.vue:133
msgid "Category added successfully"
msgstr "Категорија је успешно додата"

#: frontend/src/components/Settings/Categories.vue:193
msgid "Category deleted successfully"
msgstr "Категорија је успешно обрисана"

#: frontend/src/components/Settings/Categories.vue:173
msgid "Category updated successfully"
msgstr "Категорија је успешно ажурирана"

#. Label of the certificate (Link) field in DocType 'LMS Enrollment'
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certificate"
msgstr "Сертификат"

#. Label of the certification_template (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certificate Email Template"
msgstr "Шаблон имејла за сертификат"

#: lms/templates/emails/certification.html:13
msgid "Certificate Link"
msgstr "Линк сертификата"

#: frontend/src/components/CourseCardOverlay.vue:130
msgid "Certificate of Completion"
msgstr "Сертификат о похађању"

#: frontend/src/components/Modals/Event.vue:311
msgid "Certificate saved successfully"
msgstr "Сертификат је успешно сачуван"

#: frontend/src/pages/ProfileCertificates.vue:4
msgid "Certificates"
msgstr "Сертификати"

#: frontend/src/components/Modals/BulkCertificates.vue:120
msgid "Certificates generated successfully"
msgstr "Сертификати су успешно генерисани"

#. Label of the certification (Table) field in DocType 'User'
#. Name of a DocType
#. Label of the certification (Check) field in DocType 'LMS Batch'
#. Label of the certification_section (Section Break) field in DocType 'LMS
#. Enrollment'
#. Label of a Card Break in the LMS Workspace
#. Label of a Link in the LMS Workspace
#: frontend/src/components/AppSidebar.vue:588
#: frontend/src/components/CourseCard.vue:115
#: frontend/src/components/Modals/Event.vue:372
#: frontend/src/pages/BatchForm.vue:62 frontend/src/pages/Batches.vue:38
#: frontend/src/pages/CourseCertification.vue:10
#: frontend/src/pages/CourseCertification.vue:135
#: frontend/src/pages/Courses.vue:34 lms/fixtures/custom_field.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certification"
msgstr "Сертификација"

#. Label of the certification_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Certification Details"
msgstr "Детаљи сертификације"

#. Label of the certification_name (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Certification Name"
msgstr "Назив сертифиакције"

#: frontend/src/components/BatchStudents.vue:17
msgid "Certified"
msgstr "Сертификован"

#. Label of the certified_members (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/CertifiedParticipants.vue:182
#: frontend/src/pages/CertifiedParticipants.vue:189
#: frontend/src/pages/Statistics.vue:40
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certified Members"
msgstr "Сертификовани чланови"

#. Label of the certified_participants (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:302
msgid "Certified Participants"
msgstr "Сертификовани учесници"

#: lms/templates/assignment.html:13
msgid "Change"
msgstr "Промена"

#: frontend/src/components/Assignment.vue:342
msgid "Changes saved successfully"
msgstr "Промене су успешно сачуване"

#. Label of the chapter (Link) field in DocType 'Chapter Reference'
#. Label of the chapter (Link) field in DocType 'LMS Course Progress'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/chapter_reference/chapter_reference.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/workspace/lms/lms.json
msgid "Chapter"
msgstr "Поглавље"

#. Name of a DocType
#: lms/lms/doctype/chapter_reference/chapter_reference.json
msgid "Chapter Reference"
msgstr "Референца поглавља"

#: frontend/src/components/Modals/ChapterModal.vue:154
msgid "Chapter added successfully"
msgstr "Поглавље је успешно додато"

#: frontend/src/components/CourseOutline.vue:299
msgid "Chapter deleted successfully"
msgstr "Поглавље је успешно обрисано"

#: frontend/src/components/Modals/ChapterModal.vue:196
msgid "Chapter updated successfully"
msgstr "Поглавље је успешно ажурирано"

#. Label of the chapters (Table) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Chapters"
msgstr "Поглавља"

#: frontend/src/components/Quiz.vue:219 lms/templates/quiz/quiz.html:120
msgid "Check"
msgstr "Означи"

#: lms/templates/emails/mention_template.html:10
msgid "Check Discussion"
msgstr "Провери дискусију"

#: frontend/src/components/Modals/AssignmentForm.vue:55
msgid "Check Submissions"
msgstr "Провери поднеске"

#: lms/templates/certificates_section.html:24
msgid "Check out the {0} to know more about certification."
msgstr "Погледај {0} за више информација о сертификацији."

#: frontend/src/components/NoPermission.vue:19
msgid "Checkout Courses"
msgstr "Заврши куповину курсева"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Choices"
msgstr "Избори"

#: frontend/src/components/Quiz.vue:634 lms/templates/quiz/quiz.html:53
msgid "Choose all answers that apply"
msgstr "Изабери све тачне одговоре"

#: frontend/src/components/Modals/Question.vue:19
msgid "Choose an existing question"
msgstr "Изабери постојеће питање"

#: frontend/src/components/Controls/IconPicker.vue:27
msgid "Choose an icon"
msgstr "Изабери иконицу"

#: frontend/src/components/Quiz.vue:635 lms/templates/quiz/quiz.html:53
msgid "Choose one answer"
msgstr "Изаберите један одговор"

#. Label of the city (Data) field in DocType 'User'
#. Label of the location (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/Billing.vue:81 frontend/src/pages/JobForm.vue:34
#: lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "City"
msgstr "Град"

#: lms/templates/emails/live_class_reminder.html:10
msgid "Class:"
msgstr "Предавање:"

#: frontend/src/components/Controls/Link.vue:49
msgid "Clear"
msgstr "Очисти"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Clearly Defined Role"
msgstr "Јасно дефинисана улога"

#: frontend/src/components/BatchFeedback.vue:10
msgid "Click here"
msgstr "Кликните овде"

#. Label of the client_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the client_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:36
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client ID"
msgstr "ИД клијента"

#. Label of the client_secret (Password) field in DocType 'LMS Zoom Settings'
#. Label of the client_secret (Password) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:49
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client Secret"
msgstr "Тајна клијента"

#: frontend/src/components/Settings/Categories.vue:27
#: frontend/src/components/Settings/Evaluators.vue:24
#: frontend/src/components/Settings/Members.vue:24
msgid "Close"
msgstr "Затвори"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Closed"
msgstr "Затворено"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Cloud"
msgstr "Облак"

#. Label of the code (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Code"
msgstr "Код"

#. Name of a DocType
#. Label of the cohort (Link) field in DocType 'Cohort Join Request'
#. Label of the cohort (Link) field in DocType 'Cohort Mentor'
#. Label of the cohort (Link) field in DocType 'Cohort Staff'
#. Label of the cohort (Link) field in DocType 'Cohort Subgroup'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the cohort (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Cohort"
msgstr "Образовна група"

#. Name of a DocType
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Cohort Join Request"
msgstr "Захтев за придруживањем образовној групи"

#. Name of a DocType
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Cohort Mentor"
msgstr "Члан образовне групе"

#. Name of a DocType
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Cohort Staff"
msgstr "Особље образовне групе"

#. Name of a DocType
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Cohort Subgroup"
msgstr "Подгрупа образовне групе"

#. Name of a DocType
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Cohort Web Page"
msgstr "Веб-страница образовне групе"

#. Label of the collaboration (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Collaboration Preference"
msgstr "Преференције за сарадњу"

#: frontend/src/components/AppSidebar.vue:142
msgid "Collapse"
msgstr "Сажми"

#. Label of the college (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "College Name"
msgstr "Назив факултета"

#. Label of the color (Color) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Color"
msgstr "Боја"

#: frontend/src/pages/BatchForm.vue:293 frontend/src/pages/CourseForm.vue:283
msgid "Comma separated keywords for SEO"
msgstr "Кључне речи, одвојене зарезом, за SEO"

#. Label of the comments (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the comments (Small Text) field in DocType 'Exercise Submission'
#. Label of the comments (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the comments (Small Text) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Assignment.vue:164
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Comments"
msgstr "Коментари"

#: frontend/src/components/Assignment.vue:142
msgid "Comments by Evaluator"
msgstr "Коментари од особе за оцењивање"

#. Description of the 'Meta Keywords' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Common keywords that will be used for all pages"
msgstr "Заједничке кључне речи које ће се користити за све странице"

#. Label of the company (Data) field in DocType 'LMS Job Application'
#. Label of the company (Data) field in DocType 'Work Experience'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Company"
msgstr "Компанија"

#. Label of the section_break_6 (Section Break) field in DocType 'Job
#. Opportunity'
#: frontend/src/pages/JobForm.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Details"
msgstr "Детаљи компаније"

#. Label of the company_email_address (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:75
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Email Address"
msgstr "Имејл адреса компаније"

#. Label of the company_logo (Attach Image) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:80
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Logo"
msgstr "Лого компаније"

#. Label of the company_name (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:62
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Name"
msgstr "Назив компаније"

#. Label of the company_type (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Company Type"
msgstr "Врста компаније"

#. Label of the company_website (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:68
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Website"
msgstr "Веб-сајт компаније"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: frontend/src/components/Modals/BatchStudentProgress.vue:24
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/widgets/CourseCard.html:75 lms/templates/reviews.html:48
msgid "Complete"
msgstr "Завршено"

#: lms/templates/emails/lms_invite_request_approved.html:7
msgid "Complete Sign Up"
msgstr "Завршите регистрацију"

#: lms/templates/emails/payment_reminder.html:15
msgid "Complete Your Enrollment"
msgstr "Довршите свој упис"

#: lms/lms/doctype/lms_payment/lms_payment.py:73
msgid "Complete Your Enrollment - Don't miss out!"
msgstr "Довршите свој упис - Немојте га пропустити!"

#: frontend/src/components/VideoBlock.vue:140
msgid "Complete the upcoming quiz to continue watching the video. The quiz will open in {0} {1}."
msgstr "Завршите предстојећи квиз да бисте наставили гледање видео-снимка. Квиз ће бити доступан за {0} {1}."

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/widgets/CourseCard.html:78
msgid "Completed"
msgstr "Завршено"

#. Label of the enable_certification (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:232
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Completion Certificate"
msgstr "Сертификат о завршетку"

#. Label of the condition (Code) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Condition"
msgstr "Услов"

#: lms/lms/doctype/lms_badge/lms_badge.py:16
msgid "Condition must be in valid JSON format."
msgstr "Услов мора бити у важећем JSON формату."

#: lms/lms/doctype/lms_badge/lms_badge.py:21
msgid "Condition must be valid python code."
msgstr "Услов мора бити важећи python код."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:7
msgid "Conduct Evaluation"
msgstr "Спровести оцењивање"

#: frontend/src/pages/BatchForm.vue:141
msgid "Configurations"
msgstr "Конфигурације"

#: frontend/src/components/UserDropdown.vue:180
msgid "Confirm"
msgstr "Потврди"

#. Label of the confirmation_email_sent (Check) field in DocType 'LMS Batch
#. Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "Confirmation Email Sent"
msgstr "Потврдни имејл је послат"

#. Label of the confirmation_email_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Confirmation Email Template"
msgstr "Шаблон имејла за потврду"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:29
msgid "Congratulations on getting certified!"
msgstr "Честитамо на добијању сертификата!"

#: frontend/src/components/CourseCardOverlay.vue:57
#: frontend/src/pages/Lesson.vue:47
msgid "Contact the Administrator to enroll for this course."
msgstr "Контактирајте администратора да бисте се уписали на ову обуку."

#. Label of the content (Text) field in DocType 'Course Lesson'
#. Label of the content (Rating) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/Modals/EmailTemplateModal.vue:44
#: frontend/src/components/Modals/EmailTemplateModal.vue:57
#: frontend/src/pages/LessonForm.vue:62
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Content"
msgstr "Садржај"

#: frontend/src/components/CourseCardOverlay.vue:30
msgid "Continue Learning"
msgstr "Наставите са учењем"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:178
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Contract"
msgstr "Уговор"

#: lms/lms/utils.py:442
msgid "Cookie Policy"
msgstr "Политика колачића"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Corporate Organization"
msgstr "Корпоративна организација"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:179
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Correct"
msgstr "Тачно"

#: frontend/src/components/Modals/Question.vue:76
msgid "Correct Answer"
msgstr "Тачан одговор"

#. Label of the country (Link) field in DocType 'User'
#. Label of the country (Link) field in DocType 'Job Opportunity'
#. Label of the country (Link) field in DocType 'Payment Country'
#: frontend/src/pages/Billing.vue:92 frontend/src/pages/JobForm.vue:40
#: frontend/src/pages/Jobs.vue:57 lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Country"
msgstr "Држава"

#. Label of the course (Link) field in DocType 'Batch Course'
#. Label of the course (Link) field in DocType 'Cohort'
#. Label of the course (Link) field in DocType 'Cohort Mentor'
#. Label of the course (Link) field in DocType 'Cohort Staff'
#. Label of the course (Link) field in DocType 'Cohort Subgroup'
#. Label of the course (Link) field in DocType 'Course Chapter'
#. Label of the course (Link) field in DocType 'Course Lesson'
#. Label of the course (Link) field in DocType 'Exercise Latest Submission'
#. Label of the course (Link) field in DocType 'Exercise Submission'
#. Label of the course (Link) field in DocType 'LMS Assignment Submission'
#. Label of the course (Link) field in DocType 'LMS Batch Old'
#. Label of the course (Link) field in DocType 'LMS Certificate'
#. Label of the course (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the course (Link) field in DocType 'LMS Certificate Request'
#. Label of the course (Link) field in DocType 'LMS Course Interest'
#. Label of the course (Link) field in DocType 'LMS Course Mentor Mapping'
#. Label of the course (Link) field in DocType 'LMS Course Progress'
#. Label of the course (Link) field in DocType 'LMS Course Review'
#. Label of the course (Link) field in DocType 'LMS Enrollment'
#. Label of the course (Link) field in DocType 'LMS Exercise'
#. Label of the course (Link) field in DocType 'LMS Mentor Request'
#. Label of the course (Link) field in DocType 'LMS Program Course'
#. Label of the course (Link) field in DocType 'LMS Quiz'
#. Label of the course (Link) field in DocType 'LMS Quiz Submission'
#. Label of the course (Link) field in DocType 'Related Courses'
#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/BatchCourseModal.vue:20
#: frontend/src/components/Modals/BulkCertificates.vue:38
#: frontend/src/components/Modals/EvaluationModal.vue:20
#: frontend/src/components/Modals/Event.vue:24
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/related_courses/related_courses.json
#: lms/lms/report/course_progress_summary/course_progress_summary.js:9
#: lms/lms/report/course_progress_summary/course_progress_summary.py:51
#: lms/lms/workspace/lms/lms.json
msgid "Course"
msgstr "Обука"

#. Name of a DocType
#. Label of the chapter (Link) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Chapter"
msgstr "Поглавље обуке"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Completed"
msgstr "Обука је завршена"

#: frontend/src/pages/Statistics.vue:31
msgid "Course Completions"
msgstr "Завршене обуке"

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:26
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Course Creator"
msgstr "Аутор обуке"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Data"
msgstr "Подаци о обуци"

#: frontend/src/pages/CourseForm.vue:181
msgid "Course Description"
msgstr "Опис обуке"

#: frontend/src/pages/Statistics.vue:22
msgid "Course Enrollments"
msgstr "Упис на обуку"

#. Name of a DocType
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Course Evaluator"
msgstr "Особа за оцењивање обуке"

#: frontend/src/pages/CourseForm.vue:90
msgid "Course Image"
msgstr "Слика обуке"

#. Name of a DocType
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Course Instructor"
msgstr "Предавач на обуци"

#. Name of a DocType
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Lesson"
msgstr "Лекција обуке"

#: lms/www/lms.py:87
msgid "Course List"
msgstr "Листа обуке"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:58
msgid "Course Name"
msgstr "Назив обуке"

#: frontend/src/pages/CourseDetail.vue:76 frontend/src/pages/CourseForm.vue:293
msgid "Course Outline"
msgstr "План обуке"

#. Name of a report
#: lms/lms/report/course_progress_summary/course_progress_summary.json
msgid "Course Progress Summary"
msgstr "Сажетак о напретку обуке"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Course Settings"
msgstr "Подешавање обуке"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Stats"
msgstr "Статистика обуке"

#. Label of the title (Data) field in DocType 'Batch Course'
#. Label of the course_title (Data) field in DocType 'Course Chapter'
#. Label of the course_title (Data) field in DocType 'LMS Certificate'
#. Label of the course_title (Data) field in DocType 'LMS Certificate Request'
#. Label of the course_title (Data) field in DocType 'LMS Program Course'
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "Course Title"
msgstr "Наслов обуке"

#: frontend/src/pages/ProgramForm.vue:234
msgid "Course added to program"
msgstr "Обука је додата у програм"

#: frontend/src/pages/CourseForm.vue:523
msgid "Course created successfully"
msgstr "Обука је успешно креирана"

#: frontend/src/pages/CourseForm.vue:560
msgid "Course deleted successfully"
msgstr "Обука је успешно обрисана"

#: frontend/src/pages/ProgramForm.vue:303
msgid "Course moved successfully"
msgstr "Обука је успешно премештена"

#: frontend/src/pages/CourseForm.vue:543
msgid "Course updated successfully"
msgstr "Обука је успешно ажурирана"

#: lms/lms/doctype/lms_batch/lms_batch.py:54
#: lms/lms/doctype/lms_program/lms_program.py:19
msgid "Course {0} has already been added to this batch."
msgstr "Обука {0} је већ додата у ову групу."

#. Label of the courses (Table) field in DocType 'LMS Batch'
#. Label of the show_courses (Check) field in DocType 'LMS Settings'
#. Label of the courses (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchCourses.vue:5
#: frontend/src/components/BatchOverlay.vue:37
#: frontend/src/components/BatchStudents.vue:25
#: frontend/src/components/Modals/BatchStudentProgress.vue:91
#: frontend/src/pages/BatchDetail.vue:44
#: frontend/src/pages/CourseCertification.vue:127
#: frontend/src/pages/Courses.vue:331 frontend/src/pages/Courses.vue:338
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Courses"
msgstr "Обуке"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:28
msgid "Courses Completed"
msgstr "Завршене обуке"

#: frontend/src/components/BatchCourses.vue:154
msgid "Courses deleted successfully"
msgstr "Обуке су успешно обрисане"

#. Label of the cover_image (Attach Image) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Cover Image"
msgstr "Насловна слика"

#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/Programs.vue:93
msgid "Create"
msgstr "Креирај"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.js:7
msgid "Create Certificate"
msgstr "Креирај сертификат"

#: frontend/src/components/Controls/Link.vue:37
#: frontend/src/components/Controls/MultiSelect.vue:66
msgid "Create New"
msgstr "Креирај нови"

#: lms/templates/onboarding_header.html:19
msgid "Create a Course"
msgstr "Креирај обуку"

#: frontend/src/components/Modals/LiveClassModal.vue:5
msgid "Create a Live Class"
msgstr "Креирај онлајн предавање"

#: frontend/src/components/AppSidebar.vue:575
msgid "Create a batch"
msgstr "Креирај групу"

#: frontend/src/components/AppSidebar.vue:566
msgid "Create a course"
msgstr "Креирај обуку"

#: frontend/src/components/AppSidebar.vue:576
msgid "Create a live class"
msgstr "Креирај онлајн предавање"

#: frontend/src/components/Modals/AssignmentForm.vue:13
msgid "Create an Assignment"
msgstr "Креирај задатак"

#: frontend/src/components/AppSidebar.vue:500
msgid "Create your first batch"
msgstr "Креирајте своју прву групу"

#: frontend/src/components/AppSidebar.vue:431
msgid "Create your first course"
msgstr "Креирајте своју прву обуку"

#: frontend/src/components/AppSidebar.vue:478
msgid "Create your first quiz"
msgstr "Креирајте свој први квиз"

#: frontend/src/pages/Assignments.vue:176 frontend/src/pages/Courses.vue:321
msgid "Created"
msgstr "Креирано"

#: frontend/src/components/AppSidebar.vue:572
msgid "Creating a batch"
msgstr "Креирање групе"

#: frontend/src/components/AppSidebar.vue:563
msgid "Creating a course"
msgstr "Креирање обуке"

#. Label of the currency (Link) field in DocType 'LMS Batch'
#. Label of the currency (Link) field in DocType 'LMS Course'
#. Label of the currency (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:272 frontend/src/pages/CourseForm.vue:262
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Currency"
msgstr "Валута"

#. Label of the current_lesson (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Current Lesson"
msgstr "Тренутна лекција"

#: frontend/src/components/AppSidebar.vue:594
msgid "Custom Certificate Templates"
msgstr "Прилагођени шаблон сертификата"

#. Label of the custom_component (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom HTML"
msgstr "Прилагођени HTML"

#. Label of the custom_script (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom Script (JavaScript)"
msgstr "Прилагођена скрипта (JavaScript)"

#. Label of the custom_signup_content (HTML Editor) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Custom Signup Content"
msgstr "Прилагођени садржај за пријаву"

#. Label of the customisations_tab (Tab Break) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Customisations"
msgstr "Прилагођавања"

#. Label of the show_dashboard (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Dashboard"
msgstr "Контролна табла"

#. Label of the date (Date) field in DocType 'LMS Batch Timetable'
#. Label of the date (Date) field in DocType 'LMS Certificate Evaluation'
#. Label of the date (Date) field in DocType 'LMS Certificate Request'
#. Label of the date (Date) field in DocType 'LMS Live Class'
#. Label of the date (Date) field in DocType 'Scheduled Flow'
#: frontend/src/components/Modals/EvaluationModal.vue:26
#: frontend/src/components/Modals/Event.vue:40
#: frontend/src/components/Modals/LiveClassModal.vue:29
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/templates/quiz/quiz.html:149
msgid "Date"
msgstr "Датум"

#: frontend/src/pages/BatchForm.vue:69
msgid "Date and Time"
msgstr "Време и датум"

#: lms/templates/emails/live_class_reminder.html:13
msgid "Date:"
msgstr "Датум:"

#. Label of the day (Select) field in DocType 'Evaluator Schedule'
#. Label of the day (Int) field in DocType 'LMS Batch Timetable'
#. Label of the day (Select) field in DocType 'LMS Certificate Request'
#: frontend/src/pages/ProfileEvaluator.vue:26
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Day"
msgstr "Дан"

#: lms/templates/emails/mentor_request_creation_email.html:2
#: lms/templates/emails/mentor_request_status_update_email.html:2
msgid "Dear"
msgstr "Здраво"

#: lms/templates/emails/batch_confirmation.html:2
#: lms/templates/emails/batch_start_reminder.html:2
#: lms/templates/emails/certification.html:2
#: lms/templates/emails/live_class_reminder.html:2
msgid "Dear "
msgstr "Здраво "

#: frontend/src/components/Modals/EmailTemplateModal.vue:66
msgid "Dear {{ member_name }},\\n\\nYou have been enrolled in our upcoming batch {{ batch_name }}.\\n\\nThanks,\\nFrappe Learning"
msgstr "Здраво {{ member_name }},\\n\\nУписани сте у нашу предстојећу групу {{ batch_name }}.\\n\\nХвала Вам,\\nFrappe Learning"

#. Label of the default_currency (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Default Currency"
msgstr "Подразумевана валута"

#. Label of the degree_type (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Degree Type"
msgstr "Врста дипломе"

#: frontend/src/components/CourseOutline.vue:253
#: frontend/src/components/CourseOutline.vue:311
#: frontend/src/pages/CourseForm.vue:573
msgid "Delete"
msgstr "Обриши"

#: frontend/src/components/CourseOutline.vue:56
msgid "Delete Chapter"
msgstr "Обриши поглавље"

#: frontend/src/pages/CourseForm.vue:567
msgid "Delete Course"
msgstr "Обриши обуку"

#: frontend/src/components/CourseOutline.vue:305
msgid "Delete this chapter?"
msgstr "Обриши ово поглавље?"

#: frontend/src/components/CourseOutline.vue:247
msgid "Delete this lesson?"
msgstr "Обришите ову лекцију?"

#: frontend/src/pages/CourseForm.vue:568
msgid "Deleting the course will also delete all its chapters and lessons. Are you sure you want to delete this course?"
msgstr "Брисањем обуке такође ће се обрисати сва поглавља и лекције. Да ли сте сигурни да желите да обришете ову обуку?"

#: frontend/src/components/CourseOutline.vue:306
msgid "Deleting this chapter will also delete all its lessons and permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "Брисањем овог поглавља такође ће се обрисати све лекције, а поглавље ће трајно бити уклоњено из обуке. Ова радња се не може поништити. Да ли сте сигурни да желите да наставите?"

#: frontend/src/components/CourseOutline.vue:248
msgid "Deleting this lesson will permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "Брисањем ове лекције она ће трајно бити уклоњена из обуке. Ова радња се не може поништити. Да ли сте сигурни да желите да наставите?"

#. Label of the description (Text Editor) field in DocType 'Job Opportunity'
#. Label of the description (Small Text) field in DocType 'Certification'
#. Label of the description (Markdown Editor) field in DocType 'Cohort'
#. Label of the description (Markdown Editor) field in DocType 'Cohort
#. Subgroup'
#. Label of the description (Small Text) field in DocType 'LMS Badge'
#. Label of the description (Small Text) field in DocType 'LMS Batch'
#. Label of the description (Markdown Editor) field in DocType 'LMS Batch Old'
#. Label of the description (Text Editor) field in DocType 'LMS Course'
#. Label of the description (Small Text) field in DocType 'LMS Exercise'
#. Label of the description (Text) field in DocType 'LMS Live Class'
#. Label of the description (Small Text) field in DocType 'Work Experience'
#: frontend/src/components/Modals/LiveClassModal.vue:80
#: frontend/src/pages/JobForm.vue:125
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Description"
msgstr "Опис"

#: frontend/src/components/Apps.vue:51
msgid "Desk"
msgstr "Радна површина"

#: frontend/src/components/Modals/DiscussionModal.vue:22
#: frontend/src/pages/BatchForm.vue:14 frontend/src/pages/CourseForm.vue:25
#: frontend/src/pages/QuizForm.vue:42
msgid "Details"
msgstr "Детаљи"

#: frontend/src/pages/CourseForm.vue:172
msgid "Disable Self Enrollment"
msgstr "Онемогући самостални упис"

#. Label of the disable_self_learning (Check) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Disable Self Learning"
msgstr "Онемогући самостално учење"

#. Label of the disable_signup (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Disable Signup"
msgstr "Онемогући регистрацију"

#. Label of the disabled (Check) field in DocType 'Job Opportunity'
#: frontend/src/components/Settings/ZoomSettings.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Disabled"
msgstr "Онемогућено"

#: frontend/src/components/DiscussionReplies.vue:57
#: lms/lms/widgets/NoPreviewModal.html:25 lms/templates/reviews.html:159
msgid "Discard"
msgstr "Одбаци"

#. Label of the show_discussions (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batch.vue:88
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Discussions"
msgstr "Дискусија"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Document"
msgstr "Документ"

#: lms/templates/emails/payment_reminder.html:11
msgid "Don’t miss this opportunity to enhance your skills. Click below to complete your enrollment"
msgstr "Немојте пропустити прилику да унапредите своје знање. Кликните испод да бисте завршили свој упис"

#. Label of the dream_companies (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Dream Companies"
msgstr "Пожељне компаније"

#: lms/lms/doctype/lms_question/lms_question.py:33
msgid "Duplicate options found for this question."
msgstr "Пронађене су дупликат опције за ово питање."

#. Label of the duration (Data) field in DocType 'Cohort'
#. Label of the duration (Data) field in DocType 'LMS Batch Timetable'
#. Label of the duration (Int) field in DocType 'LMS Live Class'
#. Label of the duration (Int) field in DocType 'LMS Live Class Participant'
#: frontend/src/components/Modals/LiveClassModal.vue:36
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Duration"
msgstr "Трајање"

#. Label of the duration (Data) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:63 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Duration (in minutes)"
msgstr "Трајање (у минутима)"

#: frontend/src/components/Modals/LiveClassModal.vue:32
msgid "Duration of the live class in minutes"
msgstr "Трајање онлајн предавања у минутима"

#. Label of the email (Link) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "E-Mail"
msgstr "Имејл"

#. Label of the email (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "E-mail"
msgstr "Имејл"

#: frontend/src/components/BatchOverlay.vue:116
#: frontend/src/components/CourseCardOverlay.vue:90
#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/JobDetail.vue:34 frontend/src/pages/Lesson.vue:135
#: frontend/src/pages/Profile.vue:36 frontend/src/pages/Programs.vue:53
msgid "Edit"
msgstr "Уреди"

#: frontend/src/components/Modals/AssignmentForm.vue:14
msgid "Edit Assignment"
msgstr "Уреди задатак"

#: frontend/src/components/CourseOutline.vue:49
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Edit Chapter"
msgstr "Уреди поглавље"

#: frontend/src/components/Modals/EmailTemplateModal.vue:8
msgid "Edit Email Template"
msgstr "Уреди имејл шаблон"

#: frontend/src/pages/Profile.vue:72
msgid "Edit Profile"
msgstr "Уреди профил"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "Edit Zoom Account"
msgstr "Уреди Zoom налог"

#: frontend/src/pages/QuizForm.vue:182
msgid "Edit the question"
msgstr "Уреди питање"

#. Label of the education (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education"
msgstr "Образовање"

#. Name of a DocType
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Education Detail"
msgstr "Детаљи образовања"

#. Label of the education_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education Details"
msgstr "Детаљи образовања"

#: frontend/src/components/Settings/Evaluators.vue:33
#: frontend/src/components/Settings/Members.vue:33
#: lms/templates/signup-form.html:10
msgid "Email"
msgstr "Имејл"

#: frontend/src/components/Modals/Event.vue:16
msgid "Email ID"
msgstr "ИД имејла"

#. Label of the email_sent (Check) field in DocType 'LMS Course Interest'
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "Email Sent"
msgstr "Имејл послат"

#: frontend/src/pages/BatchForm.vue:154
msgid "Email Template"
msgstr "Имејл шаблон"

#: frontend/src/components/Modals/EmailTemplateModal.vue:117
msgid "Email Template created successfully"
msgstr "Имејл шаблон је успешно креиран"

#: frontend/src/components/Modals/EmailTemplateModal.vue:146
msgid "Email Template updated successfully"
msgstr "Имејл шаблон је успешно ажуриран"

#. Label of the email_templates_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Email Templates"
msgstr "Имејл шаблони"

#: frontend/src/components/Settings/EmailTemplates.vue:128
#: frontend/src/components/Settings/ZoomSettings.vue:161
msgid "Email Templates deleted successfully"
msgstr "Имејл шаблон је успешно обрисан"

#. Label of the show_emails (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Emails"
msgstr "Имејлови"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:25
msgid "Employee"
msgstr "Запослено лице"

#. Label of the enable (Check) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Enable"
msgstr "Омогући"

#: lms/lms/doctype/lms_settings/lms_settings.py:21
msgid "Enable Google API in Google Settings to send calendar invites for evaluations."
msgstr "Омогућите Google API у Google подешавањима за слање позивница за оцењивање у календар."

#. Label of the enable_learning_paths (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Enable Learning Paths"
msgstr "Омогући путању учења"

#: frontend/src/components/Modals/ChapterModal.vue:24
msgid "Enable this only if you want to upload a SCORM package as a chapter."
msgstr "Омогућите ово само уколико желите да отпремите SCORM пакет као поглавље."

#. Label of the enabled (Check) field in DocType 'LMS Badge'
#. Label of the enabled (Check) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:23
#: frontend/src/components/Settings/ZoomSettings.vue:53
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Enabled"
msgstr "Омогућено"

#: frontend/src/components/Modals/BulkCertificates.vue:53
msgid "Enabling this will publish the certificate on the certified participants page."
msgstr "Омогућавањем овога сертификат ће бити објављен на страници са сертификованим учесницима."

#. Label of the end_date (Date) field in DocType 'Cohort'
#. Label of the end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:82 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "End Date"
msgstr "Датум завршетка"

#. Label of the end_date (Date) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "End Date (or expected)"
msgstr "Датум завршетка (или очекивани)"

#. Label of the end_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the end_time (Time) field in DocType 'LMS Batch'
#. Label of the end_time (Time) field in DocType 'LMS Batch Old'
#. Label of the end_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the end_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:98
#: frontend/src/pages/ProfileEvaluator.vue:32
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "End Time"
msgstr "Време завршетка"

#: frontend/src/components/LiveClass.vue:89
msgid "Ended"
msgstr "Завршено"

#: frontend/src/components/BatchOverlay.vue:103
msgid "Enroll Now"
msgstr "Упишите се сада"

#: frontend/src/pages/Batches.vue:286 frontend/src/pages/Courses.vue:324
msgid "Enrolled"
msgstr "Уписан"

#: frontend/src/components/CourseCard.vue:46
#: frontend/src/components/CourseCardOverlay.vue:112
#: frontend/src/pages/CourseDetail.vue:33
msgid "Enrolled Students"
msgstr "Уписани студенти"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:93
msgid "Enrollment Confirmation for {0}"
msgstr "Потврда о упису за {0}"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:20
msgid "Enrollment Count"
msgstr "Број уписаних"

#: lms/lms/utils.py:1906
msgid "Enrollment Failed"
msgstr "Упис је неуспешан"

#. Label of the enrollments (Int) field in DocType 'LMS Course'
#. Label of a chart in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_course/lms_course.json lms/lms/workspace/lms/lms.json
msgid "Enrollments"
msgstr "Уписи"

#: lms/lms/doctype/lms_settings/lms_settings.py:26
msgid "Enter Client Id and Client Secret in Google Settings to send calendar invites for evaluations."
msgstr "Унесите ИД клијента и клијентску тајну у Google подешавањима да бисте слали позивнице за оцењивање у календар."

#: frontend/src/components/Assignment.vue:113
msgid "Enter a URL"
msgstr "Унесите URL"

#: frontend/src/pages/QuizForm.vue:49
msgid "Enter a title and save the quiz to proceed"
msgstr "Унесите наслов и сачувајте квиз да бисте наставили"

#: lms/templates/quiz/quiz.html:53
msgid "Enter the correct answer"
msgstr "Унесите тачан одговор"

#: frontend/src/components/Modals/ZoomAccountModal.vue:163
msgid "Error creating Zoom Account"
msgstr "Грешка приликом креирања Zoom налога"

#: frontend/src/components/Modals/EmailTemplateModal.vue:122
msgid "Error creating email template"
msgstr "Грешка приликом генерисања имејл шаблона"

#: lms/lms/doctype/lms_batch/lms_batch.py:204
msgid "Error creating live class. Please try again. {0}"
msgstr "Грешка приликом креирања онлајн предавања. Молимо Вас да покушате поново. {0}"

#: frontend/src/components/Settings/EmailTemplates.vue:133
#: frontend/src/components/Settings/ZoomSettings.vue:166
msgid "Error deleting email templates"
msgstr "Грешка приликом брисања имејл шаблона"

#: frontend/src/components/Modals/ZoomAccountModal.vue:200
msgid "Error updating Zoom Account"
msgstr "Грешка приликом ажурирања Zoom налога"

#: frontend/src/components/Modals/EmailTemplateModal.vue:151
msgid "Error updating email template"
msgstr "Грешка приликом ажурирања имејл шаблона"

#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/Event.vue:365 lms/lms/workspace/lms/lms.json
msgid "Evaluation"
msgstr "Оцењивање"

#. Label of the section_break_6 (Section Break) field in DocType 'LMS
#. Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Evaluation Details"
msgstr "Детаљи оцењивања"

#. Label of the evaluation_end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:115
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Evaluation End Date"
msgstr "Датум завршетка оцењивања"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Evaluation Request"
msgstr "Захтев за оцењивање"

#: lms/lms/doctype/lms_batch/lms_batch.py:87
msgid "Evaluation end date cannot be less than the batch end date."
msgstr "Датум завршетка оцењивање не може бити мањи од датума завршетка групе."

#: frontend/src/components/Modals/Event.vue:256
msgid "Evaluation saved successfully"
msgstr "Оцењивање је успешно сачувано"

#. Label of the evaluator (Link) field in DocType 'Batch Course'
#. Label of the evaluator (Link) field in DocType 'Course Evaluator'
#. Label of the evaluator (Link) field in DocType 'LMS Assignment Submission'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Request'
#. Label of the evaluator (Link) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BatchCourseModal.vue:37
#: frontend/src/components/Modals/BulkCertificates.vue:22
#: frontend/src/pages/CourseForm.vue:251 frontend/src/pages/ProfileRoles.vue:32
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/templates/upcoming_evals.html:33
msgid "Evaluator"
msgstr "Особа за оцењивање"

#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Evaluator Name"
msgstr "Име особе за оцењивање"

#. Name of a DocType
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
msgid "Evaluator Schedule"
msgstr "Распоред особе за оцењивање"

#: lms/lms/doctype/lms_course/lms_course.py:67
msgid "Evaluator is required for paid certificates."
msgstr "Особа за оцењивање је обавезна за плаћене сертификате."

#. Label of the event (Select) field in DocType 'LMS Badge'
#. Label of the event (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Event"
msgstr "Догађај"

#: frontend/src/pages/BatchForm.vue:109
msgid "Example: IST (+5:30)"
msgstr "Пример: IST (+5:30)"

#. Label of the exercise (Link) field in DocType 'Exercise Latest Submission'
#. Label of the exercise (Link) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise"
msgstr "Вежба"

#. Name of a DocType
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Exercise Latest Submission"
msgstr "Најновији рад на вежби"

#. Name of a DocType
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise Submission"
msgstr "Подношење вежбе"

#. Label of the exercise_title (Data) field in DocType 'Exercise Latest
#. Submission'
#. Label of the exercise_title (Data) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise Title"
msgstr "Наслов вежбе"

#: frontend/src/components/AppSidebar.vue:142
msgid "Expand"
msgstr "Прошири"

#. Label of the expiration_date (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Expiration Date"
msgstr "Датум истека"

#. Label of the expiry_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:33
#: frontend/src/components/Modals/Event.vue:126
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Expiry Date"
msgstr "Датум истека"

#. Label of the explanation_1 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_3 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_4 (Small Text) field in DocType 'LMS Question'
#: frontend/src/components/Modals/Question.vue:72
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation"
msgstr "Објашњење"

#. Label of the explanation_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation "
msgstr "Објашњење "

#: lms/lms/web_template/course_cards/course_cards.html:15
#: lms/lms/web_template/recently_published_courses/recently_published_courses.html:16
msgid "Explore More"
msgstr "Истражите више"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:357
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Fail"
msgstr "Неуспех"

#: lms/lms/doctype/lms_live_class/lms_live_class.py:139
msgid "Failed to fetch attendance data from Zoom for class {0}: {1}"
msgstr "Неуспешно преузимање података о присуству за Zoom за предавање {0}: {1}"

#: frontend/src/utils/index.js:622
msgid "Failed to update meta tags {0}"
msgstr "Неуспешно ажурирање мета ознака {0}"

#. Label of the featured (Check) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:20
#: frontend/src/pages/CourseForm.vue:167
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Featured"
msgstr "Истакнуто"

#. Label of the feedback (Small Text) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/BatchFeedback.vue:30
#: frontend/src/pages/Batch.vue:146
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Feedback"
msgstr "Повратна информација"

#: frontend/src/components/Assignment.vue:64
msgid "Feel free to make edits to your submission if needed."
msgstr "Будите слободни да измените свој унос уколико је то неопходно."

#. Label of the field_to_check (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Field To Check"
msgstr "Поља за проверу"

#. Label of the major (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Field of Major/Study"
msgstr "Област студија"

#. Label of the file_type (Select) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "File Type"
msgstr "Врста фајла"

#: frontend/src/components/Modals/EditProfile.vue:59
#: frontend/src/components/Settings/Members.vue:39
msgid "First Name"
msgstr "Име"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Fixed 9-5"
msgstr "Фиксно време од 9,00 до 17,00"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Flexible Time"
msgstr "Флексибилно време"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Formal Wear"
msgstr "Формална одећа"

#: lms/lms/widgets/CourseCard.html:114
msgid "Free"
msgstr "Бесплатно"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:179
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Freelance"
msgstr "Фриленс"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:27
msgid "Freelancer/Just looking"
msgstr "Фриленсе / Само разгледам"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "French (e.g. Distinction)"
msgstr "Француски систем (нпр. Изузетно)"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Friday"
msgstr "Петак"

#. Label of the unavailable_from (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:99
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "From"
msgstr "Од"

#. Label of the from_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "From Date"
msgstr "Датум почетка"

#. Label of the full_name (Data) field in DocType 'Course Evaluator'
#. Label of the full_name (Data) field in DocType 'Invite Request'
#. Label of the full_name (Data) field in DocType 'LMS Program Member'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/templates/signup-form.html:5
msgid "Full Name"
msgstr "Име и презиме"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:176
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Full Time"
msgstr "Пуно радно време"

#. Name of a DocType
#. Label of the function (Data) field in DocType 'Function'
#. Label of the function (Link) field in DocType 'Preferred Function'
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Function"
msgstr "Функција"

#: frontend/src/pages/Billing.vue:43
msgid "GST Amount"
msgstr "GST износ"

#: frontend/src/pages/Billing.vue:110
msgid "GST Number"
msgstr "GST број"

#. Label of the gstin (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "GSTIN"
msgstr "GSTIN"

#. Label of the general_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "General"
msgstr "Опште"

#: frontend/src/components/Modals/BulkCertificates.vue:5
#: frontend/src/pages/Batch.vue:12
msgid "Generate Certificates"
msgstr "Генериши сертификате"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:15
msgid "Generate Google Meet Link"
msgstr "Генериши Google Meet линк"

#: frontend/src/components/CourseCardOverlay.vue:77
msgid "Get Certificate"
msgstr "Преузми сертификат"

#: frontend/src/components/CertificationLinks.vue:34
#: frontend/src/components/CertificationLinks.vue:50
#: frontend/src/pages/CertifiedParticipants.vue:11
msgid "Get Certified"
msgstr "Стекни сертификат"

#: lms/templates/onboarding_header.html:8
msgid "Get Started"
msgstr "Започни"

#. Label of the github (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Github ID"
msgstr "GitHub ID"

#. Label of the google_meet_link (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Google Meet Link"
msgstr "Google Meet линк"

#. Label of the grade (Data) field in DocType 'Education Detail'
#: frontend/src/components/Assignment.vue:158
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade"
msgstr "Оцена"

#. Label of the grade_assignment (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Grade Assignment"
msgstr "Оцена задатка"

#. Label of the grade_type (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade Type"
msgstr "Врста оцене"

#: frontend/src/components/Assignment.vue:153
msgid "Grading"
msgstr "Оцењивање"

#. Label of the grant_only_once (Check) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Grant only once"
msgstr "Додели само једном"

#: lms/templates/signup-form.html:56
msgid "Have an account? Login"
msgstr "Имате налог? Пријавите се"

#. Label of the headline (Data) field in DocType 'User'
#: frontend/src/components/Modals/EditProfile.vue:69
#: lms/fixtures/custom_field.json
msgid "Headline"
msgstr "Наслов"

#: lms/lms/widgets/HelloWorld.html:13
msgid "Hello"
msgstr "Здраво"

#: frontend/src/components/AppSidebar.vue:128
msgid "Help"
msgstr "Помоћ"

#: lms/templates/courses_created.html:15
msgid "Help others learn something new by creating a course."
msgstr "Помозите другима да науче нешто ново креирањем обуке."

#: frontend/src/components/BatchFeedback.vue:15
msgid "Help us improve by providing your feedback."
msgstr "Помозите нам да се побољшамо дајући нам своје утиске."

#: lms/templates/reviews.html:101
msgid "Help us improve our course material."
msgstr "Помозите нам да унапредимо материјале за обуку."

#: frontend/src/pages/PersonaForm.vue:16
msgid "Help us understand your needs"
msgstr "Помозите нам да разумемо Ваше потребе"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:1
#: lms/templates/emails/certificate_request_notification.html:1
msgid "Hey {0}"
msgstr "Здраво {0}"

#: lms/templates/emails/job_report.html:3
msgid "Hey,"
msgstr "Здраво,"

#: lms/templates/emails/payment_reminder.html:2
msgid "Hi"
msgstr "Здраво"

#: lms/templates/emails/lms_course_interest.html:3
msgid "Hi {0},"
msgstr "Здраво {0},"

#: lms/templates/emails/lms_invite_request_approved.html:3
msgid "Hi,"
msgstr "Здраво,"

#. Label of the hide_private (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Hide my Private Information from others"
msgstr "Сакриј моје приватне информације од других"

#. Label of the hints (Small Text) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Hints"
msgstr "Савети"

#. Label of the host (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Host"
msgstr "Домаћин"

#. Label of the current (Check) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "I am currently working here"
msgstr "Тренутно радим овде"

#: lms/templates/emails/certification.html:6
msgid "I am delighted to inform you that you have successfully earned your certification for the {0} course. Congratulations!"
msgstr "Са задовољством Вас обавештавамо да сте успешно стекли сертификат за обуку {0}. Честитамо!"

#. Label of the looking_for_job (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "I am looking for a job"
msgstr "Тренутно сам у потрази за послом"

#: frontend/src/pages/ProfileEvaluator.vue:94
msgid "I am unavailable"
msgstr "Нисам доступан"

#: frontend/src/pages/QuizForm.vue:385
msgid "ID"
msgstr "ИД"

#. Label of the icon (Data) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:28
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Icon"
msgstr "Иконица"

#. Label of the user_category (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Identify User Category"
msgstr "Одреди категорију корисника"

#: frontend/src/components/LessonHelp.vue:11
msgid "If Include in Preview is enabled for a lesson then the lesson will also be accessible to non logged in users."
msgstr "Уколико је опција укључи у преглед омогућена за лекцију, тада ће лекција бити доступна и корисницима који нису пријављени."

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "If you are not any more interested to mentor the course"
msgstr "Уколико више нисте заинтересовани да будете ментор обуке"

#: frontend/src/components/Quiz.vue:23
msgid "If you fail to do so, the quiz will be automatically submitted when the timer ends."
msgstr "Уколико то не учините самостално, квиз ће бити аутоматски поднет када истекне време."

#: lms/templates/emails/payment_reminder.html:19
msgid "If you have any questions or need assistance, feel free to reach out to our support team."
msgstr "Уколико имате питања или Вам је потребна помоћ, слободно контактирајте наш тим подршке."

#: lms/templates/emails/batch_confirmation.html:29
#: lms/templates/emails/batch_start_reminder.html:27
#: lms/templates/emails/live_class_reminder.html:24
msgid "If you have any questions or require assistance, feel free to contact us."
msgstr "Уколико имате питања или Вам је потребна помоћ, слободно нас контактирајте."

#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Batch'
#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "If you set an amount here, then the USD equivalent setting will not get applied."
msgstr "Уколико овде унесете износ, подешавање еквивалента у америчким доларима (USD) неће бити примењено."

#: lms/lms/doctype/lms_quiz/lms_quiz.py:61
msgid "If you want open ended questions then make sure each question in the quiz is of open ended type."
msgstr "Уколико желите питања са отвореним одговорима, проверите да ли је свако питање у квизу врсте отворени одговор."

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Label of the image (Code) field in DocType 'Exercise Latest Submission'
#. Label of the image (Code) field in DocType 'Exercise Submission'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#. Label of the image (Attach Image) field in DocType 'LMS Badge'
#. Label of the image (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Image"
msgstr "Слика"

#: frontend/src/components/Modals/EditCoverImage.vue:58
#: frontend/src/components/UnsplashImageBrowser.vue:52
msgid "Image search powered by"
msgstr "Претрага слика омогућена уз подршку"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:215
msgid "Image: Corrupted Data Stream"
msgstr "Слика: Оштећен ток података"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: frontend/src/components/Modals/Event.vue:349
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "In Progress"
msgstr "У току"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Inactive"
msgstr "Неактиван"

#. Label of the include_in_preview (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Include In Preview"
msgstr "Укључи у преглед"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Incomplete"
msgstr "Непотпуно"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:184
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Incorrect"
msgstr "Нетачно"

#. Label of the index_ (Int) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index"
msgstr "Индекс"

#. Label of the index_label (Data) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index Label"
msgstr "Ознака индекса"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Individual Work"
msgstr "Самостални рад"

#. Name of a DocType
#. Label of the industry (Data) field in DocType 'Industry'
#. Label of the industry (Link) field in DocType 'Preferred Industry'
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Industry"
msgstr "Индустрија"

#. Label of the institution_name (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Institution Name"
msgstr "Назив институције"

#. Label of the instructor (Link) field in DocType 'Cohort'
#. Label of the instructor (Link) field in DocType 'Course Instructor'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Instructor"
msgstr "Предавач"

#. Label of the instructor_content (Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Content"
msgstr "Садржај предавача"

#. Label of the instructor_notes (Markdown Editor) field in DocType 'Course
#. Lesson'
#: frontend/src/pages/Lesson.vue:199 frontend/src/pages/LessonForm.vue:42
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Notes"
msgstr "Белешке предавача"

#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Batch'
#. Label of the instructors (Rating) field in DocType 'LMS Batch Feedback'
#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:27 frontend/src/pages/CourseForm.vue:44
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Instructors"
msgstr "Предавачи"

#: lms/templates/assignment.html:17
msgid "Instructors Comments"
msgstr "Коментари предавача"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Interest"
msgstr "Интересовање"

#: frontend/src/components/AppSidebar.vue:555
#: frontend/src/components/AppSidebar.vue:558
msgid "Introduction"
msgstr "Увод"

#: lms/lms/doctype/invite_request/invite_request.py:83
msgid "Invalid Invite Code."
msgstr "Неисправан код за позив."

#: lms/lms/doctype/course_lesson/course_lesson.py:20
msgid "Invalid Quiz ID"
msgstr "Неважећи ИД квиза"

#: lms/lms/doctype/course_lesson/course_lesson.py:34
msgid "Invalid Quiz ID in content"
msgstr "Неважећи ИД квиза у садржају"

#. Label of the invite_code (Data) field in DocType 'Cohort Subgroup'
#. Label of the invite_code (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Code"
msgstr "Шифра позивнице"

#. Label of the invite_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Email"
msgstr "Имејл позивница"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Invite Only"
msgstr "Само за позване"

#. Name of a DocType
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Request"
msgstr "Захтев за позивницу"

#: frontend/src/components/AppSidebar.vue:489
msgid "Invite your team and students"
msgstr "Позовите свој тим и студенте"

#. Label of the is_correct (Check) field in DocType 'LMS Option'
#. Label of the is_correct_1 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_2 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_3 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_4 (Check) field in DocType 'LMS Question'
#. Label of the is_correct (Check) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_option/lms_option.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Is Correct"
msgstr "Тачно"

#. Label of the is_scorm_package (Check) field in DocType 'Course Chapter'
#. Label of the is_scorm_package (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Is SCORM Package"
msgstr "SCORM пакет"

#. Label of the issue_date (Date) field in DocType 'Certification'
#. Label of the issue_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:28
#: frontend/src/components/Modals/Event.vue:121
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Issue Date"
msgstr "Датум издавања"

#: frontend/src/components/AppSidebar.vue:591
msgid "Issue a Certificate"
msgstr "Издај сертификат"

#. Label of the issued_on (Date) field in DocType 'LMS Badge Assignment'
#: frontend/src/pages/CourseCertification.vue:27
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Issued On"
msgstr "Издато"

#: frontend/src/pages/ProfileAbout.vue:56
#: frontend/src/pages/ProfileCertificates.vue:17
#: lms/templates/certificates_section.html:11
msgid "Issued on"
msgstr "Издато"

#. Label of the items_in_sidebar_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Items in Sidebar"
msgstr "Ставке у бочној траци"

#: frontend/src/pages/ProgramForm.vue:277
msgid "Items removed successfully"
msgstr "Ставке су успешно уклоњене"

#: lms/templates/signup-form.html:6
msgid "Jane Doe"
msgstr "Петар Петровић"

#. Label of the job (Link) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job"
msgstr "Посао"

#. Label of the subtitle (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Subtitle"
msgstr "Поднаслов табле огласа за посао"

#. Label of the title (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Title"
msgstr "Наслов табле огласа за посао"

#: frontend/src/pages/JobForm.vue:14
msgid "Job Details"
msgstr "Детаљи посла"

#: lms/www/lms.py:176
msgid "Job Openings"
msgstr "Огласи за посао"

#. Name of a DocType
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Job Opportunity"
msgstr "Прилика за посао"

#. Name of a DocType
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Settings"
msgstr "Подешавање посла"

#. Label of the job_title (Data) field in DocType 'Job Opportunity'
#. Label of the job_title (Data) field in DocType 'LMS Job Application'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job Title"
msgstr "Назив радног места"

#. Label of the jobs (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/JobDetail.vue:10 frontend/src/pages/Jobs.vue:8
#: frontend/src/pages/Jobs.vue:185
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Jobs"
msgstr "Послови"

#: frontend/src/components/LiveClass.vue:78
#: lms/templates/upcoming_evals.html:15
msgid "Join"
msgstr "Придружи се"

#: frontend/src/components/UpcomingEvaluations.vue:90
msgid "Join Call"
msgstr "Придржи се позиву"

#: frontend/src/components/Modals/Event.vue:74
msgid "Join Meeting"
msgstr "Придружи се састанку"

#. Label of the join_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Join URL"
msgstr "URL за придруживање"

#. Label of the joined_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Joined At"
msgstr "Приступ у"

#: frontend/src/components/Modals/LiveClassAttendance.vue:18
msgid "Joined at"
msgstr "Приступ остварен у"

#. Name of a Workspace
#: lms/lms/workspace/lms/lms.json
msgid "LMS"
msgstr "LMS"

#. Name of a DocType
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "LMS Assessment"
msgstr "LMS процена"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "LMS Assignment"
msgstr "LMS задатак"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "LMS Assignment Submission"
msgstr "LMS подношење задатка"

#. Name of a DocType
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "LMS Badge"
msgstr "LMS беџ"

#. Name of a DocType
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "LMS Badge Assignment"
msgstr "LMS додела беџа"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Batch"
msgstr "LMS група"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "LMS Batch Enrollment"
msgstr "LMS упис у групу"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "LMS Batch Feedback"
msgstr "LMS повратна информација групе"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "LMS Batch Old"
msgstr "LMS стара група"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "LMS Batch Timetable"
msgstr "LMS распоред наставе групе"

#. Name of a DocType
#: lms/lms/doctype/lms_category/lms_category.json
msgid "LMS Category"
msgstr "LMS категорија"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "LMS Certificate"
msgstr "LMS сертификат"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "LMS Certificate Evaluation"
msgstr "LMS оцењивање сертификата"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "LMS Certificate Request"
msgstr "LMS захтев за сертификатом"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Course"
msgstr "LMS обука"

#. Name of a DocType
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "LMS Course Interest"
msgstr "LMS интересовање за обуку"

#. Name of a DocType
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "LMS Course Mentor Mapping"
msgstr "LMS повезивање ментора на обуку"

#. Name of a DocType
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "LMS Course Progress"
msgstr "LMS напредак на обуци"

#. Name of a DocType
#: lms/lms/doctype/lms_course_review/lms_course_review.json
msgid "LMS Course Review"
msgstr "LMS рецензија обуке"

#. Name of a DocType
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "LMS Enrollment"
msgstr "LMS упис"

#. Name of a DocType
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "LMS Exercise"
msgstr "LMS вежба"

#. Name of a DocType
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "LMS Job Application"
msgstr "LMS пријава за посао"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "LMS Live Class"
msgstr "LMS онлајн предавање"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "LMS Live Class Participant"
msgstr "LMS учесник онлајн предавања"

#. Name of a DocType
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "LMS Mentor Request"
msgstr "LMS захтев за ментора"

#. Name of a DocType
#: lms/lms/doctype/lms_option/lms_option.json
msgid "LMS Option"
msgstr "LMS опција"

#. Name of a DocType
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Payment"
msgstr "LMS уплата"

#. Name of a DocType
#: lms/lms/doctype/lms_program/lms_program.json
msgid "LMS Program"
msgstr "LMS програм"

#. Name of a DocType
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "LMS Program Course"
msgstr "LMS програм обуке"

#. Name of a DocType
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "LMS Program Member"
msgstr "LMS члан програма"

#. Name of a DocType
#: lms/lms/doctype/lms_question/lms_question.json
msgid "LMS Question"
msgstr "LMS питање"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "LMS Quiz"
msgstr "LMS квиз"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "LMS Quiz Question"
msgstr "LMS питање у квизу"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "LMS Quiz Result"
msgstr "LMS резултати квиза"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "LMS Quiz Submission"
msgstr "LMS подношење квиза"

#. Name of a DocType
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LMS Settings"
msgstr "LMS подешавања"

#. Name of a DocType
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "LMS Sidebar Item"
msgstr "LMS ставка бочне траке"

#. Name of a DocType
#: lms/lms/doctype/lms_source/lms_source.json
msgid "LMS Source"
msgstr "LMS извор"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/user_skill/user_skill.json
msgid "LMS Student"
msgstr "LMS студент"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "LMS Timetable Legend"
msgstr "LMS легенда распореда наставе"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "LMS Timetable Template"
msgstr "LMS шаблон распореда наставе"

#. Name of a DocType
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "LMS Zoom Settings"
msgstr "LMS Zoom подешавања"

#. Label of the label (Data) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Label"
msgstr "Ознака"

#: frontend/src/components/Modals/EditProfile.vue:64
msgid "Last Name"
msgstr "Презиме"

#. Label of the latest_submission (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Latest Submission"
msgstr "Последњи поднесак"

#. Label of the launch_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Launch File"
msgstr "Покрени фајл"

#. Label of the left_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Left At"
msgstr "Напуштено у"

#: frontend/src/components/Modals/LiveClassAttendance.vue:21
msgid "Left at"
msgstr "Одлазак у"

#. Label of the lesson (Link) field in DocType 'Exercise Latest Submission'
#. Label of the lesson (Link) field in DocType 'Exercise Submission'
#. Label of the lesson (Link) field in DocType 'Lesson Reference'
#. Label of the lesson (Link) field in DocType 'LMS Assignment Submission'
#. Label of the lesson (Link) field in DocType 'LMS Course Progress'
#. Label of the lesson (Link) field in DocType 'LMS Exercise'
#. Label of the lesson (Link) field in DocType 'LMS Quiz'
#. Label of the lesson (Link) field in DocType 'Scheduled Flow'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lesson_reference/lesson_reference.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/lms/workspace/lms/lms.json
msgid "Lesson"
msgstr "Лекција"

#. Name of a DocType
#: lms/lms/doctype/lesson_reference/lesson_reference.json
msgid "Lesson Reference"
msgstr "Референца лекције"

#. Label of the lesson_title (Data) field in DocType 'Scheduled Flow'
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Lesson Title"
msgstr "Наслов лекције"

#: frontend/src/pages/LessonForm.vue:416
msgid "Lesson created successfully"
msgstr "Лекција је успешно креирана"

#: frontend/src/components/CourseOutline.vue:226
msgid "Lesson deleted successfully"
msgstr "Лекција је успешно обрисана"

#: frontend/src/components/CourseOutline.vue:241
msgid "Lesson moved successfully"
msgstr "Лекција је успешно премештена"

#: frontend/src/pages/LessonForm.vue:440
msgid "Lesson updated successfully"
msgstr "Лекција је успешно ажурирана"

#. Label of the lessons (Table) field in DocType 'Course Chapter'
#. Group in Course Chapter's connections
#. Label of the lessons (Int) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:37
#: frontend/src/components/CourseCardOverlay.vue:105
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Lessons"
msgstr "Лекције"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:36
msgid "Lessons Completed"
msgstr "Лекције завршене"

#: lms/templates/onboarding_header.html:11
msgid "Lets start setting up your content on the LMS so that you can reclaim time and focus on growth."
msgstr "Почнимо са подешавањем садржаја на LMS-у како бисте уштедели време и фокусирали се на раст."

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Letter Grade (e.g. A, B-)"
msgstr "Словна оцена (нпр. А, Б-)"

#. Label of the limit_questions_to (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:108 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Limit Questions To"
msgstr "Ограничи питања на"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:38
msgid "Limit cannot be greater than or equal to the number of questions in the quiz."
msgstr "Ограничење не може бити веће или једнако броју питања у квизу."

#: frontend/src/pages/ProfileAbout.vue:74
msgid "LinkedIn"
msgstr "LinkedIn"

#. Label of the linkedin (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "LinkedIn ID"
msgstr "LinkedIn ИД"

#. Group in Cohort's connections
#. Group in Cohort Subgroup's connections
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Links"
msgstr "Линкови"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#: frontend/src/pages/Courses.vue:307 lms/lms/doctype/cohort/cohort.json
msgid "Live"
msgstr "Активно"

#. Label of the live_class (Link) field in DocType 'LMS Live Class Participant'
#. Label of the show_live_class (Check) field in DocType 'LMS Settings'
#: frontend/src/components/LiveClass.vue:14
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Live Class"
msgstr "Онлајн предавање"

#. Label of the livecode_url (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LiveCode URL"
msgstr "LiveCode URL"

#: frontend/src/components/Settings/Members.vue:109
#: frontend/src/pages/Assignments.vue:69 frontend/src/pages/Batches.vue:80
#: frontend/src/pages/CertifiedParticipants.vue:98
#: frontend/src/pages/Courses.vue:75
#: frontend/src/pages/QuizSubmissionList.vue:39
#: frontend/src/pages/Quizzes.vue:55
msgid "Load More"
msgstr "Учитај више"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Local"
msgstr "Локално"

#. Label of the location (Data) field in DocType 'Education Detail'
#. Label of the location (Data) field in DocType 'Work Experience'
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Location"
msgstr "Локација"

#. Label of the location_preference (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Location Preference"
msgstr "Преференција локације"

#: frontend/src/components/NoPermission.vue:28
#: frontend/src/components/QuizBlock.vue:9 frontend/src/pages/Batch.vue:196
#: frontend/src/pages/Lesson.vue:53
msgid "Login"
msgstr "Пријава"

#: frontend/src/components/UserDropdown.vue:174
msgid "Login to Frappe Cloud?"
msgstr "Пријава на Frappe Cloud?"

#: frontend/src/pages/JobDetail.vue:63
msgid "Login to apply"
msgstr "Пријавите се да бисте наставили"

#: lms/templates/emails/payment_reminder.html:23
msgid "Looking forward to seeing you enrolled!"
msgstr "Радујемо се Вашем упису!"

#. Label of the default_home (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Make LMS the default home"
msgstr "Постави LMS као подразумевану почетну страницу"

#: frontend/src/components/Modals/AnnouncementModal.vue:5
#: frontend/src/pages/Batch.vue:16
msgid "Make an Announcement"
msgstr "Направи саопштење"

#: frontend/src/pages/Billing.vue:123
msgid "Make sure to enter the correct billing name as the same will be used in your invoice."
msgstr "Унесите тачан назив за фактурисање јер ће бити коришћено на Вашој фактури."

#: frontend/src/components/BatchOverlay.vue:69
msgid "Manage Batch"
msgstr "Управљај групом"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Manager"
msgstr "Менаџер"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:24
msgid "Manager (Sales/Marketing/Customer)"
msgstr "Менаџер (продаја/маркетинг/корисници)"

#. Label of the manifest_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Manifest File"
msgstr "Манифест фајл"

#: frontend/src/components/Quiz.vue:110
msgid "Mark"
msgstr "Поен"

#: frontend/src/pages/Notifications.vue:12
msgid "Mark all as read"
msgstr "Означи све као прочитано"

#. Label of the marks (Int) field in DocType 'LMS Quiz Question'
#. Label of the marks (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Modals/Question.vue:40
#: frontend/src/components/Modals/Question.vue:103
#: frontend/src/components/Quiz.vue:110 frontend/src/pages/QuizForm.vue:395
#: frontend/src/pages/QuizSubmission.vue:64
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:59
msgid "Marks"
msgstr "Поени"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:41
msgid "Marks for question number {0} cannot be greater than the marks allotted for that question."
msgstr "Број поена за питање број {0} не може бити већи од поена додељених за то питање."

#. Label of the marks_out_of (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/pages/QuizSubmission.vue:67
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Marks out of"
msgstr "Поени од укупно"

#. Label of the max_attempts (Int) field in DocType 'LMS Quiz'
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Max Attempts"
msgstr "Максималан број покушаја"

#: frontend/src/pages/QuizForm.vue:58
msgid "Maximum Attempts"
msgstr "Максималан број покушаја"

#. Label of the medium (Select) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:187
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Medium"
msgstr "Средство"

#. Label of the medium (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Medium ID"
msgstr "ИД средства"

#: lms/templates/emails/batch_confirmation.html:16
#: lms/templates/emails/batch_start_reminder.html:19
msgid "Medium:"
msgstr "Средство:"

#. Label of the meeting_id (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Meeting ID"
msgstr "ИД састанка"

#. Label of the member (Link) field in DocType 'Exercise Latest Submission'
#. Label of the member (Link) field in DocType 'Exercise Submission'
#. Label of the member (Link) field in DocType 'LMS Assignment Submission'
#. Label of the member (Link) field in DocType 'LMS Badge Assignment'
#. Label of the member (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the member (Link) field in DocType 'LMS Batch Feedback'
#. Label of the member (Link) field in DocType 'LMS Certificate'
#. Label of the member (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the member (Link) field in DocType 'LMS Certificate Request'
#. Label of the member (Link) field in DocType 'LMS Course Progress'
#. Label of the member (Link) field in DocType 'LMS Enrollment'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#. Label of the member (Link) field in DocType 'LMS Live Class Participant'
#. Label of the member (Link) field in DocType 'LMS Mentor Request'
#. Label of the member (Link) field in DocType 'LMS Payment'
#. Label of the member (Link) field in DocType 'LMS Program Member'
#. Label of the member (Link) field in DocType 'LMS Quiz Submission'
#. Label of the member (Link) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/LiveClassAttendance.vue:14
#: frontend/src/components/Modals/ZoomAccountModal.vue:42
#: frontend/src/components/Settings/ZoomSettings.vue:178
#: frontend/src/pages/AssignmentSubmissionList.vue:14
#: frontend/src/pages/QuizSubmission.vue:31
#: frontend/src/pages/QuizSubmissionList.vue:91
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:64
msgid "Member"
msgstr "Члан"

#. Label of the member_cohort (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Cohort"
msgstr "Члан образовне групе"

#. Label of the member_email (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Email"
msgstr "Имејл члана"

#. Label of the member_image (Attach Image) field in DocType 'LMS Batch
#. Feedback'
#. Label of the member_image (Attach Image) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Member Image"
msgstr "Слика члана"

#. Label of the member_name (Data) field in DocType 'LMS Assignment Submission'
#. Label of the member_name (Data) field in DocType 'LMS Badge Assignment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Feedback'
#. Label of the member_name (Data) field in DocType 'LMS Certificate'
#. Label of the member_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the member_name (Data) field in DocType 'LMS Certificate Request'
#. Label of the member_name (Data) field in DocType 'LMS Course Progress'
#. Label of the member_name (Data) field in DocType 'LMS Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_name (Data) field in DocType 'LMS Mentor Request'
#. Label of the member_name (Data) field in DocType 'LMS Quiz Submission'
#. Label of the member_name (Data) field in DocType 'LMS Zoom Settings'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:71
msgid "Member Name"
msgstr "Име члана"

#. Label of the member_subgroup (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Subgroup"
msgstr "Подгрупа члана"

#. Label of the member_type (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Member Type"
msgstr "Врста члана"

#. Label of the member_username (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Member Username"
msgstr "Корисничко име члана"

#: frontend/src/pages/ProgramForm.vue:256
msgid "Member added to program"
msgstr "Члан је додата у програм"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:25
msgid "Member already enrolled in this batch"
msgstr "Члан се већ уписао у ову групу"

#: lms/lms/doctype/lms_program/lms_program.py:29
msgid "Member {0} has already been added to this batch."
msgstr "Члан {0} је већ додат у ову групу."

#. Group in LMS Batch Old's connections
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Members"
msgstr "Чланови"

#. Label of the membership (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Membership"
msgstr "Чланство"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Label of the mentor (Link) field in DocType 'LMS Course Mentor Mapping'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Mentor"
msgstr "Ментор"

#. Label of the mentor_name (Data) field in DocType 'LMS Course Mentor Mapping'
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "Mentor Name"
msgstr "Име ментора"

#. Label of the mentor_request_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Mentor Request"
msgstr "Захтев за ментора"

#. Label of the mentor_request_creation (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:11
#: lms/patches/create_mentor_request_email_templates.py:18
#: lms/patches/create_mentor_request_email_templates.py:28
msgid "Mentor Request Creation Template"
msgstr "Шаблон за креирање захтева за ментора"

#. Label of the mentor_request_status_update (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:31
#: lms/patches/create_mentor_request_email_templates.py:38
#: lms/patches/create_mentor_request_email_templates.py:48
msgid "Mentor Request Status Update Template"
msgstr "Шаблон за ажурирање статуса захтева за ментора"

#. Label of the meta_description (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:284 frontend/src/pages/CourseForm.vue:274
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Description"
msgstr "Мета опис"

#. Label of the meta_image (Attach Image) field in DocType 'LMS Batch'
#. Label of the meta_image (Attach Image) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:200
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Image"
msgstr "Мета слика"

#. Label of the meta_keywords (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:290 frontend/src/pages/CourseForm.vue:280
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Keywords"
msgstr "Мета кључне речи"

#: frontend/src/pages/BatchForm.vue:279 frontend/src/pages/CourseForm.vue:269
msgid "Meta Tags"
msgstr "Мета ознаке"

#: lms/lms/api.py:1454
msgid "Meta tags should be a list."
msgstr "Мета ознаке треба да буду листа."

#. Label of the milestone (Check) field in DocType 'LMS Batch Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Milestone"
msgstr "Кључна тачка"

#: lms/lms/doctype/lms_question/lms_question.py:48
msgid "Minimum two options are required for multiple choice questions."
msgstr "Неопходно су најмање две опције за питања са вишеструким избором."

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:20
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Moderator"
msgstr "Модератор"

#: lms/lms/doctype/lms_badge/lms_badge.js:40
msgid "Modified By"
msgstr "Измењено од стране"

#: lms/lms/api.py:218
msgid "Module Name is incorrect or does not exist."
msgstr "Назив модула је нетачан или не постоји."

#: lms/lms/api.py:214
msgid "Module is incorrect."
msgstr "Модул је неисправан."

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Monday"
msgstr "Понедељак"

#: frontend/src/components/AppSidebar.vue:599
msgid "Monetization"
msgstr "Монетизација"

#: frontend/src/components/AppSidebar.vue:39
msgid "More"
msgstr "Више"

#. Label of the multiple (Check) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Multiple Correct Answers"
msgstr "Више тачних одговора"

#: frontend/src/pages/ProfileEvaluator.vue:4
msgid "My availability"
msgstr "Моја доступност"

#: frontend/src/pages/ProfileEvaluator.vue:127
msgid "My calendar"
msgstr "Мој календар"

#: frontend/src/components/Modals/EmailTemplateModal.vue:24
msgid "Name"
msgstr "Име"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: frontend/src/components/Settings/Categories.vue:27
#: frontend/src/components/Settings/EmailTemplates.vue:17
#: frontend/src/components/Settings/Evaluators.vue:24
#: frontend/src/components/Settings/Members.vue:24
#: frontend/src/components/Settings/ZoomSettings.vue:17
#: frontend/src/pages/Assignments.vue:19 frontend/src/pages/Batches.vue:17
#: frontend/src/pages/Courses.vue:17 frontend/src/pages/Courses.vue:310
#: frontend/src/pages/Programs.vue:14 lms/lms/doctype/lms_badge/lms_badge.json
msgid "New"
msgstr "Нови"

#: lms/www/lms.py:151
msgid "New Batch"
msgstr "Нова група"

#: frontend/src/pages/CourseForm.vue:661 lms/www/lms.py:95
msgid "New Course"
msgstr "Нова обука"

#: frontend/src/components/Modals/EmailTemplateModal.vue:7
msgid "New Email Template"
msgstr "Нови имејл шаблон"

#: frontend/src/pages/Jobs.vue:23
msgid "New Job"
msgstr "Нови посао"

#: lms/job/doctype/lms_job_application/lms_job_application.py:27
msgid "New Job Applicant"
msgstr "Нови кандидат за посао"

#: frontend/src/pages/Programs.vue:90
msgid "New Program"
msgstr "Нови програм"

#: frontend/src/pages/ProgramForm.vue:133
msgid "New Program Course"
msgstr "Нова обука у оквиру програма"

#: frontend/src/pages/ProgramForm.vue:134
msgid "New Program Member"
msgstr "Нови члан програма"

#: frontend/src/pages/QuizForm.vue:123
msgid "New Question"
msgstr "Ново питање"

#: frontend/src/pages/QuizForm.vue:451 frontend/src/pages/QuizForm.vue:459
#: frontend/src/pages/Quizzes.vue:19
msgid "New Quiz"
msgstr "Нови квиз"

#: lms/www/new-sign-up.html:3
msgid "New Sign Up"
msgstr "Нова регистрација"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "New Zoom Account"
msgstr "Нови Zoom налог"

#: lms/lms/utils.py:610
msgid "New comment in batch {0}"
msgstr "Нови коментар у групи {0}"

#: lms/lms/utils.py:603
msgid "New reply on the topic {0} in course {1}"
msgstr "Нова порука на тему {0} у обуци {1}"

#: frontend/src/components/Discussions.vue:8
#: frontend/src/components/Discussions.vue:63
msgid "New {0}"
msgstr "Нови {0}"

#: frontend/src/components/Quiz.vue:227 frontend/src/pages/Lesson.vue:154
msgid "Next"
msgstr "Следеће"

#: lms/templates/quiz/quiz.html:125
msgid "Next Question"
msgstr "Следеће питање"

#: frontend/src/components/Assessments.vue:75 lms/templates/assessments.html:58
msgid "No Assessments"
msgstr "Нема процена"

#: lms/templates/notifications.html:26
msgid "No Notifications"
msgstr "Нема обавештења"

#: frontend/src/components/Quiz.vue:297
msgid "No Quiz submissions found"
msgstr "Нема пронађених поднетих квизова"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "No Recording"
msgstr "Нема снимка"

#: lms/templates/upcoming_evals.html:43
msgid "No Upcoming Evaluations"
msgstr "Нема предстојећих оцењивања"

#: frontend/src/components/Annoucements.vue:24
msgid "No announcements"
msgstr "Нема саопштења"

#: lms/templates/certificates_section.html:23
msgid "No certificates"
msgstr "Нема сертификата"

#: frontend/src/components/BatchCourses.vue:67
msgid "No courses added"
msgstr "Нема додатих обука"

#: lms/templates/courses_created.html:14
msgid "No courses created"
msgstr "Нема креираних обука"

#: frontend/src/pages/Programs.vue:81
msgid "No courses in this program"
msgstr "Нема обука у овом програму"

#: lms/templates/courses_under_review.html:14
msgid "No courses under review"
msgstr "Нема обука у фази прегледа"

#: frontend/src/components/BatchFeedback.vue:60
msgid "No feedback received yet."
msgstr "Још увек нема примљених повратних информација."

#: frontend/src/pages/ProfileAbout.vue:12
msgid "No introduction"
msgstr "Нема увода"

#: frontend/src/components/LiveClass.vue:97
msgid "No live classes scheduled"
msgstr "Нема заказаних онлајн предавања"

#: frontend/src/components/Modals/QuizInVideo.vue:93
msgid "No quizzes added yet."
msgstr "Још увек нису додати квизови."

#: frontend/src/components/Modals/EvaluationModal.vue:62
msgid "No slots available for this date."
msgstr "Нема доступних термина за овај датум."

#: frontend/src/components/Modals/AnnouncementModal.vue:90
msgid "No students in this batch"
msgstr "Нема студената у овој групи"

#: frontend/src/pages/AssignmentSubmissionList.vue:67
msgid "No submissions"
msgstr "Нема поднесака"

#: frontend/src/components/EmptyState.vue:5 lms/templates/course_list.html:13
msgid "No {0}"
msgstr "Нема {0}"

#: lms/templates/quiz/quiz.html:147
msgid "No."
msgstr "Бр."

#: lms/lms/user.py:29
msgid "Not Allowed"
msgstr "Није дозвољено"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Applicable"
msgstr "Није примењиво"

#: lms/templates/assessments.html:48
msgid "Not Attempted"
msgstr "Није покушано"

#: lms/lms/widgets/NoPreviewModal.html:6
msgid "Not Available for Preview"
msgstr "Није доступно за преглед"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Graded"
msgstr "Није оцењено"

#: frontend/src/components/NoPermission.vue:7 frontend/src/pages/Batch.vue:164
msgid "Not Permitted"
msgstr "Није дозвољено"

#: frontend/src/components/Assignment.vue:36
#: frontend/src/components/Settings/BrandSettings.vue:10
#: frontend/src/components/Settings/PaymentSettings.vue:9
#: frontend/src/components/Settings/SettingDetails.vue:10
#: frontend/src/pages/QuizSubmission.vue:9
msgid "Not Saved"
msgstr "Није сачувано"

#: frontend/src/pages/Notifications.vue:53
msgid "Nothing to see here."
msgstr "Овде нема ничега за преглед."

#. Label of the notifications (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Notifications"
msgstr "Обавештења"

#: lms/lms/widgets/NoPreviewModal.html:30
msgid "Notify me when available"
msgstr "Обавести ме када буде доступно"

#: frontend/src/components/BatchStudents.vue:48
msgid "Number of Students"
msgstr "Број студената"

#: frontend/src/pages/BatchForm.vue:150
msgid "Number of seats available"
msgstr "Број доступних места"

#. Label of the sb_00 (Section Break) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "OAuth Client ID"
msgstr "OAuth ID клијента"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Office close to Home"
msgstr "Канцеларија близу куће"

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Offline"
msgstr "Уживо"

#: lms/templates/emails/certification.html:16
msgid "Once again, congratulations on this significant accomplishment."
msgstr "Још једном, честитамо на овом значајном успеху."

#: frontend/src/components/Assignment.vue:60
msgid "Once the moderator grades your submission, you'll find the details here."
msgstr "Након што модератор оцени Ваш поднесак, овде ћете пронаћи детаље."

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Online"
msgstr "Онлајн"

#: frontend/src/pages/ProgramForm.vue:157
msgid "Only courses for which self learning is disabled can be added to program."
msgstr "Искључиво обуке код којих је самостално учење онемогућено могу се додати у програм."

#: lms/templates/assignment.html:6
msgid "Only files of type {0} will be accepted."
msgstr "Искључиво су прихватљиви фајлови врсте {0}."

#: frontend/src/pages/CourseForm.vue:597 frontend/src/utils/index.js:509
msgid "Only image file is allowed."
msgstr "Дозвољен је само фајл слике."

#: frontend/src/components/Modals/ChapterModal.vue:218
msgid "Only zip files are allowed"
msgstr "Дозвољени су само зип фајлови"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: frontend/src/pages/QuizForm.vue:17
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Open"
msgstr "Отворено"

#: lms/templates/emails/assignment_submission.html:8
msgid "Open Assignment"
msgstr "Отворени задатак"

#: lms/templates/emails/lms_message.html:13
msgid "Open Course"
msgstr "Отворена обука"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Open Ended"
msgstr "Отворено питање"

#. Label of the option (Data) field in DocType 'LMS Option'
#: frontend/src/components/Modals/Question.vue:67
#: lms/lms/doctype/lms_option/lms_option.json
msgid "Option"
msgstr "Опција"

#. Label of the option_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 1"
msgstr "Опција 1"

#. Label of the option_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 2"
msgstr "Опција 2"

#. Label of the option_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 3"
msgstr "Опција 3"

#. Label of the option_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 4"
msgstr "Опција 4"

#: frontend/src/components/Modals/Question.vue:56
msgid "Options"
msgstr "Опције"

#. Label of the order_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Order ID"
msgstr "ИД наруџбине"

#. Label of the organization (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Organization"
msgstr "Организација"

#: frontend/src/pages/Billing.vue:32
msgid "Original Amount"
msgstr "Оригинални износ"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:28
msgid "Others"
msgstr "Остали"

#: lms/lms/doctype/lms_badge/lms_badge.js:39
msgid "Owner"
msgstr "Власник"

#. Label of the pan (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "PAN"
msgstr "PAN"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "PDF"
msgstr "PDF"

#. Label of the pages (Table) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Pages"
msgstr "Странице"

#. Label of the paid_batch (Check) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:260
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Paid Batch"
msgstr "Плаћена група"

#. Label of the paid_certificate (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:237
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Certificate"
msgstr "Плаћени сертификат"

#: frontend/src/components/CourseCardOverlay.vue:139
msgid "Paid Certificate after Evaluation"
msgstr "Плаћени сертификат након оцењивања"

#. Label of the paid_course (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:227
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Course"
msgstr "Плаћена обука"

#: frontend/src/pages/Billing.vue:115
msgid "Pan Number"
msgstr "PAN број"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:177
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Part Time"
msgstr "Непуно време"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Partially Complete"
msgstr "Делимично завршено"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:353
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Pass"
msgstr "Положено"

#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz'
#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizForm.vue:72 frontend/src/pages/Quizzes.vue:131
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Passing Percentage"
msgstr "Минимални проценат за пролаз"

#. Label of the password (Password) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Password"
msgstr "Лозинка"

#: frontend/src/pages/CourseForm.vue:197
msgid "Paste the youtube link of a short video introducing the course"
msgstr "Налепите YouTube линк кратког видео-записа који представља обуку"

#. Label of the payment (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the payment (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Payment"
msgstr "Плаћање"

#. Name of a DocType
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Payment Country"
msgstr "Држава плаћања"

#. Label of the payment_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Details"
msgstr "Детаљи плаћања"

#. Label of the payment_gateway (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Gateway"
msgstr "Платни портал"

#. Label of the payment_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment ID"
msgstr "ИД плаћања"

#. Label of the payment_received (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Received"
msgstr "Плаћање примљено"

#. Label of the payment_reminder_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Reminder Template"
msgstr "Шаблон за подсетник за плаћање"

#. Label of the payment_settings_tab (Tab Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Settings"
msgstr "Подешавање плаћања"

#: frontend/src/pages/Billing.vue:21
msgid "Payment for "
msgstr "Плаћање за "

#. Label of the payment_for_certificate (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Certificate"
msgstr "Плаћање за сертификат"

#. Label of the payment_for_document (Dynamic Link) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document"
msgstr "Плаћање за документ"

#. Label of the payment_for_document_type (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document Type"
msgstr "Плаћање за врсту документа"

#. Label of the payments_app_is_not_installed (HTML) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payments app is not installed"
msgstr "Апликација за обраду плаћања није инсталирана"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Modals/Event.vue:345
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Pending"
msgstr "На чекању"

#. Label of the percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:44
#: frontend/src/pages/QuizSubmissionList.vue:102
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Percentage"
msgstr "Проценат"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Percentage (e.g. 70%)"
msgstr "Проценат (нпр. 70%)"

#: frontend/src/components/Modals/BatchStudentProgress.vue:44
msgid "Percentage/Status"
msgstr "Проценат/Статус"

#. Label of the persona_captured (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Persona Captured"
msgstr "Забележен кориснички профил"

#: frontend/src/pages/Billing.vue:99
msgid "Phone Number"
msgstr "Број телефона"

#: lms/lms/doctype/lms_settings/lms_settings.py:34
msgid "Please add <a href='{0}'>{1}</a> for <a href='{2}'>{3}</a> to send calendar invites for evaluations."
msgstr "Молимо Вас да додате <a href='{0}'>{1}</a> за <a href='{2}'>{3}</a> како бисте слали позивнице за оцењивање у календар."

#: frontend/src/components/LiveClass.vue:8
msgid "Please add a zoom account to the batch to create live classes."
msgstr "Молимо Вас да додате Zoom налог да у групи да бисте могли да креирате онлајн предавања."

#: lms/lms/user.py:75
msgid "Please ask your administrator to verify your sign-up"
msgstr "Молимо Вас да затражите од администратора да верификује Вашу регистрацију"

#: lms/lms/user.py:73
msgid "Please check your email for verification"
msgstr "Молимо Вас да проверите свој имејл за верификацију"

#: lms/templates/emails/community_course_membership.html:7
msgid "Please click on the following button to set your new password"
msgstr "Молимо Вас да кликнете на следеће дугме да поставите нову лозинку"

#: lms/lms/utils.py:2040 lms/lms/utils.py:2044
msgid "Please complete the previous courses in the program to enroll in this course."
msgstr "Молимо Вас да завршите претходне обуке у програму како бисте се уписали на ову обуку."

#: lms/lms/doctype/lms_batch/lms_batch.py:211
msgid "Please enable the zoom account to use this feature."
msgstr "Молимо Вас да омогућите Zoom налог како бисте користили ову могућност."

#: frontend/src/components/CourseOutline.vue:328
msgid "Please enroll for this course to view this lesson"
msgstr "Молимо Вас да се упишете на ову обуку да бисте приступили лекцији"

#: frontend/src/components/Quiz.vue:16
msgid "Please ensure that you complete all the questions in {0} minutes."
msgstr "Молимо Вас да се уверите да су сва питања завршена у року од {0} минута."

#: frontend/src/components/Modals/LiveClassModal.vue:186
msgid "Please enter a title."
msgstr "Молимо Вас да унесете наслов."

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:31
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:84
msgid "Please enter a valid URL."
msgstr "Молимо Вас да унесте важећи URL."

#: frontend/src/components/Modals/LiveClassModal.vue:198
msgid "Please enter a valid time in the format HH:mm."
msgstr "Молимо Вас да унесете исправно време у формату HH:mm."

#: frontend/src/components/Modals/QuizInVideo.vue:181
msgid "Please enter a valid timestamp"
msgstr "Молимо Вас да унесете важећи временски жиг"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:78
msgid "Please enter the URL for assignment submission."
msgstr "Молимо Вас да унесете URL за подношење задатка."

#: lms/templates/quiz/quiz.js:176
msgid "Please enter your answer"
msgstr "Молимо Вас да унесете одговор"

#: lms/lms/doctype/lms_batch/lms_batch.py:63
msgid "Please install the Payments App to create a paid batch. Refer to the documentation for more details. {0}"
msgstr "Молимо Вас да инсталирате апликацију за обраду плаћања да бисте креирали плаћену групу. За више детаља погледајте документацију. {0}"

#: lms/lms/doctype/lms_course/lms_course.py:55
msgid "Please install the Payments App to create a paid course. Refer to the documentation for more details. {0}"
msgstr "Молимо Вас да инсталирате апликације за обраду плаћања да бисте креирали плаћену обуку. За више детаља погледајте документацију. {0}"

#: frontend/src/pages/Billing.vue:254
msgid "Please let us know where you heard about us from."
msgstr "Молимо Вас да нам кажете како сте чули за нас."

#: frontend/src/components/QuizBlock.vue:5
msgid "Please login to access the quiz."
msgstr "Молимо Вас да се пријавите да бисте приступили квизу."

#: frontend/src/components/NoPermission.vue:25 frontend/src/pages/Batch.vue:175
msgid "Please login to access this page."
msgstr "Молимо Вас да се пријавите да бисте приступили овој страници."

#: lms/lms/api.py:210
msgid "Please login to continue with payment."
msgstr "Молимо Вас да се пријавите да бисте наставили са плаћањем."

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:7
#: lms/templates/emails/certificate_request_notification.html:7
msgid "Please prepare well and be on time for the evaluations."
msgstr "Молимо Вас да се добро припремите и стигнете на време за оцењивање."

#: frontend/src/components/UpcomingEvaluations.vue:98
msgid "Please schedule an evaluation to get certified."
msgstr "Молимо Вас да закажете оцењивање како бисте добили сертификат."

#: frontend/src/components/Modals/LiveClassModal.vue:189
msgid "Please select a date."
msgstr "Молимо Вас да изаберете датум."

#: frontend/src/components/Modals/LiveClassModal.vue:213
msgid "Please select a duration."
msgstr "Молимо Вас да изаберете трајање."

#: frontend/src/components/Modals/LiveClassModal.vue:210
msgid "Please select a future date and time."
msgstr "Молимо Вас да изаберете будући датум и време."

#: frontend/src/components/Modals/QuizInVideo.vue:186
msgid "Please select a quiz"
msgstr "Молимо Вас да изаберете квиз"

#: frontend/src/components/Modals/LiveClassModal.vue:192
msgid "Please select a time."
msgstr "Молимо Вас да изаберете време."

#: frontend/src/components/Modals/LiveClassModal.vue:195
msgid "Please select a timezone."
msgstr "Молимо Вас да изаберете временску зону."

#: frontend/src/components/Quiz.vue:523
msgid "Please select an option"
msgstr "Молимо Вас да изаберете опцију"

#: lms/templates/emails/job_report.html:6
msgid "Please take appropriate action at {0}"
msgstr "Молимо Вас да предузмете одговарајућу радњу на {0}"

#: frontend/src/components/Modals/ChapterModal.vue:175
msgid "Please upload a SCORM package"
msgstr "Молимо Вас да отпремнице SCORM пакет"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:81
msgid "Please upload the assignment file."
msgstr "Молимо Вас да отпремите фајл задатка."

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Point of Score (e.g. 70)"
msgstr "Нумерички приказ оцене (нпр. 70)"

#: frontend/src/components/Modals/Question.vue:62
msgid "Possibilities"
msgstr "Могућности"

#: frontend/src/components/Modals/Question.vue:88
msgid "Possibility"
msgstr "Могућност"

#. Label of the possibility_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 1"
msgstr "Могући одговор 1"

#. Label of the possibility_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 2"
msgstr "Могући одговор 2"

#. Label of the possibility_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 3"
msgstr "Могући одговор 3"

#. Label of the possibility_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 4"
msgstr "Могући одговор 4"

#: frontend/src/components/DiscussionReplies.vue:54
#: frontend/src/components/DiscussionReplies.vue:89
msgid "Post"
msgstr "Објави"

#: frontend/src/pages/Billing.vue:95
msgid "Postal Code"
msgstr "Поштански број"

#: frontend/src/components/AppSidebar.vue:122
msgid "Powered by Learning"
msgstr "Powered by Learning"

#. Name of a DocType
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Preferred Function"
msgstr "Пожељна функција"

#. Label of the preferred_functions (Table MultiSelect) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Functions"
msgstr "Пожељне функције"

#. Label of the preferred_industries (Table MultiSelect) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Industries"
msgstr "Пожељне индустрије"

#. Name of a DocType
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Preferred Industry"
msgstr "Пожељна индустрија"

#. Label of the preferred_location (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Location"
msgstr "Пожељна локација"

#. Label of the image (Attach Image) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Preview Image"
msgstr "Преглед слике"

#: frontend/src/pages/CourseForm.vue:195
msgid "Preview Video"
msgstr "Преглед видео-снимка"

#: frontend/src/pages/Lesson.vue:119
msgid "Previous"
msgstr "Претходно"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:255
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Pricing"
msgstr "Цене"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:221
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Pricing and Certification"
msgstr "Ценовник и сертификација"

#. Label of the exception_country (Table MultiSelect) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Primary Countries"
msgstr "Примарне државе"

#. Label of the subgroup (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Primary Subgroup"
msgstr "Примарна подгрупа"

#: lms/lms/utils.py:441
msgid "Privacy Policy"
msgstr "Политика приватности"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Private"
msgstr "Приватно"

#. Description of the 'Hide my Private Information from others' (Check) field
#. in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Private Information includes your Grade and Work Environment Preferences"
msgstr "Приватне информације укључују Вашу оцену и жеље везане за радно окружење"

#: frontend/src/pages/Billing.vue:129
msgid "Proceed to Payment"
msgstr "Настави на плаћање"

#. Label of the profession (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Profession"
msgstr "Занимање"

#: frontend/src/components/Modals/EditProfile.vue:37
msgid "Profile Image"
msgstr "Профилна слика"

#: frontend/src/pages/ProgramForm.vue:155
msgid "Program Course"
msgstr "Обука у програму"

#. Label of the program_courses (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:17
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Courses"
msgstr "Обуке у програму"

#: frontend/src/pages/ProgramForm.vue:170
msgid "Program Member"
msgstr "Члан програма"

#. Label of the program_members (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:79
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Members"
msgstr "Чланови програма"

#: frontend/src/pages/Programs.vue:206 frontend/src/pages/Programs.vue:212
#: lms/www/lms.py:295
msgid "Programs"
msgstr "Програми"

#. Label of the progress (Float) field in DocType 'LMS Enrollment'
#. Label of the progress (Int) field in DocType 'LMS Program Member'
#: frontend/src/components/Modals/BatchStudentProgress.vue:94
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "Progress"
msgstr "Напредак"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:77
msgid "Progress (%)"
msgstr "Напредак (%)"

#: frontend/src/components/BatchStudents.vue:41
msgid "Progress of students in courses and assessments"
msgstr "Напредак студената на обукама и у проценама"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Public"
msgstr "Јавни"

#. Label of the published (Check) field in DocType 'LMS Certificate'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Publish on Participant Page"
msgstr "Објави на страници учесника"

#. Label of the published (Check) field in DocType 'LMS Batch'
#. Label of the published (Check) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BulkCertificates.vue:51
#: frontend/src/components/Modals/Event.vue:108
#: frontend/src/pages/BatchForm.vue:52 frontend/src/pages/CourseForm.vue:150
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published"
msgstr "Објављено"

#: frontend/src/pages/Statistics.vue:10
#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:4
msgid "Published Courses"
msgstr "Објављене обуке"

#. Label of the published_on (Date) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:154
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published On"
msgstr "Објављено на"

#. Label of the purchased_certificate (Check) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Purchased Certificate"
msgstr "Купљени сертификат"

#. Label of the question (Small Text) field in DocType 'Course Lesson'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the question (Text Editor) field in DocType 'LMS Question'
#. Label of the question (Link) field in DocType 'LMS Quiz Question'
#. Label of the question (Text) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Assignment.vue:20
#: frontend/src/components/Modals/AssignmentForm.vue:32
#: frontend/src/components/Modals/Question.vue:27
#: frontend/src/pages/QuizForm.vue:390 frontend/src/pages/QuizSubmission.vue:56
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:104
msgid "Question"
msgstr "Питање"

#: lms/templates/quiz/quiz.html:62
msgid "Question "
msgstr "Питање "

#. Label of the question_detail (Text) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Question Detail"
msgstr "Детаљи питања"

#. Label of the question_name (Link) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Question Name"
msgstr "Назив питања"

#: frontend/src/components/Modals/Question.vue:281
msgid "Question added successfully"
msgstr "Питање је успешно додато"

#: frontend/src/components/Modals/Question.vue:331
msgid "Question updated successfully"
msgstr "Питање је успешно ажурирано"

#: frontend/src/components/Quiz.vue:102
msgid "Question {0}"
msgstr "Питање {0}"

#: frontend/src/components/Quiz.vue:204
msgid "Question {0} of {1}"
msgstr "Питање {0} од {1}"

#. Label of the questions (Table) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:117 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Questions"
msgstr "Питања"

#: frontend/src/pages/QuizForm.vue:432
msgid "Questions deleted successfully"
msgstr "Питања су успешно обрисана"

#. Label of the quiz (Link) field in DocType 'LMS Quiz Submission'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Modals/QuizInVideo.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:216
#: frontend/src/pages/QuizSubmission.vue:26 frontend/src/utils/quiz.js:24
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/workspace/lms/lms.json
msgid "Quiz"
msgstr "Квиз"

#. Label of the quiz_id (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz ID"
msgstr "ИД квиза"

#. Label of a Link in the LMS Workspace
#: frontend/src/pages/QuizPage.vue:57 lms/lms/workspace/lms/lms.json
msgid "Quiz Submission"
msgstr "Подношење квиза"

#: frontend/src/pages/QuizSubmission.vue:131
#: frontend/src/pages/QuizSubmissionList.vue:111
#: frontend/src/pages/QuizSubmissionList.vue:116
msgid "Quiz Submissions"
msgstr "Подношења квиза"

#: frontend/src/components/Quiz.vue:241
msgid "Quiz Summary"
msgstr "Резиме квиза"

#. Label of the quiz_title (Data) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Quiz Title"
msgstr "Наслов квиза"

#: frontend/src/pages/QuizForm.vue:344
msgid "Quiz created successfully"
msgstr "Квиз је успешно креиран"

#: lms/plugins.py:96
msgid "Quiz is not available to Guest users. Please login to continue."
msgstr "Квиз није доступан гостујућим корисницима. Молимо Вас да се пријавите да бисте наставили."

#: frontend/src/pages/QuizForm.vue:363
msgid "Quiz updated successfully"
msgstr "Квиз је успешно ажуриран"

#. Description of the 'Quiz ID' (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz will appear at the bottom of the lesson."
msgstr "Квиз ће бити приказиван на дну лекције."

#: frontend/src/components/AppSidebar.vue:583
#: frontend/src/pages/QuizForm.vue:443 frontend/src/pages/Quizzes.vue:142
#: frontend/src/pages/Quizzes.vue:152 lms/www/lms.py:251
msgid "Quizzes"
msgstr "Квизови"

#: frontend/src/components/Modals/QuizInVideo.vue:35
msgid "Quizzes in this video"
msgstr "Квизови у овом видео-снимку"

#. Label of the rating (Rating) field in DocType 'LMS Certificate Evaluation'
#. Label of the rating (Data) field in DocType 'LMS Course'
#. Label of the rating (Rating) field in DocType 'LMS Course Review'
#: frontend/src/components/CourseCardOverlay.vue:121
#: frontend/src/components/Modals/Event.vue:86
#: frontend/src/components/Modals/ReviewModal.vue:18
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/templates/reviews.html:125
msgid "Rating"
msgstr "Оцена"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.py:17
msgid "Rating cannot be 0"
msgstr "Оцена не може бити 0"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Ready"
msgstr "Спремно"

#. Label of the reference_docname (Dynamic Link) field in DocType 'LMS Batch
#. Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Reference DocName"
msgstr "Назив референтног документа"

#. Label of the reference_doctype (Link) field in DocType 'LMS Batch Timetable'
#. Label of the reference_doctype (Link) field in DocType 'LMS Timetable
#. Legend'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Reference DocType"
msgstr "DocType референца"

#. Label of the reference_doctype (Link) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Reference Document Type"
msgstr "Врста референтног документа"

#: lms/templates/emails/community_course_membership.html:17
msgid "Regards"
msgstr "Поздрав"

#: frontend/src/components/BatchOverlay.vue:89
msgid "Register Now"
msgstr "Региструјте се сада"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Registered"
msgstr "Регистрован"

#: lms/lms/user.py:36
msgid "Registered but disabled"
msgstr "Регистровано, али онемогућено"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Rejected"
msgstr "Одбијено"

#. Label of the related_courses (Table) field in DocType 'LMS Course'
#. Name of a DocType
#: frontend/src/components/RelatedCourses.vue:5
#: frontend/src/pages/CourseForm.vue:206
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/related_courses/related_courses.json
msgid "Related Courses"
msgstr "Сродне обуке"

#: frontend/src/pages/BatchForm.vue:236 frontend/src/pages/CourseForm.vue:127
msgid "Remove"
msgstr "Уклони"

#: frontend/src/components/Modals/AnnouncementModal.vue:27
msgid "Reply To"
msgstr "Одговори"

#: lms/lms/widgets/RequestInvite.html:7
msgid "Request Invite"
msgstr "Затражи позивницу"

#: lms/patches/create_mentor_request_email_templates.py:20
msgid "Request for Mentorship"
msgstr "Захтев за менторство"

#. Label of the required_role (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Required Role"
msgstr "Неопходна улога"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Restricted"
msgstr "Ограничено"

#. Label of the result (Table) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Result"
msgstr "Резултат"

#. Label of the resume (Attach) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Resume"
msgstr "CV"

#: frontend/src/components/Quiz.vue:75 frontend/src/components/Quiz.vue:278
msgid "Resume Video"
msgstr "Настави видео-снимак"

#. Label of the review (Small Text) field in DocType 'LMS Course Review'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Modals/ReviewModal.vue:20
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/workspace/lms/lms.json lms/templates/reviews.html:143
msgid "Review"
msgstr "Преглед"

#: lms/templates/reviews.html:100
msgid "Review the course"
msgstr "Оцените ову обуку"

#. Label of the reviewed_by (Link) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Reviewed By"
msgstr "Прегледао"

#: lms/templates/reviews.html:4
msgid "Reviews"
msgstr "Прегледи"

#. Label of the role (Select) field in DocType 'Cohort Staff'
#. Label of the role (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Role"
msgstr "Улога"

#. Label of the role (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Role Preference"
msgstr "Пожељна улога"

#: frontend/src/pages/ProfileRoles.vue:105
msgid "Role updated successfully"
msgstr "Улога је успешно ажурирана"

#: frontend/src/components/AppSidebar.vue:611
msgid "Roles"
msgstr "Улоге"

#. Label of the route (Data) field in DocType 'LMS Sidebar Item'
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Route"
msgstr "Путања"

#: lms/lms/doctype/lms_batch/lms_batch.py:139
msgid "Row #{0} Date cannot be outside the batch duration."
msgstr "Ред #{0} садржи датум ван трајања групе."

#: lms/lms/doctype/lms_batch/lms_batch.py:134
msgid "Row #{0} End time cannot be outside the batch duration."
msgstr "Ред #{0} садржи време завршетка ван трајања групе."

#: lms/lms/doctype/lms_batch/lms_batch.py:116
msgid "Row #{0} Start time cannot be greater than or equal to end time."
msgstr "Ред #{0} садржи време почетка које је веће или једнако времену завршетка"

#: lms/lms/doctype/lms_batch/lms_batch.py:125
msgid "Row #{0} Start time cannot be outside the batch duration."
msgstr "Ред #{0} садржи време почетка ван трајања групе."

#: lms/lms/doctype/lms_quiz/lms_quiz.py:32
msgid "Rows {0} have the duplicate questions."
msgstr "Редови {0} садрже дупликате питања."

#: lms/templates/livecode/extension_footer.html:21
msgid "Run"
msgstr "Покрени"

#. Label of the scorm_section (Section Break) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM"
msgstr "SCORM"

#. Label of the scorm_package (Link) field in DocType 'Course Chapter'
#: frontend/src/components/Modals/ChapterModal.vue:22
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package"
msgstr "SCORM пакет"

#. Label of the scorm_package_path (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package Path"
msgstr "SCORM путања пакета"

#. Label of the seo_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "SEO"
msgstr "SEO"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Saturday"
msgstr "Субота"

#: frontend/src/components/AssessmentPlugin.vue:34
#: frontend/src/components/Assignment.vue:46
#: frontend/src/components/Controls/CodeEditor.vue:25
#: frontend/src/components/Modals/AssignmentForm.vue:59
#: frontend/src/components/Modals/EmailTemplateModal.vue:12
#: frontend/src/components/Modals/Event.vue:101
#: frontend/src/components/Modals/Event.vue:129
#: frontend/src/components/Modals/ZoomAccountModal.vue:10
#: frontend/src/pages/BatchForm.vue:8 frontend/src/pages/CourseForm.vue:17
#: frontend/src/pages/JobForm.vue:8 frontend/src/pages/LessonForm.vue:14
#: frontend/src/pages/ProgramForm.vue:7 frontend/src/pages/QuizForm.vue:34
#: frontend/src/pages/QuizSubmission.vue:14
msgid "Save"
msgstr "Сачувај"

#. Label of the schedule (Table) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Schedule"
msgstr "Распоред"

#: frontend/src/components/Modals/EvaluationModal.vue:5
#: frontend/src/components/UpcomingEvaluations.vue:11
msgid "Schedule Evaluation"
msgstr "Закажи оцењивање"

#. Name of a DocType
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Scheduled Flow"
msgstr "Планирани ток"

#. Label of the scope (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Scope"
msgstr "Опсег"

#. Label of the score (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:39
#: frontend/src/pages/QuizSubmissionList.vue:96
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/templates/quiz/quiz.html:148
msgid "Score"
msgstr "Оцена"

#. Label of the score_out_of (Int) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Score Out Of"
msgstr "Резултат од"

#: frontend/src/components/Settings/Evaluators.vue:15
#: frontend/src/components/Settings/Members.vue:15
#: frontend/src/pages/Jobs.vue:41
msgid "Search"
msgstr "Претрага"

#: frontend/src/pages/CertifiedParticipants.vue:23
msgid "Search by Name"
msgstr "Претрага по називу"

#: frontend/src/pages/Batches.vue:45 frontend/src/pages/Courses.vue:41
msgid "Search by Title"
msgstr "Претрага по наслову"

#: frontend/src/pages/Assignments.vue:37
msgid "Search by title"
msgstr "Претрага по наслову"

#: frontend/src/components/Controls/IconPicker.vue:36
msgid "Search for an icon"
msgstr "Претражи иконицу"

#. Label of the seat_count (Int) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:147
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Seat Count"
msgstr "Број места"

#: frontend/src/components/BatchCard.vue:18
#: frontend/src/components/BatchOverlay.vue:17
msgid "Seat Left"
msgstr "Преостало место"

#: lms/lms/doctype/lms_batch/lms_batch.py:103
msgid "Seat count cannot be negative."
msgstr "Број места не може бити негативан."

#: frontend/src/components/BatchCard.vue:15
#: frontend/src/components/BatchOverlay.vue:14
msgid "Seats Left"
msgstr "Преостала места"

#: frontend/src/components/Modals/Question.vue:98
msgid "Select a question"
msgstr "Изаберите питање"

#: frontend/src/components/AssessmentPlugin.vue:21
msgid "Select a quiz"
msgstr "Изаберите квиз"

#: frontend/src/components/Modals/EvaluationModal.vue:40
msgid "Select a slot"
msgstr "Изаберите термин"

#: frontend/src/components/AssessmentPlugin.vue:28
msgid "Select an assignment"
msgstr "Изаберите задатак"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.js:7
msgid "Send Confirmation Email"
msgstr "Пошаљи имејл потврде"

#. Label of the send_calendar_invite_for_evaluations (Check) field in DocType
#. 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Send calendar invite for evaluations"
msgstr "Пошаљи календарску позивницу за оцењивање"

#. Label of the sessions_on (Data) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Sessions On Days"
msgstr "Сесије по данима"

#: lms/templates/emails/community_course_membership.html:1
msgid "Set your Password"
msgstr "Поставите своју лозинку"

#: frontend/src/components/AppSidebar.vue:559
msgid "Setting up"
msgstr "Подешавање"

#: frontend/src/components/AppSidebar.vue:604
msgid "Setting up payment gateway"
msgstr "Подешавање платног портала"

#: frontend/src/components/AppSidebar.vue:609
#: frontend/src/components/Settings/Settings.vue:7
#: frontend/src/pages/BatchForm.vue:46 frontend/src/pages/CourseForm.vue:143
#: frontend/src/pages/ProfileRoles.vue:4 frontend/src/pages/QuizForm.vue:79
msgid "Settings"
msgstr "Подешавања"

#: frontend/src/pages/ProfileAbout.vue:62
msgid "Share on"
msgstr "Подели на"

#: frontend/src/pages/BatchForm.vue:35
msgid "Short Description"
msgstr "Кратак опис"

#. Label of the short_introduction (Small Text) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:80
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Short Introduction"
msgstr "Кратак увод"

#: frontend/src/pages/BatchForm.vue:38
msgid "Short description of the batch"
msgstr "Кратак опис групе"

#. Label of the show_answer (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Show Answer"
msgstr "Прикажи одговор"

#. Label of the show_answers (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:85 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Answers"
msgstr "Прикажи одговоре"

#. Label of the show_submission_history (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:90 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Submission History"
msgstr "Прикажи историју поднесака"

#. Label of the column_break_2 (Column Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show Tab in Batch"
msgstr "Прикажи картицу у групи"

#. Label of the show_usd_equivalent (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show USD Equivalent"
msgstr "Прикажи еквивалент америчког долара (USD)"

#. Label of the show_day_view (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show day view in timetable"
msgstr "Прикажи дневни приказ у распореду наставе"

#. Label of the show_live_class (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Show live class"
msgstr "Прикажи онлајн предавања"

#. Label of the shuffle_questions (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:103 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Shuffle Questions"
msgstr "Промешај питања"

#: frontend/src/pages/QuizForm.vue:97
msgid "Shuffle Settings"
msgstr "Подешавање мешања"

#. Label of the sidebar_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar"
msgstr "Бочна трака"

#. Label of the sidebar_items (Table) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar Items"
msgstr "Ставке бочне траке"

#: lms/lms/user.py:29
msgid "Sign Up is disabled"
msgstr "Регистрација је онемогућена"

#: lms/templates/signup-form.html:53
msgid "Sign up"
msgstr "Региструј се"

#. Label of the signup_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Signup Email"
msgstr "Имејл за регистрацију"

#. Label of the signup_settings_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Signup Settings"
msgstr "Подешавање регистрације"

#. Label of a chart in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Signups"
msgstr "Регистрације"

#. Label of the skill (Table MultiSelect) field in DocType 'User'
#. Label of the skill (Data) field in DocType 'User Skill'
#: lms/fixtures/custom_field.json lms/lms/doctype/user_skill/user_skill.json
msgid "Skill"
msgstr "Вештина"

#. Label of the skill_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Skill Details"
msgstr "Детаљи вештине"

#. Label of the skill_name (Link) field in DocType 'Skills'
#: lms/lms/doctype/skills/skills.json
msgid "Skill Name"
msgstr "Назив вештине"

#. Name of a DocType
#: lms/lms/doctype/skills/skills.json
msgid "Skills"
msgstr "Вештине"

#: frontend/src/pages/PersonaForm.vue:51 lms/templates/onboarding_header.html:6
msgid "Skip"
msgstr "Прескочи"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:63
msgid "Slot Times are overlapping for some schedules."
msgstr "Временски термини се преклапају за неке распореде."

#: frontend/src/pages/ProfileEvaluator.vue:201
msgid "Slot added successfully"
msgstr "Термин је успешно додат"

#: frontend/src/pages/ProfileEvaluator.vue:240
msgid "Slot deleted successfully"
msgstr "Термин је успешно обрисан"

#. Label of the slug (Data) field in DocType 'Cohort'
#. Label of the slug (Data) field in DocType 'Cohort Subgroup'
#. Label of the slug (Data) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Slug"
msgstr "Slug"

#: frontend/src/components/BatchCard.vue:25
#: frontend/src/components/BatchOverlay.vue:24
msgid "Sold Out"
msgstr "Распродато"

#. Label of the solution (Code) field in DocType 'Exercise Latest Submission'
#. Label of the solution (Code) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Solution"
msgstr "Решење"

#. Label of the source (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the source (Link) field in DocType 'LMS Payment'
#. Label of the source (Data) field in DocType 'LMS Source'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_source/lms_source.json
msgid "Source"
msgstr "Извор"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Staff"
msgstr "Особље"

#. Label of the stage (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Stage"
msgstr "Фаза"

#: frontend/src/components/LiveClass.vue:70 frontend/src/components/Quiz.vue:71
#: lms/templates/quiz/quiz.html:39
msgid "Start"
msgstr "Почетак"

#. Label of the start_date (Date) field in DocType 'Education Detail'
#. Label of the start_date (Date) field in DocType 'LMS Batch'
#. Label of the start_date (Date) field in DocType 'LMS Batch Old'
#: frontend/src/pages/BatchForm.vue:75
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Start Date"
msgstr "Датум почетка"

#: lms/templates/emails/batch_start_reminder.html:13
msgid "Start Date:"
msgstr "Датум почетка:"

#: frontend/src/components/CourseCardOverlay.vue:67
#: frontend/src/pages/Lesson.vue:39 frontend/src/pages/SCORMChapter.vue:28
#: lms/templates/emails/lms_course_interest.html:9
msgid "Start Learning"
msgstr "Започни учење"

#. Label of the start_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the start_time (Time) field in DocType 'LMS Batch'
#. Label of the start_time (Time) field in DocType 'LMS Batch Old'
#. Label of the start_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the start_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:91
#: frontend/src/pages/ProfileEvaluator.vue:29
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Start Time"
msgstr "Време почетка"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:34
msgid "Start Time cannot be greater than End Time"
msgstr "Време почетка не може бити веће од времена завршетка"

#. Label of the start_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Start URL"
msgstr "Почетни URL"

#: frontend/src/components/Quiz.vue:71
msgid "Start the Quiz"
msgstr "Започни квиз"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Startup Organization"
msgstr "Стартуп организација"

#: frontend/src/pages/Billing.vue:83
msgid "State"
msgstr "Стање"

#. Label of the tab_4_tab (Tab Break) field in DocType 'LMS Course'
#. Label of the statistics (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:5
#: frontend/src/pages/Statistics.vue:225
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:204
msgid "Statistics"
msgstr "Статистика"

#. Label of the status (Select) field in DocType 'Job Opportunity'
#. Label of the status (Select) field in DocType 'Cohort'
#. Label of the status (Select) field in DocType 'Cohort Join Request'
#. Label of the status (Select) field in DocType 'Exercise Latest Submission'
#. Label of the status (Select) field in DocType 'Exercise Submission'
#. Label of the status (Select) field in DocType 'Invite Request'
#. Label of the status (Select) field in DocType 'LMS Assignment Submission'
#. Label of the status (Select) field in DocType 'LMS Batch Old'
#. Label of the status (Select) field in DocType 'LMS Certificate Evaluation'
#. Label of the status (Select) field in DocType 'LMS Certificate Request'
#. Label of the status (Select) field in DocType 'LMS Course'
#. Label of the status (Select) field in DocType 'LMS Course Progress'
#. Label of the status (Select) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Modals/Event.vue:91
#: frontend/src/components/Settings/ZoomSettings.vue:182
#: frontend/src/pages/AssignmentSubmissionList.vue:19
#: frontend/src/pages/JobForm.vue:46
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Status"
msgstr "Статус"

#: lms/templates/assessments.html:17
msgid "Status/Score"
msgstr "Статус/Резултат"

#. Option for the 'User Category' (Select) field in DocType 'User'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: frontend/src/pages/ProfileRoles.vue:38 lms/fixtures/custom_field.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/templates/signup-form.html:26
msgid "Student"
msgstr "Студент"

#: frontend/src/components/CourseReviews.vue:11
msgid "Student Reviews"
msgstr "Рецензије студената"

#. Label of the show_students (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:11
#: frontend/src/components/BatchStudents.vue:67
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Students"
msgstr "Студенти"

#: frontend/src/components/BatchStudents.vue:285
msgid "Students deleted successfully"
msgstr "Студенти су успешно обрисани"

#. Description of the 'Paid Batch' (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Students will be enrolled in a paid batch once they complete the payment"
msgstr "Студенти ће бити уписани у плаћену групу након што изврше уплату"

#. Label of the subgroup (Link) field in DocType 'Cohort Join Request'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the subgroup (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Subgroup"
msgstr "Подгрупа"

#: frontend/src/components/Modals/AnnouncementModal.vue:20
#: frontend/src/components/Modals/EmailTemplateModal.vue:31
msgid "Subject"
msgstr "Наслов"

#: frontend/src/components/Modals/AnnouncementModal.vue:93
msgid "Subject is required"
msgstr "Наслов је неопходан"

#: frontend/src/components/Assignment.vue:32
msgid "Submission"
msgstr "Поднесак"

#: frontend/src/pages/QuizForm.vue:30
msgid "Submission List"
msgstr "Листа поднесака"

#: frontend/src/components/Modals/AssignmentForm.vue:27
msgid "Submission Type"
msgstr "Врста поднесака"

#: frontend/src/components/Assignment.vue:13
#: frontend/src/components/Assignment.vue:16
msgid "Submission by"
msgstr "Поднето од стране"

#: frontend/src/components/Modals/AssessmentModal.vue:9
#: frontend/src/components/Modals/BatchCourseModal.vue:9
#: frontend/src/components/Modals/EvaluationModal.vue:9
#: frontend/src/components/Modals/Question.vue:109
#: frontend/src/components/Quiz.vue:232 lms/templates/assignment.html:9
#: lms/templates/livecode/extension_footer.html:25
#: lms/templates/quiz/quiz.html:128 lms/templates/reviews.html:163
#: lms/www/new-sign-up.html:32
msgid "Submit"
msgstr "Поднеси"

#: frontend/src/components/BatchFeedback.vue:35
msgid "Submit Feedback"
msgstr "Поднеси повратну информацију"

#: frontend/src/pages/PersonaForm.vue:43
msgid "Submit and Continue"
msgstr "Поднеси и настави"

#: frontend/src/components/Modals/JobApplicationModal.vue:23
msgid "Submit your resume to proceed with your application for this position. Upon submission, it will be shared with the job poster."
msgstr "Поднесите свој CV да бисте наставили са пријавом на ову позицију. Након предаје, Ваш CV ће бити прослеђен оглашивачу посла."

#: lms/templates/livecode/extension_footer.html:85
#: lms/templates/livecode/extension_footer.html:115
msgid "Submitted {0}"
msgstr "Поднето {0}"

#. Label of the summary (Small Text) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:97
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Summary"
msgstr "Резиме"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Sunday"
msgstr "Недеља"

#: lms/lms/api.py:1076
msgid "Suspicious pattern found in {0}: {1}"
msgstr "Сумњив образац пронађен у {0}: {1}"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/job_settings/job_settings.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/user_skill/user_skill.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "System Manager"
msgstr "Систем менаџер"

#. Label of the tags (Data) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:51
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Tags"
msgstr "Ознаке"

#: lms/templates/emails/community_course_membership.html:18
#: lms/templates/emails/mentor_request_creation_email.html:8
#: lms/templates/emails/mentor_request_status_update_email.html:7
msgid "Team School"
msgstr "Тим школе"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Team Work"
msgstr "Тимски рад"

#. Label of the template (Link) field in DocType 'Cohort Web Page'
#. Label of the template (Link) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:43
#: frontend/src/components/Modals/Event.vue:112
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Template"
msgstr "Шаблон"

#: lms/lms/user.py:40
msgid "Temporarily Disabled"
msgstr "Привремено онемогућено"

#: lms/lms/utils.py:440
msgid "Terms of Use"
msgstr "Услови коришћења"

#. Label of the test_results (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the test_results (Small Text) field in DocType 'Exercise
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Test Results"
msgstr "Резултати теста"

#. Label of the tests (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Tests"
msgstr "Тестови"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Text"
msgstr "Текст"

#: frontend/src/components/BatchFeedback.vue:6
msgid "Thank you for providing your feedback."
msgstr "Хвала Вам што сте поделили своје утиске."

#: lms/templates/emails/lms_course_interest.html:17
#: lms/templates/emails/lms_invite_request_approved.html:15
#: lms/templates/emails/mentor_request_creation_email.html:7
#: lms/templates/emails/mentor_request_status_update_email.html:6
msgid "Thanks and Regards"
msgstr "Хвала и срдачан поздрав"

#: lms/lms/utils.py:1938
msgid "The batch is full. Please contact the Administrator."
msgstr "Група је попуњена. Молимо Вас да контактирате администратора."

#: lms/templates/emails/batch_start_reminder.html:6
msgid "The batch you have enrolled for is starting tomorrow. Please be prepared and be on time for the session."
msgstr "Група у коју сте се уписали почиње сутра. Молимо Вас да будете спремни и тачни за сесију."

#: lms/templates/emails/lms_course_interest.html:5
msgid "The course {0} is now available on {1}."
msgstr "Обука {0} је сада доступна на {1}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:53
msgid "The evaluator of this course is unavailable from {0} to {1}. Please select a date after {1}"
msgstr "Особа која оцењује ову обуку није доступна у периоду од {0} до {1}. Молимо Вас да изаберете датум након {1}"

#: lms/templates/quiz/quiz.html:24
msgid "The quiz has a time limit. For each question you will be given {0} seconds."
msgstr "Квиз је временски ограничен. За свако питање биће Вам додељено {0} секунди."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:71
msgid "The slot is already booked by another participant."
msgstr "Термин је већ резервисан од стране другог учесника."

#: lms/patches/create_mentor_request_email_templates.py:40
msgid "The status of your application has changed."
msgstr "Статус Ваше пријаве је промењен."

#: frontend/src/components/CreateOutline.vue:12
msgid "There are no chapters in this course. Create and manage chapters from here."
msgstr "У овој обуци нема поглавља. Овде можете креирати и уређивати поглавља."

#: lms/lms/doctype/lms_batch/lms_batch.py:107
msgid "There are no seats available in this batch."
msgstr "Нема слободних места у овој групи."

#: frontend/src/components/BatchStudents.vue:155
msgid "There are no students in this batch."
msgstr "У овој групи нема студената."

#: frontend/src/pages/AssignmentSubmissionList.vue:70
msgid "There are no submissions for this assignment."
msgstr "Нема поднесака за овај задатак."

#: frontend/src/components/EmptyState.vue:11
msgid "There are no {0} currently. Keep an eye out, fresh learning experiences are on the way!"
msgstr "Тренутно нема {0}. Пратите нас, ускоро стижу нова искуства учења!"

#: lms/templates/course_list.html:14
msgid "There are no {0} on this site."
msgstr "На овој сајту нема {0}."

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:44
msgid "There has been an update on your submission for assignment {0}"
msgstr "Дошло је до измене у Вашем поднеску за задатак {0}"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:59
msgid "There has been an update on your submission. You have got a score of {0} for the quiz {1}"
msgstr "Дошло је до измене Вашег поднеска. Имали сте резултат од {0} поена на квизу {1}"

#. Description of the 'section_break_ubxi' (Section Break) field in DocType
#. 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "These customisations will work on the main batch page."
msgstr "Ова прилагођавања ће радити на главној страници групе."

#: frontend/src/pages/Badge.vue:14
msgid "This badge has been awarded to {0} on {1}."
msgstr "Овај беџ је додељен за {0} на {1}."

#. Label of the expire (Check) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "This certificate does no expire"
msgstr "Овај сертификат нема рок трајања"

#: frontend/src/components/LiveClass.vue:83
msgid "This class has ended"
msgstr "Ово предавање се завршило"

#: frontend/src/components/CourseCardOverlay.vue:100
msgid "This course has:"
msgstr "Ова обука садржи:"

#: lms/lms/utils.py:1781
msgid "This course is free."
msgstr "Ова обука је бесплатна."

#. Description of the 'Meta Description' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This description will be shown on lists and pages without meta description"
msgstr "Овај опис ће се приказивати на листама и страницама које немају мета опис"

#. Description of the 'Meta Image' (Attach Image) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This image will be shown on lists and pages that don't have an image by default"
msgstr "Ова слика ће се приказивати на листама и страницама које немају подразумевану слику"

#: frontend/src/pages/Lesson.vue:24
msgid "This lesson is locked"
msgstr "Лекција је закључана"

#: frontend/src/pages/Lesson.vue:29
msgid "This lesson is not available for preview. Please enroll in the course to access it."
msgstr "Ова лекција није доступна за преглед. Молимо Вас да се упишете на обуку да бисте јој приступили."

#: lms/lms/widgets/NoPreviewModal.html:16
msgid "This lesson is not available for preview. Please join the course to access it."
msgstr "Ова лекција није доступна за преглед. Молимо Вас да се придружите обуци да бисте јој приступили."

#: frontend/src/components/Quiz.vue:11 lms/templates/quiz/quiz.html:6
msgid "This quiz consists of {0} questions."
msgstr "Квиз се састоји од {0} питања."

#: frontend/src/components/AppSidebar.vue:75
#: frontend/src/components/AppSidebar.vue:115
msgid "This site is being updated. You will not be able to make any changes. Full access will be restored shortly."
msgstr "Сајт се ажурира. Тренутно нису могуће измене. Пун приступ ће ускоро бити враћен."

#: frontend/src/components/VideoBlock.vue:5
msgid "This video contains {0} {1}:"
msgstr "Овај видео-снимак садржи {0} {1}:"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Thursday"
msgstr "Четвртак"

#. Label of the time (Time) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:48
#: frontend/src/components/Modals/LiveClassModal.vue:52
#: frontend/src/components/Quiz.vue:48
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Time"
msgstr "Време"

#. Label of the time (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Time Preference"
msgstr "Временска преференција"

#: frontend/src/components/Modals/QuizInVideo.vue:13
msgid "Time in Video"
msgstr "Време у видео-снимку"

#: frontend/src/components/Modals/QuizInVideo.vue:220
msgid "Time in Video (minutes)"
msgstr "Време у видео-снимку (у минутима)"

#: frontend/src/components/Modals/QuizInVideo.vue:173
msgid "Time in video exceeds the total duration of the video."
msgstr "Време у видео-снимку прелази укупно трајање видео-снимка."

#: frontend/src/components/Modals/LiveClassModal.vue:44
msgid "Time must be in 24 hour format (HH:mm). Example 11:30 or 22:00"
msgstr "Време мора бити у 24 часовном формату (HH:mm). На пример 11:30 или 22:00"

#. Label of the schedule_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Timetable Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable"
msgstr "Распоред наставе"

#. Label of the timetable_legends (Table) field in DocType 'LMS Batch'
#. Label of the timetable_legends (Table) field in DocType 'LMS Timetable
#. Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable Legends"
msgstr "Легенде распореда наставе"

#. Label of the timetable_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Timetable Template"
msgstr "Шаблон распореда наставе"

#. Label of the timezone (Data) field in DocType 'LMS Batch'
#. Label of the timezone (Data) field in DocType 'LMS Certificate Request'
#. Label of the timezone (Data) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:59
#: frontend/src/pages/BatchForm.vue:107
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Timezone"
msgstr "Временска зона"

#: lms/templates/emails/batch_confirmation.html:21
#: lms/templates/emails/batch_start_reminder.html:16
#: lms/templates/emails/live_class_reminder.html:16
msgid "Timings:"
msgstr "Временски термини:"

#. Label of the title (Data) field in DocType 'Cohort'
#. Label of the title (Data) field in DocType 'Cohort Subgroup'
#. Label of the title (Data) field in DocType 'Cohort Web Page'
#. Label of the title (Data) field in DocType 'Course Chapter'
#. Label of the title (Data) field in DocType 'Course Lesson'
#. Label of the title (Data) field in DocType 'LMS Assignment'
#. Label of the title (Data) field in DocType 'LMS Badge'
#. Label of the title (Data) field in DocType 'LMS Batch'
#. Label of the title (Data) field in DocType 'LMS Batch Old'
#. Label of the title (Data) field in DocType 'LMS Course'
#. Label of the title (Data) field in DocType 'LMS Exercise'
#. Label of the title (Data) field in DocType 'LMS Live Class'
#. Label of the title (Data) field in DocType 'LMS Program'
#. Label of the title (Data) field in DocType 'LMS Quiz'
#. Label of the title (Data) field in DocType 'LMS Sidebar Item'
#. Label of the title (Data) field in DocType 'LMS Timetable Template'
#. Label of the title (Data) field in DocType 'Work Experience'
#: frontend/src/components/Modals/AssignmentForm.vue:20
#: frontend/src/components/Modals/DiscussionModal.vue:18
#: frontend/src/components/Modals/LiveClassModal.vue:23
#: frontend/src/pages/Assignments.vue:165 frontend/src/pages/BatchForm.vue:20
#: frontend/src/pages/CourseForm.vue:30 frontend/src/pages/JobForm.vue:20
#: frontend/src/pages/ProgramForm.vue:11 frontend/src/pages/Programs.vue:101
#: frontend/src/pages/QuizForm.vue:48 frontend/src/pages/Quizzes.vue:120
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Title"
msgstr "Наслов"

#: frontend/src/components/Modals/ChapterModal.vue:172
msgid "Title is required"
msgstr "Наслов је неопходан"

#. Label of the unavailable_to (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:112
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "To"
msgstr "За"

#. Label of the to_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "To Date"
msgstr "Датум завршетка"

#: lms/lms/utils.py:1792
msgid "To join this batch, please contact the Administrator."
msgstr "За придруживање овој групи, молимо Вас да контактирате администратора."

#: lms/lms/user.py:41
msgid "Too many users signed up recently, so the registration is disabled. Please try back in an hour"
msgstr "Превише корисника се регистровало у последње време, стога је регистрација привремено онемогућена. Покушајте поново за сат времена"

#: frontend/src/pages/Billing.vue:53
msgid "Total"
msgstr "Укупно"

#. Label of the total_marks (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:67 frontend/src/pages/Quizzes.vue:125
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Total Marks"
msgstr "Укупан број поена"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:12
msgid "Total Signups"
msgstr "Укупно регистрација"

#: frontend/src/components/Modals/FeedbackModal.vue:11
msgid "Training Feedback"
msgstr "Повратне информације о тренингу"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Travel"
msgstr "Путовање"

#: frontend/src/components/Quiz.vue:274 lms/templates/quiz/quiz.html:131
msgid "Try Again"
msgstr "Покушајте поново"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Tuesday"
msgstr "Уторак"

#: frontend/src/pages/ProfileAbout.vue:86
msgid "Twitter"
msgstr "Twitter"

#. Label of the type (Select) field in DocType 'Job Opportunity'
#. Label of the type (Select) field in DocType 'LMS Assignment'
#. Label of the type (Select) field in DocType 'LMS Assignment Submission'
#. Label of the type (Select) field in DocType 'LMS Question'
#. Label of the type (Select) field in DocType 'LMS Quiz Question'
#: frontend/src/components/Modals/AssessmentModal.vue:22
#: frontend/src/components/Modals/Question.vue:44
#: frontend/src/pages/Assignments.vue:43 frontend/src/pages/Assignments.vue:170
#: frontend/src/pages/JobForm.vue:25 frontend/src/pages/Jobs.vue:65
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/templates/assessments.html:14
msgid "Type"
msgstr "Врста"

#: frontend/src/utils/markdownParser.js:11
msgid "Type '/' for commands or select text to format"
msgstr "Укуцајте '/' за команде или означите текст за форматирање"

#: frontend/src/components/Quiz.vue:636
msgid "Type your answer"
msgstr "Унесите свој одговор"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "UK Grading  (e.g. 1st, 2:2)"
msgstr "UK систем оцењивања  (нпр. први, 2:2)"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "URL"
msgstr "URL"

#. Label of the uuid (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "UUID"
msgstr "UUID"

#. Label of the unavailability_section (Section Break) field in DocType 'Course
#. Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Unavailability"
msgstr "Недоступност"

#: frontend/src/pages/ProfileEvaluator.vue:259
msgid "Unavailability updated successfully"
msgstr "Недоступност је успешно ажурирана"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:29
msgid "Unavailable From Date cannot be greater than Unavailable To Date"
msgstr "Датум почетка недоступности не може бити након датума завршетка недоступности"

#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Under Review"
msgstr "Преглед у току"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Unlisted"
msgstr "Није на листи"

#: frontend/src/pages/Batches.vue:284 frontend/src/pages/Courses.vue:322
msgid "Unpublished"
msgstr "Необјављено"

#: frontend/src/components/Modals/EditCoverImage.vue:60
#: frontend/src/components/UnsplashImageBrowser.vue:54
msgid "Unsplash"
msgstr "Unsplash"

#. Label of the unsplash_access_key (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Unsplash Access Key"
msgstr "Unsplash приступни кључ"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Unstructured Role"
msgstr "Неструктурирана улога"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#. Label of the upcoming (Check) field in DocType 'LMS Course'
#: frontend/src/pages/Batches.vue:282 frontend/src/pages/CourseForm.vue:162
#: frontend/src/pages/Courses.vue:313 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Upcoming"
msgstr "Предстојеће"

#: frontend/src/pages/Batch.vue:187
msgid "Upcoming Batches"
msgstr "Предстојеће групе"

#: frontend/src/components/UpcomingEvaluations.vue:5
#: lms/templates/upcoming_evals.html:3
msgid "Upcoming Evaluations"
msgstr "Предстојећа оцењивања"

#: frontend/src/components/Settings/BrandSettings.vue:24
#: frontend/src/components/Settings/PaymentSettings.vue:27
#: frontend/src/components/Settings/SettingDetails.vue:23
msgid "Update"
msgstr "Ажурирај"

#: lms/templates/emails/community_course_membership.html:11
msgid "Update Password"
msgstr "Ажурирај лозинку"

#: frontend/src/pages/BatchForm.vue:217 frontend/src/pages/CourseForm.vue:108
msgid "Upload"
msgstr "Отпреми"

#: frontend/src/components/Assignment.vue:81
msgid "Upload File"
msgstr "Отпреми фајл"

#: frontend/src/components/Assignment.vue:80
msgid "Uploading {0}%"
msgstr "Отпремање {0}%"

#: frontend/src/components/Modals/EmailTemplateModal.vue:38
msgid "Use HTML"
msgstr "Користите HTML"

#. Label of the user (Link) field in DocType 'LMS Job Application'
#. Label of the email (Link) field in DocType 'Cohort Staff'
#. Label of the user (Link) field in DocType 'LMS Course Interest'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "User"
msgstr "Корисник"

#. Label of the user_category (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:17
msgid "User Category"
msgstr "Категорија корисника"

#. Label of the user_field (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "User Field"
msgstr "Корисничко поље"

#. Label of the user_image (Attach Image) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "User Image"
msgstr "Слика корисника"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "User Input"
msgstr "Кориснички унос"

#. Name of a DocType
#: lms/lms/doctype/user_skill/user_skill.json
msgid "User Skill"
msgstr "Вештина корисника"

#: lms/job/doctype/job_opportunity/job_opportunity.py:40
msgid "User {0} has reported the job post {1}"
msgstr "Корисник {0} је пријавио оглас за посао {1}"

#. Label of the username (Data) field in DocType 'Course Evaluator'
#. Label of the username (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Username"
msgstr "Корисничко име"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Users"
msgstr "Корисници"

#. Label of the answer (Small Text) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Users Response"
msgstr "Одговор корисника"

#: lms/templates/signup-form.html:83
msgid "Valid email and name required"
msgstr "Неопходни су важећи имејл и назив"

#. Label of the value (Rating) field in DocType 'LMS Batch Feedback'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Value"
msgstr "Вредност"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Value Change"
msgstr "Промена вредности"

#. Label of the video_link (Data) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Video Embed Link"
msgstr "Линк за уметање видео-снимка"

#: frontend/src/pages/Notifications.vue:39
msgid "View"
msgstr "Приказ"

#: frontend/src/components/CertificationLinks.vue:10
#: frontend/src/components/Modals/Event.vue:67
msgid "View Certificate"
msgstr "Приказ сертификата"

#: frontend/src/components/BatchFeedback.vue:56
msgid "View all feedback"
msgstr "Погледај све повратне информације"

#. Label of the visibility (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Visibility"
msgstr "Видљивост"

#: frontend/src/components/BatchOverlay.vue:69
msgid "Visit Batch"
msgstr "Посети групу"

#: frontend/src/pages/JobDetail.vue:41
msgid "Visit Website"
msgstr "Посети веб-сајт"

#: lms/templates/emails/batch_confirmation.html:25
msgid "Visit the following link to view your "
msgstr "Посетите следећи линк да бисте погледали свој "

#: lms/templates/emails/batch_start_reminder.html:23
#: lms/templates/emails/live_class_reminder.html:20
msgid "Visit your batch"
msgstr "Посетите своју групу"

#. Label of the internship (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Volunteering or Internship"
msgstr "Волонтирање или пракса"

#: lms/templates/emails/batch_confirmation.html:6
msgid "We are pleased to inform you that you have been enrolled in our upcoming batch. Congratulations!"
msgstr "Драго нам је да Вас обавестимо да сте уписани у нашу предстојећу групу. Честитамо!"

#: lms/templates/emails/payment_reminder.html:7
msgid "We have a limited number of seats, and they won't be available for long!"
msgstr "Имамо ограничен број места, која ће брзо бити попуњена!"

#: lms/templates/emails/payment_reminder.html:4
msgid "We noticed that you started enrolling in the"
msgstr "Приметили смо да сте започели упис за"

#. Label of the web_page (Link) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:23
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Web Page"
msgstr "Веб-страница"

#: frontend/src/components/Modals/PageModal.vue:80
msgid "Web page added to sidebar"
msgstr "Веб-страница је додата у бочну траку"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Wednesday"
msgstr "Среда"

#: lms/lms/doctype/invite_request/invite_request.py:40
#: lms/templates/emails/lms_invite_request_approved.html:4
msgid "Welcome to {0}!"
msgstr "Добро дошли у {0}!"

#: frontend/src/pages/PersonaForm.vue:32
msgid "What best describes your role?"
msgstr "Шта најбоље описује Вашу улогу?"

#: frontend/src/components/LessonHelp.vue:6
msgid "What does include in preview mean?"
msgstr "Шта значи укључи у преглед?"

#: frontend/src/pages/PersonaForm.vue:21
msgid "What is your use case for Frappe Learning?"
msgstr "Која је Ваша намена за Frappe Learning?"

#: lms/templates/courses_under_review.html:15
msgid "When a course gets submitted for review, it will be listed here."
msgstr "Када се обука поднесе на преглед, биће приказана овде."

#: frontend/src/pages/Billing.vue:106
msgid "Where did you hear about us?"
msgstr "Где сте чули о нама?"

#: lms/templates/emails/certification.html:10
msgid "With this certification, you can now showcase your updated skills and share your achievement with your colleagues and on LinkedIn. To access your certificate, please click on the link provided below. Make sure you are logged in to the portal."
msgstr "Уз ову сертификацију сада можете показати своја унапређена знања и поделити своје постигнуће са колегама и на LinkedIn-у. Да бисте приступили сертификату, кликните на линк испод. Уверите се да сте пријављени на портал."

#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Withdrawn"
msgstr "Повучено"

#. Label of the work_environment (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Environment"
msgstr "Радно окружење"

#. Label of the work_experience (Table) field in DocType 'User'
#. Name of a DocType
#: lms/fixtures/custom_field.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Work Experience"
msgstr "Радно искуство"

#. Label of the work_experience_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Experience Details"
msgstr "Детаљи радног искуства"

#: frontend/src/components/CourseReviews.vue:8
#: frontend/src/components/Modals/ReviewModal.vue:5
#: lms/templates/reviews.html:117
msgid "Write a Review"
msgstr "Напишите рецензију"

#: lms/templates/reviews.html:31 lms/templates/reviews.html:103
#: lms/templates/reviews_cta.html:3 lms/templates/reviews_cta.html:7
msgid "Write a review"
msgstr "Напишите рецензију"

#: frontend/src/components/Assignment.vue:123
msgid "Write your answer here"
msgstr "Овде упишите Ваш одговор"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:95
msgid "You already have an evaluation on {0} at {1} for the course {2}."
msgstr "Већ постоји оцена на {0} у {1} за обуку {2}."

#: frontend/src/pages/CourseCertification.vue:14
msgid "You are already certified for this course. Click on the card below to open your certificate."
msgstr "Већ имате сертификат за ову обуку. Кликните на картицу испод да отворите свој сертификат."

#: lms/lms/api.py:234
msgid "You are already enrolled for this batch."
msgstr "Већ сте уписани на ову групу."

#: lms/lms/api.py:226
msgid "You are already enrolled for this course."
msgstr "Већ сте уписани на ову обуку."

#: frontend/src/pages/Batch.vue:169
msgid "You are not a member of this batch. Please checkout our upcoming batches."
msgstr "Нисте члан ове групе. Погледајте наше предстојеће групе."

#: lms/lms/doctype/lms_batch_old/lms_batch_old.py:20
msgid "You are not a mentor of the course {0}"
msgstr "Нисте ментор обуке {0}"

#: frontend/src/pages/SCORMChapter.vue:22
msgid "You are not enrolled in this course. Please enroll to access this lesson."
msgstr "Нисте уписани у ову обуку. Молимо Вас да се упишете да бисте приступили овој лекцији."

#: lms/templates/emails/lms_course_interest.html:13
#: lms/templates/emails/lms_invite_request_approved.html:11
msgid "You can also copy-paste following link in your browser"
msgstr "Такође можете копирати и налепити следећи линк у Вашем интернет претраживачу"

#: lms/templates/quiz/quiz.html:18
msgid "You can attempt this quiz only {0} {1}"
msgstr "Овај квиз можете покушати да урадите само {0} {1}"

#: frontend/src/components/Quiz.vue:37
msgid "You can attempt this quiz {0}."
msgstr "Овај квиз можете покушати да урадите само {0}."

#: lms/templates/emails/job_application.html:6
msgid "You can find their resume attached to this email."
msgstr "Можете пронаћи њихов CV који је приложен у овом имејлу."

#: frontend/src/pages/ProfileEvaluator.vue:14
msgid "You cannot change the availability when the site is being updated."
msgstr "Не можете мењати доступност док се сајт ажурира."

#: frontend/src/pages/ProfileRoles.vue:12
msgid "You cannot change the roles in read-only mode."
msgstr "Не можете мењати улоге у режиму само за читање."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:115
msgid "You cannot schedule evaluations after {0}."
msgstr "Не можете заказати оцењивање након {0}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:104
msgid "You cannot schedule evaluations for past slots."
msgstr "Оцењивање није могуће заказати за термине који су већ прошли."

#: frontend/src/components/NoPermission.vue:11
msgid "You do not have permission to access this page."
msgstr "Немате дозволу за приступ овој страници."

#: lms/templates/notifications.html:27
msgid "You don't have any notifications."
msgstr "Немате ниједно обавештење."

#: lms/templates/quiz/quiz.js:137
msgid "You got"
msgstr "Остварили сте"

#: frontend/src/components/Quiz.vue:255
#, python-format
msgid "You got {0}% correct answers with a score of {1} out of {2}"
msgstr "Имате {0}% тачних одговора са резултатом од {1} од могућих {2}"

#: lms/templates/emails/live_class_reminder.html:6
msgid "You have a live class scheduled tomorrow. Please be prepared and be on time for the session."
msgstr "Имате онлајн предавање заказано за сутра. Молимо Вас да се припремите и придружите на време."

#: lms/job/doctype/lms_job_application/lms_job_application.py:22
msgid "You have already applied for this job."
msgstr "Већ сте се пријавили за овај посао."

#: frontend/src/components/Quiz.vue:86 lms/templates/quiz/quiz.html:43
msgid "You have already exceeded the maximum number of attempts allowed for this quiz."
msgstr "Већ сте премашили максималан дозвољени број покушаја за овај квиз."

#: lms/lms/api.py:258
msgid "You have already purchased the certificate for this course."
msgstr "Већ сте купили сертификат за ову обуку."

#: lms/lms/doctype/lms_course_review/lms_course_review.py:17
msgid "You have already reviewed this course"
msgstr "Већ сте оценили ову обуку"

#: frontend/src/pages/JobDetail.vue:57
msgid "You have applied"
msgstr "Пријавили сте се"

#: frontend/src/components/BatchOverlay.vue:159
msgid "You have been enrolled in this batch"
msgstr "Уписани сте у ову групу"

#: frontend/src/components/CourseCardOverlay.vue:187
msgid "You have been enrolled in this course"
msgstr "Уписани сте на ову обуку"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:30
msgid "You have exceeded the maximum number of attempts ({0}) for this quiz"
msgstr "Премашили сте максималан дозвољени број покушаја ({0}) за овај квиз"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:56
msgid "You have got a score of {0} for the quiz {1}"
msgstr "Добили сте резултат од {0} на квизу {1}"

#: lms/lms/widgets/NoPreviewModal.html:12
msgid "You have opted to be notified for this course. You will receive an email when the course becomes available."
msgstr "Изабрали сте да будете обавештени о овој обуци. Добићете имејл када обука постане доступна."

#: frontend/src/components/CourseCardOverlay.vue:175
msgid "You need to login first to enroll for this course"
msgstr "Неопходно је да се прво пријавите да бисте се уписали на ову обуку"

#: frontend/src/components/Quiz.vue:7
msgid "You will have to complete the quiz to continue the video"
msgstr "Неопходно је да завршите квиз како бисте наставили видео-снимак"

#: frontend/src/components/Quiz.vue:30 lms/templates/quiz/quiz.html:11
#, python-format
msgid "You will have to get {0}% correct answers in order to pass the quiz."
msgstr "Да бисте прошли квиз, морате имати најмање {0}% тачних одговора."

#: lms/templates/emails/mentor_request_creation_email.html:4
msgid "You've applied to become a mentor for this course. Your request is currently under review."
msgstr "Пријавили сте се да будете ментор за ову обуку. Ваш захтев је тренутно на разматрању."

#: frontend/src/components/Assignment.vue:58
msgid "You've successfully submitted the assignment."
msgstr "Успешно сте поднели задатак."

#. Label of the youtube (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video URL"
msgstr "YouTube видео URL"

#. Description of the 'YouTube Video URL' (Data) field in DocType 'Course
#. Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video will appear at the top of the lesson."
msgstr "YouTube видео-снимак ће се приказати на врху лекције."

#: lms/www/new-sign-up.html:56
msgid "Your Account has been successfully created!"
msgstr "Ваш налог је успешно креиран!"

#: lms/lms/doctype/lms_batch/lms_batch.py:362
msgid "Your batch {0} is starting tomorrow"
msgstr "Ваша група {0} почиње сутра"

#: frontend/src/pages/ProfileEvaluator.vue:134
msgid "Your calendar is set."
msgstr "Ваш календар је подешен."

#: lms/lms/doctype/lms_live_class/lms_live_class.py:90
msgid "Your class on {0} is today"
msgstr "Ваше онлајн предавање {0} је данас"

#: frontend/src/components/Modals/EmailTemplateModal.vue:35
msgid "Your enrollment in {{ batch_name }} is confirmed"
msgstr "Ваш упис у {{ batch_name }} је потврђен"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:3
#: lms/templates/emails/certificate_request_notification.html:3
msgid "Your evaluation for the course {0} has been scheduled on {1} at {2} {3}."
msgstr "Ваше оцењивање за обуку {0} је заказано за {1} у {2} {3}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:125
msgid "Your evaluation slot has been booked"
msgstr "Ваш термин за оцењивање је резервисан"

#: lms/templates/emails/certificate_request_notification.html:5
msgid "Your evaluator is {0}"
msgstr "Особа која ће Вас оцењивати је {0}"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "Your request to join us as a mentor for the course"
msgstr "Ваш захтев за придруживањем као ментор за обуку"

#: lms/templates/quiz/quiz.js:140
msgid "Your score is"
msgstr "Ваш резултат је"

#: frontend/src/components/Quiz.vue:248
msgid "Your submission has been successfully saved. The instructor will review and grade it shortly, and you'll be notified of your final result."
msgstr "Ваш поднесак је успешно сачуван. Предавач ће је ускоро прегледати и оценити, а Ви ћете бити обавештени о коначном резултату."

#: frontend/src/pages/Lesson.vue:8
msgid "Zen Mode"
msgstr "Зен режим"

#. Label of the zoom_account (Link) field in DocType 'LMS Batch'
#. Label of the zoom_account (Link) field in DocType 'LMS Live Class'
#: frontend/src/pages/BatchForm.vue:164
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Zoom Account"
msgstr "Zoom налог"

#: frontend/src/components/Modals/ZoomAccountModal.vue:158
msgid "Zoom Account created successfully"
msgstr "Zoom налог је успешно креиран"

#: frontend/src/components/Modals/ZoomAccountModal.vue:195
msgid "Zoom Account updated successfully"
msgstr "Zoom налог је успешно ажуриран"

#. Name of a DocType
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Zoom Settings"
msgstr "Zoom подешавање"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activities"
msgstr "активности"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activity"
msgstr "активност"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicant"
msgstr "кандидат"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicants"
msgstr "кандидати"

#: frontend/src/components/VideoBlock.vue:15
msgid "at {0} minutes"
msgstr "на {0} минута"

#: lms/templates/emails/payment_reminder.html:4
msgid "but didn’t complete your payment"
msgstr "али нисте завршили плаћање"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "cancel your application"
msgstr "откажите своју пријаву"

#: frontend/src/pages/CertifiedParticipants.vue:79
msgid "certificate"
msgstr "сертификат"

#: frontend/src/pages/CertifiedParticipants.vue:78
msgid "certificates"
msgstr "сертификати"

#: frontend/src/pages/CertifiedParticipants.vue:18
msgid "certified members"
msgstr "сертификовани чланови"

#: frontend/src/pages/Lesson.vue:92 frontend/src/pages/Lesson.vue:249
msgid "completed"
msgstr "завршено"

#: lms/templates/quiz/quiz.js:137
msgid "correct answers"
msgstr "тачних одговора"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "has been"
msgstr "је"

#: frontend/src/components/StudentHeatmap.vue:8
msgid "in the last"
msgstr "у последњих"

#: lms/templates/signup-form.html:12
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/pages/Programs.vue:31
msgid "member"
msgstr "члан"

#: frontend/src/pages/Programs.vue:31
msgid "members"
msgstr "чланови"

#: frontend/src/components/Modals/LiveClassAttendance.vue:57
msgid "minutes"
msgstr "минуте"

#: lms/templates/quiz/quiz.html:106
msgid "of"
msgstr "од"

#: lms/templates/quiz/quiz.js:141
msgid "out of"
msgstr "од укупно"

#: frontend/src/pages/QuizForm.vue:391
msgid "question_detail"
msgstr "question_detail"

#: lms/templates/reviews.html:25
msgid "ratings"
msgstr "оцене"

#: frontend/src/components/Settings/Categories.vue:19
msgid "saving..."
msgstr "чување..."

#: lms/templates/reviews.html:43
msgid "stars"
msgstr "звезде"

#: frontend/src/components/BatchFeedback.vue:12
msgid "to view your feedback."
msgstr "да бисте погледали своје повратне информације."

#: frontend/src/components/StudentHeatmap.vue:10
msgid "weeks"
msgstr "недеље"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "you can"
msgstr "можете"

#: frontend/src/pages/Assignments.vue:29
msgid "{0} Assignments"
msgstr "{0} задатака"

#: frontend/src/pages/Jobs.vue:32
msgid "{0} Open Jobs"
msgstr "{0} отворених послова"

#: frontend/src/pages/Quizzes.vue:25
msgid "{0} Quizzes"
msgstr "{0} квизова"

#: lms/lms/api.py:866 lms/lms/api.py:874
msgid "{0} Settings not found"
msgstr "{0} подешавања нису пронађена"

#: lms/templates/emails/job_application.html:2
msgid "{0} has applied for the job position {1}"
msgstr "Пријава за радно место {1} је поднета од стране {0}"

#: lms/templates/emails/job_report.html:4
msgid "{0} has reported a job post for the following reason."
msgstr "{0} је пријавио оглас за посао из следећег разлога."

#: lms/templates/emails/assignment_submission.html:2
msgid "{0} has submitted the assignment {1}"
msgstr "Задатак {1} је поднет од стране {0}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:57
msgid "{0} is already a Student of {1} course through {2} batch"
msgstr "{0} је већ студент обуке {1} у оквиру групе {2}"

#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.py:16
msgid "{0} is already a mentor for course {1}"
msgstr "{0} је већ ментор за обуку {1}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:30
msgid "{0} is already a {1} of the course {2}"
msgstr "{0} је већ {1} за обуку {2}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:91
msgid "{0} is already certified for the batch {1}"
msgstr "Сертификат за групу {1} је већ додељен кориснику {0}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:72
msgid "{0} is already certified for the course {1}"
msgstr "{0} већ поседује сертификат за обуку {1}"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:5
msgid "{0} is your evaluator"
msgstr "{0} је Ваша особа за оцењивање"

#: lms/lms/utils.py:687
msgid "{0} mentioned you in a comment"
msgstr "поменути сте у коментару од стране {0}"

#: lms/templates/emails/mention_template.html:2
msgid "{0} mentioned you in a comment in your batch."
msgstr "поменути сте у коментару у својој групи од стране {0}"

#: lms/lms/utils.py:640 lms/lms/utils.py:646
msgid "{0} mentioned you in a comment in {1}"
msgstr "поменути сте у коментару у оквиру {1} од стране {0}"

#: lms/lms/utils.py:462
msgid "{0}k"
msgstr "{0}к"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Active"
msgstr "{} активних"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Completed"
msgstr "{} завршено"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Enrolled"
msgstr "{} уписан"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Granted"
msgstr "{} одобрено"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Passed"
msgstr "{} положено"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Published"
msgstr "{} објављено"

