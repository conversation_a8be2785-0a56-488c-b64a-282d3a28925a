/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccessButton: typeof import('./src/components/ShareDialog/AccessButton.vue')['default']
    ActivityTree: typeof import('./src/components/ActivityTree.vue')['default']
    ActivityTreeItem: typeof import('./src/components/ActivityTreeItem.vue')['default']
    ActivityTreeShare: typeof import('./src/components/ActivityTreeShare.vue')['default']
    AddUser: typeof import('./src/components/EspressoIcons/AddUser.vue')['default']
    AlignCenter: typeof import('./src/components/DocEditor/icons/AlignCenter.vue')['default']
    AlignItemCenter: typeof import('./src/components/DocEditor/icons/align-item-center.vue')['default']
    AlignItemLeft: typeof import('./src/components/DocEditor/icons/align-item-left.vue')['default']
    AlignItemRight: typeof import('./src/components/DocEditor/icons/align-item-right.vue')['default']
    AlignJustify: typeof import('./src/components/DocEditor/icons/AlignJustify.vue')['default']
    AlignLeft: typeof import('./src/components/DocEditor/icons/AlignLeft.vue')['default']
    AlignRight: typeof import('./src/components/DocEditor/icons/AlignRight.vue')['default']
    AnnotationList: typeof import('./src/components/DocEditor/components/AnnotationList.vue')['default']
    Apps: typeof import('./src/components/Apps.vue')['default']
    AppsIcon: typeof import('./src/components/AppsIcon.vue')['default']
    AppSwitcher: typeof import('./src/components/AppSwitcher.vue')['default']
    Archive: typeof import('./src/components/MimeIcons/Archive.vue')['default']
    ArrowDownToLineOff: typeof import('./src/components/arrow-down-to-line-off.vue')['default']
    Audio: typeof import('./src/components/MimeIcons/Audio.vue')['default']
    AudioPreview: typeof import('./src/components/FileTypePreview/AudioPreview.vue')['default']
    BlockQuote: typeof import('./src/components/DocEditor/icons/BlockQuote.vue')['default']
    Bold: typeof import('./src/components/DocEditor/icons/Bold.vue')['default']
    BottomBar: typeof import('./src/components/BottomBar.vue')['default']
    Check: typeof import('./src/components/DocEditor/icons/Check.vue')['default']
    ChevronDown: typeof import('./src/components/EspressoIcons/ChevronDown.vue')['default']
    Clock: typeof import('./src/components/EspressoIcons/Clock.vue')['default']
    Cloud: typeof import('./src/components/EspressoIcons/Cloud.vue')['default']
    Code: typeof import('./src/components/DocEditor/icons/Code.vue')['default']
    Codeblock: typeof import('./src/components/DocEditor/icons/Codeblock.vue')['default']
    ColorInput: typeof import('./src/components/DocEditor/components/ColorInput.vue')['default']
    ColorPicker: typeof import('./src/components/DocEditor/components/ColorPicker.vue')['default']
    ColorPopover: typeof import('./src/components/ColorPopover.vue')['default']
    ColourPicker: typeof import('./src/components/EspressoIcons/Colour-picker.vue')['default']
    Comment: typeof import('./src/components/EspressoIcons/Comment.vue')['default']
    ContextMenu: typeof import('./src/components/ContextMenu.vue')['default']
    CTADeleteDialog: typeof import('./src/components/CTADeleteDialog.vue')['default']
    CustomListRow: typeof import('./src/components/CustomListRow.vue')['default']
    CustomListRowItem: typeof import('./src/components/CustomListRowItem.vue')['default']
    Delete: typeof import('./src/components/DocEditor/icons/delete.vue')['default']
    DeleteDialog: typeof import('./src/components/DeleteDialog.vue')['default']
    Details: typeof import('./src/components/DocEditor/icons/Details.vue')['default']
    Dialogs: typeof import('./src/components/Dialogs.vue')['default']
    Diamond: typeof import('./src/components/EspressoIcons/Diamond.vue')['default']
    DocMenuAndInfoBar: typeof import('./src/components/DocEditor/components/DocMenuAndInfoBar.vue')['default']
    DocPreview: typeof import('./src/components/FileTypePreview/DocPreview.vue')['default']
    Docs: typeof import('./src/components/EspressoIcons/Docs.vue')['default']
    Document: typeof import('./src/components/MimeIcons/Document.vue')['default']
    DownArrow: typeof import('./src/components/EspressoIcons/DownArrow.vue')['default']
    Download: typeof import('./src/components/EspressoIcons/Download.vue')['default']
    DriveToolBar: typeof import('./src/components/DriveToolBar.vue')['default']
    Edit: typeof import('./src/components/EspressoIcons/Edit.vue')['default']
    EditTagDialog: typeof import('./src/components/Settings/EditTagDialog.vue')['default']
    EntityToolbar: typeof import('./src/components/EntityToolbar.vue')['default']
    File: typeof import('./src/components/EspressoIcons/File.vue')['default']
    FilePicker: typeof import('./src/components/FilePicker.vue')['default']
    FilePreview: typeof import('./src/components/FilePreview.vue')['default']
    FileRender: typeof import('./src/components/FileRender.vue')['default']
    FileUpload: typeof import('./src/components/EspressoIcons/File-upload.vue')['default']
    FileUploader: typeof import('./src/components/FileUploader.vue')['default']
    Filter: typeof import('./src/components/EspressoIcons/Filter.vue')['default']
    FloatItemLeft: typeof import('./src/components/DocEditor/icons/float-item-left.vue')['default']
    FloatItemRight: typeof import('./src/components/DocEditor/icons/float-item-right.vue')['default']
    FloatLeft: typeof import('./src/components/DocEditor/icons/float-left.vue')['default']
    FloatRight: typeof import('./src/components/DocEditor/icons/float-right.vue')['default']
    Folder: typeof import('./src/components/MimeIcons/Folder.vue')['default']
    FolderContentsError: typeof import('./src/components/FolderContentsError.vue')['default']
    FolderUpload: typeof import('./src/components/EspressoIcons/Folder-upload.vue')['default']
    FontColor: typeof import('./src/components/DocEditor/components/FontColor.vue')['default']
    FrappeDriveLogo: typeof import('./src/components/FrappeDriveLogo.vue')['default']
    FrappeFileLine: typeof import('./src/components/FrappeFileLine.vue')['default']
    FrappeFolder: typeof import('./src/components/FrappeFolder.vue')['default']
    FrappeFolderLine: typeof import('./src/components/FrappeFolderLine.vue')['default']
    FrappeLogo: typeof import('./src/components/FrappeLogo.vue')['default']
    GeneralAccess: typeof import('./src/components/GeneralAccess.vue')['default']
    GeneralDialog: typeof import('./src/components/GeneralDialog.vue')['default']
    GenericPage: typeof import('./src/components/GenericPage.vue')['default']
    Globe: typeof import('./src/components/EspressoIcons/Globe.vue')['default']
    GridItem: typeof import('./src/components/GridItem.vue')['default']
    GridView: typeof import('./src/components/GridView.vue')['default']
    Groups: typeof import('./src/components/EspressoIcons/Groups.vue')['default']
    Home: typeof import('./src/components/EspressoIcons/Home.vue')['default']
    Image: typeof import('./src/components/DocEditor/icons/Image.vue')['default']
    ImagePreview: typeof import('./src/components/FileTypePreview/ImagePreview.vue')['default']
    Indent: typeof import('./src/components/DocEditor/icons/Indent.vue')['default']
    Info: typeof import('./src/components/EspressoIcons/Info.vue')['default']
    InfoPopup: typeof import('./src/components/InfoPopup.vue')['default']
    InfoSidebar: typeof import('./src/components/InfoSidebar.vue')['default']
    InsertImage: typeof import('./src/components/DocEditor/components/InsertImage.vue')['default']
    InsertLink: typeof import('./src/components/DocEditor/components/InsertLink.vue')['default']
    InsertVideo: typeof import('./src/components/DocEditor/components/InsertVideo.vue')['default']
    Italic: typeof import('./src/components/DocEditor/icons/Italic.vue')['default']
    LineHeight: typeof import('./src/components/DocEditor/icons/line-height.vue')['default']
    Link: typeof import('./src/components/EspressoIcons/Link.vue')['default']
    List: typeof import('./src/components/DocEditor/icons/List.vue')['default']
    ListView: typeof import('./src/components/ListView.vue')['default']
    Loader: typeof import('./src/components/Loader.vue')['default']
    Lock: typeof import('./src/components/EspressoIcons/Lock.vue')['default']
    LucideAlert: typeof import('~icons/lucide/alert')['default']
    LucideAlertCircle: typeof import('~icons/lucide/alert-circle')['default']
    LucideApp: typeof import('~icons/lucide/app')['default']
    LucideArrowBigLeft: typeof import('~icons/lucide/arrow-big-left')['default']
    LucideArrowDownAz: typeof import('~icons/lucide/arrow-down-az')['default']
    LucideArrowRight: typeof import('~icons/lucide/arrow-right')['default']
    LucideArrowUp: typeof import('~icons/lucide/arrow-up')['default']
    LucideArrowUpZa: typeof import('~icons/lucide/arrow-up-za')['default']
    LucideAxe: typeof import('~icons/lucide/axe')['default']
    LucideBookOpen: typeof import('~icons/lucide/book-open')['default']
    LucideBuilding: typeof import('~icons/lucide/building')['default']
    LucideCalendar: typeof import('~icons/lucide/calendar')['default']
    Lucideche: typeof import('~icons/lucide/che')['default']
    LucideChe: typeof import('~icons/lucide/che')['default']
    LucideChec: typeof import('~icons/lucide/chec')['default']
    LucideCheck: typeof import('~icons/lucide/check')['default']
    LucideCheckCircle: typeof import('~icons/lucide/check-circle')['default']
    LucideCheckCircle2: typeof import('~icons/lucide/check-circle2')['default']
    LucideChevronDown: typeof import('~icons/lucide/chevron-down')['default']
    LucideChevronRight: typeof import('~icons/lucide/chevron-right')['default']
    LucideChevronUp: typeof import('~icons/lucide/chevron-up')['default']
    LucideClock: typeof import('~icons/lucide/clock')['default']
    LucideCloud: typeof import('~icons/lucide/cloud')['default']
    LucideCommand: typeof import('~icons/lucide/command')['default']
    LucideDi: typeof import('~icons/lucide/di')['default']
    LucideEarth: typeof import('~icons/lucide/earth')['default']
    LucideEll: typeof import('~icons/lucide/ell')['default']
    LucideEllip: typeof import('~icons/lucide/ellip')['default']
    LucideEllipsis: typeof import('~icons/lucide/ellipsis')['default']
    LucideEllipsisHorizontal: typeof import('~icons/lucide/ellipsis-horizontal')['default']
    LucideEllipsisVertical: typeof import('~icons/lucide/ellipsis-vertical')['default']
    LucideError: typeof import('~icons/lucide/error')['default']
    LucideEyeOff: typeof import('~icons/lucide/eye-off')['default']
    LucideFile: typeof import('~icons/lucide/file')['default']
    LucideFilePl: typeof import('~icons/lucide/file-pl')['default']
    LucideFilePlus2: typeof import('~icons/lucide/file-plus2')['default']
    LucideFilter: typeof import('~icons/lucide/filter')['default']
    LucideFol: typeof import('~icons/lucide/fol')['default']
    LucideFolder: typeof import('~icons/lucide/folder')['default']
    LucideFolderClosed: typeof import('~icons/lucide/folder-closed')['default']
    LucideFolderOpenDot: typeof import('~icons/lucide/folder-open-dot')['default']
    LucideFolderPlus: typeof import('~icons/lucide/folder-plus')['default']
    LucideGlob2: typeof import('~icons/lucide/glob2')['default']
    LucideGlobe2: typeof import('~icons/lucide/globe2')['default']
    LucideGrid: typeof import('~icons/lucide/grid')['default']
    LucideH: typeof import('~icons/lucide/h')['default']
    LucideHome: typeof import('~icons/lucide/home')['default']
    LucideImage: typeof import('~icons/lucide/image')['default']
    LucideInfo: typeof import('~icons/lucide/info')['default']
    LucideItalic: typeof import('~icons/lucide/italic')['default']
    LucideLayout: typeof import('~icons/lucide/layout')['default']
    LucideLayoutGrid: typeof import('~icons/lucide/layout-grid')['default']
    LucideLayoutList: typeof import('~icons/lucide/layout-list')['default']
    LucideLink: typeof import('~icons/lucide/link')['default']
    LucideLink2: typeof import('~icons/lucide/link2')['default']
    LucideLock: typeof import('~icons/lucide/lock')['default']
    LucideLogOut: typeof import('~icons/lucide/log-out')['default']
    LucideMinus: typeof import('~icons/lucide/minus')['default']
    LucideMoreHorizontal: typeof import('~icons/lucide/more-horizontal')['default']
    LucideMoveU: typeof import('~icons/lucide/move-u')['default']
    LucideOpe: typeof import('~icons/lucide/ope')['default']
    LucideOpen: typeof import('~icons/lucide/open')['default']
    LucidePlus: typeof import('~icons/lucide/plus')['default']
    LucideSearch: typeof import('~icons/lucide/search')['default']
    LucideSearch2: typeof import('~icons/lucide/search2')['default']
    LucideSettings: typeof import('~icons/lucide/settings')['default']
    LucideStar: typeof import('~icons/lucide/star')['default']
    LucideTag: typeof import('~icons/lucide/tag')['default']
    LucideTe: typeof import('~icons/lucide/te')['default']
    LucideTrash: typeof import('~icons/lucide/trash')['default']
    Lucideuse: typeof import('~icons/lucide/use')['default']
    LucideUser: typeof import('~icons/lucide/user')['default']
    LucideUser2: typeof import('~icons/lucide/user2')['default']
    LucideVerti: typeof import('~icons/lucide/verti')['default']
    LucideX: typeof import('~icons/lucide/x')['default']
    LucideXLucideFolderOpenDot: typeof import('~icons/lucide/x-lucide-folder-open-dot')['default']
    Mention: typeof import('./src/components/DocEditor/icons/Mention.vue')['default']
    MentionList: typeof import('./src/components/DocEditor/components/MentionList.vue')['default']
    Menu: typeof import('./src/components/DocEditor/components/Menu.vue')['default']
    MenuBar: typeof import('./src/components/DocEditor/components/MenuBar.vue')['default']
    MessageCircleOff: typeof import('./src/components/message-circle-off.vue')['default']
    MobileSidebar: typeof import('./src/components/MobileSidebar.vue')['default']
    Move: typeof import('./src/components/EspressoIcons/Move.vue')['default']
    Move2: typeof import('./src/components/EspressoIcons/Move-2.vue')['default']
    MoveDialog: typeof import('./src/components/MoveDialog.vue')['default']
    MSOfficePreview: typeof import('./src/components/FileTypePreview/MSOfficePreview.vue')['default']
    MyDrive: typeof import('./src/components/EspressoIcons/MyDrive.vue')['default']
    Navbar: typeof import('./src/components/Navbar.vue')['default']
    NewAnnotation: typeof import('./src/components/DocEditor/components/NewAnnotation.vue')['default']
    NewComment: typeof import('./src/components/DocEditor/components/NewComment.vue')['default']
    NewFile: typeof import('./src/components/EspressoIcons/NewFile.vue')['default']
    NewFolder: typeof import('./src/components/EspressoIcons/NewFolder.vue')['default']
    NewFolderDialog: typeof import('./src/components/NewFolderDialog.vue')['default']
    NewLink: typeof import('./src/components/DocEditor/icons/NewLink.vue')['default']
    NewLinkDialog: typeof import('./src/components/NewLinkDialog.vue')['default']
    NewManualSnapshotDialog: typeof import('./src/components/DocEditor/components/NewManualSnapshotDialog.vue')['default']
    NewTagDialog: typeof import('./src/components/Settings/NewTagDialog.vue')['default']
    NoFilesSection: typeof import('./src/components/NoFilesSection.vue')['default']
    Open: typeof import('./src/components/EspressoIcons/Open.vue')['default']
    OrderList: typeof import('./src/components/DocEditor/icons/OrderList.vue')['default']
    Organization: typeof import('./src/components/EspressoIcons/Organization.vue')['default']
    Outdent: typeof import('./src/components/DocEditor/icons/Outdent.vue')['default']
    OuterComment: typeof import('./src/components/DocEditor/components/OuterComment.vue')['default']
    PageBreak: typeof import('./src/components/DocEditor/icons/PageBreak.vue')['default']
    Palette: typeof import('./src/components/EspressoIcons/Palette.vue')['default']
    PDF: typeof import('./src/components/MimeIcons/PDF.vue')['default']
    PDFPreview: typeof import('./src/components/FileTypePreview/PDFPreview.vue')['default']
    Presentation: typeof import('./src/components/MimeIcons/Presentation.vue')['default']
    Preview: typeof import('./src/components/EspressoIcons/Preview.vue')['default']
    PreviewEditor: typeof import('./src/components/DocEditor/PreviewEditor.vue')['default']
    PrimaryDropdown: typeof import('./src/components/PrimaryDropdown.vue')['default']
    Printer: typeof import('./src/components/EspressoIcons/Printer.vue')['default']
    ProfileSettings: typeof import('./src/components/Settings/ProfileSettings.vue')['default']
    ProgressRing: typeof import('./src/components/ProgressRing.vue')['default']
    Quote: typeof import('./src/components/DocEditor/icons/quote.vue')['default']
    Recent: typeof import('./src/components/EspressoIcons/Recent.vue')['default']
    Rename: typeof import('./src/components/EspressoIcons/Rename.vue')['default']
    RenameDialog: typeof import('./src/components/RenameDialog.vue')['default']
    ResizableMediaNodeView: typeof import('./src/components/DocEditor/components/ResizableMediaNodeView.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SaasLoginBox: typeof import('./src/components/SaasLoginBox.vue')['default']
    Search: typeof import('./src/components/EspressoIcons/Search.vue')['default']
    SearchPopup: typeof import('./src/components/SearchPopup.vue')['default']
    SettingsDialog: typeof import('./src/components/Settings/SettingsDialog.vue')['default']
    Share: typeof import('./src/components/EspressoIcons/Share.vue')['default']
    ShareDialog: typeof import('./src/components/ShareDialog/ShareDialog.vue')['default']
    ShareNew: typeof import('./src/components/EspressoIcons/ShareNew.vue')['default']
    SheetPreview: typeof import('./src/components/FileTypePreview/SheetPreview.vue')['default']
    ShortcutsDialog: typeof import('./src/components/ShortcutsDialog.vue')['default']
    Sidebar: typeof import('./src/components/Sidebar.vue')['default']
    SidebarItem: typeof import('./src/components/SidebarItem.vue')['default']
    SlidePreview: typeof import('./src/components/FileTypePreview/SlidePreview.vue')['default']
    SnapshotPreviewDialog: typeof import('./src/components/DocEditor/components/SnapshotPreviewDialog.vue')['default']
    Sort: typeof import('./src/components/EspressoIcons/Sort.vue')['default']
    Spreadsheet: typeof import('./src/components/MimeIcons/Spreadsheet.vue')['default']
    Star: typeof import('./src/components/EspressoIcons/Star.vue')['default']
    StorageBar: typeof import('./src/components/StorageBar.vue')['default']
    StorageSettings: typeof import('./src/components/Settings/StorageSettings.vue')['default']
    StrikeThrough: typeof import('./src/components/DocEditor/icons/StrikeThrough.vue')['default']
    Style: typeof import('./src/components/DocEditor/icons/Style.vue')['default']
    SuggestionList: typeof import('./src/components/DocEditor/components/suggestionList.vue')['default']
    TableBubbleMenu: typeof import('./src/components/DocEditor/components/TableBubbleMenu.vue')['default']
    TableCellMenu: typeof import('./src/components/DocEditor/components/TableCellMenu.vue')['default']
    TableColumnMenu: typeof import('./src/components/DocEditor/components/TableColumnMenu.vue')['default']
    TableRowMenu: typeof import('./src/components/DocEditor/components/TableRowMenu.vue')['default']
    Tag: typeof import('./src/components/Tag.vue')['default']
    TagColorInput: typeof import('./src/components/TagColorInput.vue')['default']
    TagInput: typeof import('./src/components/TagInput.vue')['default']
    TagSettings: typeof import('./src/components/Settings/TagSettings.vue')['default']
    TeamSwitcher: typeof import('./src/components/TeamSwitcher.vue')['default']
    TextEditor: typeof import('./src/components/DocEditor/TextEditor.vue')['default']
    TextPreview: typeof import('./src/components/FileTypePreview/TextPreview.vue')['default']
    TiptapInput: typeof import('./src/components/TiptapInput.vue')['default']
    Toast: typeof import('./src/components/Toast.vue')['default']
    ToggleHeaderCell: typeof import('./src/components/DocEditor/icons/ToggleHeaderCell.vue')['default']
    Trash: typeof import('./src/components/EspressoIcons/Trash.vue')['default']
    Underline: typeof import('./src/components/DocEditor/icons/Underline.vue')['default']
    Unknown: typeof import('./src/components/MimeIcons/Unknown.vue')['default']
    UploadTracker: typeof import('./src/components/UploadTracker.vue')['default']
    User: typeof import('./src/components/EspressoIcons/User.vue')['default']
    UserAutoComplete: typeof import('./src/components/ShareDialog/UserAutoComplete.vue')['default']
    UserDropdown: typeof import('./src/components/UserDropdown.vue')['default']
    UserListSettings: typeof import('./src/components/Settings/UserListSettings.vue')['default']
    Users: typeof import('./src/components/EspressoIcons/Users.vue')['default']
    UsersBar: typeof import('./src/components/UsersBar.vue')['default']
    UserSearch: typeof import('./src/components/ShareDialog/UserSearch.vue')['default']
    Video: typeof import('./src/components/DocEditor/icons/Video.vue')['default']
    VideoPlayer: typeof import('./src/components/VideoPlayer.vue')['default']
    VideoPreview: typeof import('./src/components/FileTypePreview/VideoPreview.vue')['default']
    View: typeof import('./src/components/EspressoIcons/View.vue')['default']
    ViewGrid: typeof import('./src/components/EspressoIcons/ViewGrid.vue')['default']
    ViewList: typeof import('./src/components/EspressoIcons/ViewList.vue')['default']
  }
}
