# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

# import frappe
from frappe.model.document import Document
import frappe
from frappe import _

class ClearingCharges(Document):
    def before_save(self):
        self.fetch_total_charges()

    def fetch_total_charges(self):
        # Initialize total variables
        tra_total = 0
        port_total = 0
        shipment_total = 0
        physical_total = 0
        
        # Fetch charges from the child table
        for charge in self.charges:
            if charge.charge_type == "TRA Clearance":
                tra_total += float(charge.amount)
            elif charge.charge_type == "Port Clearance":
                port_total += float(charge.amount)
            elif charge.charge_type == "Shipping Line Clearance":
                shipment_total += float(charge.amount)
            elif charge.charge_type == "Physical Verification":
                physical_total += float(charge.amount)
        
        # Update the doc with fetched totals
        self.tra_clearance_total = tra_total
        self.port_clearance_total = port_total
        self.shipment_clearance_total = shipment_total
        self.physical_clearance_total = physical_total
        
        # Calculate total sum
        self.total_charges_sum = tra_total + port_total + shipment_total + physical_total

@frappe.whitelist()
def create_service_invoice_from_service_charges(clearing_charges_name):
    """Create a Sales Invoice using the sum of the service_charges child table"""
    clearing_charges = frappe.get_doc("Clearing Charges", clearing_charges_name)
    if not hasattr(clearing_charges, 'service_charges') or not clearing_charges.service_charges:
        frappe.throw(_("No service charges found. Please add at least one service charge."))
    total = 0
    for row in clearing_charges.service_charges:
        total += float(row.amount or 0)
    if total == 0:
        frappe.throw(_("Total service charges amount is zero."))
    # Ensure the item exists for sales
    if not frappe.db.exists("Item", "Clearing Service Charge"):
        item = frappe.new_doc("Item")
        item.item_code = "Clearing Service Charge"
        item.item_name = "Clearing Service Charge"
        item.item_group = "Services"
        item.is_stock_item = 0
        item.is_sales_item = 1
        item.is_purchase_item = 0
        item.save()
    service_invoice = frappe.new_doc("Sales Invoice")
    service_invoice.customer = clearing_charges.consigee
    service_invoice.posting_date = frappe.utils.nowdate()
    service_invoice.is_pos = 0
    service_invoice.append("items", {
        "item_code": "Clearing Service Charge",
        "description": "Clearing Service Charges",
        "qty": 1,
        "rate": total,
        "amount": total
    })
    service_invoice.set_missing_values()
    service_invoice.save()
    clearing_charges.db_set('invoice_number', service_invoice.name)
    frappe.db.commit()
    return service_invoice

@frappe.whitelist()
def create_debit_note(clearing_charges_name):
    """Create a Debit Note (Sales Invoice) for clearing charges"""
    clearing_charges = frappe.get_doc("Clearing Charges", clearing_charges_name)

    # Validate that there are charges to create debit note for
    if not hasattr(clearing_charges, 'charges') or not clearing_charges.charges:
        frappe.throw(_("No charges found. Please add at least one charge to create a debit note."))

    # Calculate total charges
    total_charges = clearing_charges.total_charges_sum or 0
    if total_charges == 0:
        frappe.throw(_("Total charges amount is zero. Cannot create debit note."))

    # Create the debit note (Sales Invoice with is_debit_note flag)
    debit_note = frappe.new_doc("Sales Invoice")
    debit_note.customer = clearing_charges.consigee
    debit_note.posting_date = frappe.utils.nowdate()
    debit_note.is_pos = 0
    debit_note.is_debit_note = 1  # This makes it a debit note

    # Add items for each charge type with amounts, using charge_type as item_code
    for charge in clearing_charges.charges:
        if charge.amount and float(charge.amount) > 0:
            item_code = charge.charge_type
            # Ensure the item exists for this charge type
            if not frappe.db.exists("Item", item_code):
                item = frappe.new_doc("Item")
                item.item_code = item_code
                item.item_name = item_code
                item.item_group = "Services"
                item.is_stock_item = 0
                item.is_sales_item = 1
                item.is_purchase_item = 0
                item.save()
            debit_note.append("items", {
                "item_code": item_code,
                "description": f"{charge.charge_type} - {charge.amount}",
                "qty": 1,
                "rate": float(charge.amount),
                "amount": float(charge.amount)
            })

    # Set missing values and save
    debit_note.set_missing_values()
    debit_note.save()
    debit_note.submit()

    # Update clearing charges with debit note reference
    clearing_charges.db_set('debit_note_number', debit_note.name)
    frappe.db.commit()

    return debit_note
