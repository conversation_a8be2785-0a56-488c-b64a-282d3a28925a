{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2022-06-29 17:17:49.625315", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["referral_type", "column_break_nbbzj", "encounter", "column_break_bvbfe", "company", "column_break_okjqc", "posting_date", "update_referral", "section_break_urkrb", "appointment", "patient", "first_name", "last_name", "patient_name", "cardno_column_break", "card_no", "national_id", "authorization_no", "column_break_h5k6s", "dob", "attendance_date", "patient_type_code", "column_break_qasna", "mobile_no", "gender", "insurance_company", "facility_section_break", "source_facility", "source_facility_code", "facility_column_break", "referred_to_facility", "referred_to_facility_code", "column_break_cmshp", "referral_status", "referral_date", "reason_section", "practitioner", "practitioner_no", "column_break_5moo6", "reason_for_referral", "section_break_xwfkf", "select_diagnosis", "diagnosis", "section_break_kravf", "select_service", "services", "referral_section_break", "referral_no", "column_break_mqi0e", "referral_id", "naming_series", "column_break_ddz0d", "referral_submitted_by", "referral_updated_by", "amended_from"], "fields": [{"fetch_from": "encounter.appointment", "fetch_if_empty": 1, "fieldname": "appointment", "fieldtype": "Link", "in_standard_filter": 1, "label": "Patient Appointment", "options": "Patient Appointment", "read_only_depends_on": "eval: doc.referral_type != \"AcknowledgeServiceReferral\"", "reqd": 1, "search_index": 1}, {"fetch_from": "appointment.patient_name", "fieldname": "patient_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Patient Name", "read_only": 1, "search_index": 1}, {"fetch_from": "appointment.patient_sex", "fieldname": "gender", "fieldtype": "Data", "label": "Gender", "read_only": 1}, {"fieldname": "referral_status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Referal Status", "options": "Pending\nSuccess", "read_only": 1, "search_index": 1}, {"fetch_from": "appointment.coverage_plan_card_number", "fieldname": "card_no", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "CardNo", "read_only": 1, "search_index": 1}, {"fieldname": "posting_date", "fieldtype": "Datetime", "in_standard_filter": 1, "label": "Posting Date", "read_only": 1, "search_index": 1}, {"fieldname": "facility_section_break", "fieldtype": "Section Break", "label": "Health Facility info"}, {"fieldname": "source_facility", "fieldtype": "Link", "in_standard_filter": 1, "label": "Source Facility", "options": "Healthcare Facility", "read_only": 1, "search_index": 1}, {"fetch_from": "source_facility.facility_code", "fieldname": "source_facility_code", "fieldtype": "Data", "label": "Source Facility Code", "read_only": 1, "search_index": 1}, {"fieldname": "facility_column_break", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fetch_from": "encounter.practitioner", "fieldname": "practitioner", "fieldtype": "Link", "in_standard_filter": 1, "label": "Healthcare Practitioner", "mandatory_depends_on": "eval: doc.referral_type != \"AcknowledgeServiceReferral\"", "options": "Healthcare Practitioner", "read_only_depends_on": "eval: doc.docstatus != 1", "search_index": 1}, {"depends_on": "eval: !doc.__islocal", "fieldname": "reason_section", "fieldtype": "Section Break", "label": "Referring Reason"}, {"allow_on_submit": 1, "fieldname": "reason_for_referral", "fieldtype": "Text Editor", "label": "Reason For Referral"}, {"fieldname": "referral_section_break", "fieldtype": "Section Break", "label": "Referral Info"}, {"bold": 1, "fieldname": "referral_no", "fieldtype": "Data", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "Referral No", "read_only": 1, "search_index": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "HLC-.R-.YYYY.-", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Healthcare Referral", "print_hide": 1, "read_only": 1}, {"fetch_from": "appointment.national_id", "fieldname": "national_id", "fieldtype": "Data", "label": "National ID", "read_only": 1, "search_index": 1}, {"fetch_from": "appointment.authorization_number", "fieldname": "authorization_no", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "AuthorizationNo", "read_only": 1, "search_index": 1}, {"fieldname": "referral_date", "fieldtype": "Datetime", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "Referral Date", "reqd": 1, "search_index": 1}, {"fieldname": "column_break_cmshp", "fieldtype": "Column Break"}, {"fieldname": "column_break_h5k6s", "fieldtype": "Column Break"}, {"fetch_from": "appointment.patient", "fieldname": "patient", "fieldtype": "Link", "in_standard_filter": 1, "label": "Patient", "options": "Patient", "read_only": 1, "search_index": 1}, {"fetch_from": "patient.first_name", "fieldname": "first_name", "fieldtype": "Data", "hidden": 1, "label": "First Name", "read_only": 1, "search_index": 1}, {"fetch_from": "patient.last_name", "fieldname": "last_name", "fieldtype": "Data", "hidden": 1, "label": "Last Name", "read_only": 1, "search_index": 1}, {"fetch_from": "patient.dob", "fieldname": "dob", "fieldtype": "Date", "label": "Date of Birth", "read_only": 1, "search_index": 1}, {"fetch_from": "patient.mobile", "fieldname": "mobile_no", "fieldtype": "Data", "label": "Mobile No", "read_only": 1, "search_index": 1}, {"fetch_from": "appointment.appointment_date", "fieldname": "attendance_date", "fieldtype": "Date", "label": "Attendance Date", "read_only": 1, "search_index": 1}, {"fieldname": "patient_type_code", "fieldtype": "Data", "label": "Patient Type Code", "read_only": 1, "search_index": 1}, {"allow_on_submit": 1, "fetch_from": "practitioner.tz_mct_code", "fieldname": "practitioner_no", "fieldtype": "Data", "label": "Practitioner No", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_5moo6", "fieldtype": "Column Break"}, {"depends_on": "eval: doc.referral_type != \"AcknowledgeServiceReferral\"", "fieldname": "encounter", "fieldtype": "Link", "in_standard_filter": 1, "label": "Patient Encounter", "mandatory_depends_on": "eval: doc.referral_type != \"AcknowledgeServiceReferral\"", "options": "Patient Encounter", "search_index": 1}, {"fieldname": "section_break_urkrb", "fieldtype": "Section Break"}, {"fieldname": "column_break_qasna", "fieldtype": "Column Break"}, {"fetch_from": "encounter.insurance_company", "fieldname": "insurance_company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Insurance Company", "options": "Healthcare Insurance Company", "read_only": 1, "search_index": 1}, {"fieldname": "section_break_xwfkf", "fieldtype": "Section Break"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "diagnosis", "fieldtype": "Table", "label": "Diagnosis", "options": "Healthcare Referral diagnosis"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "select_diagnosis", "fieldtype": "<PERSON><PERSON>", "label": "Select Diagnosis"}, {"fieldname": "section_break_kravf", "fieldtype": "Section Break"}, {"depends_on": "eval: !doc.__islocal & doc.referral_type == 'Form 2C/2E'", "fieldname": "select_service", "fieldtype": "<PERSON><PERSON>", "label": "Select Service"}, {"depends_on": "eval: !doc.__islocal & doc.referral_type == 'Form 2C/2E'", "fieldname": "services", "fieldtype": "Table", "label": "Services", "mandatory_depends_on": "eval: !doc.__islocal & doc.referral_type == 'Form 2C/2E'", "options": "Healthcare Referral Item"}, {"fieldname": "column_break_nbbzj", "fieldtype": "Column Break"}, {"fieldname": "referral_type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Referral Type", "options": "\nForm 2C/2E\nTreatment\nAcknowledgeServiceReferral", "reqd": 1, "search_index": 1}, {"depends_on": "eval: doc.docstatus == 1", "fieldname": "column_break_okjqc", "fieldtype": "Column Break"}, {"depends_on": "eval: doc.docstatus == 1", "fieldname": "update_referral", "fieldtype": "<PERSON><PERSON>", "label": "Update Referral"}, {"fieldname": "column_break_ddz0d", "fieldtype": "Column Break"}, {"fieldname": "referral_submitted_by", "fieldtype": "Data", "label": "Referral Submitted By", "read_only": 1, "search_index": 1}, {"allow_on_submit": 1, "fieldname": "referral_updated_by", "fieldtype": "Data", "label": "Referral Updated By", "read_only": 1, "search_index": 1}, {"fieldname": "cardno_column_break", "fieldtype": "Column Break"}, {"fieldname": "referral_id", "fieldtype": "Data", "label": "Referral ID", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_mqi0e", "fieldtype": "Column Break"}, {"fieldname": "column_break_bvbfe", "fieldtype": "Column Break"}, {"fetch_from": "appointment.company", "fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "HMS TZ Setting", "read_only": 1, "search_index": 1}, {"allow_on_submit": 1, "fieldname": "referred_to_facility", "fieldtype": "Link", "label": "Referred To Facility", "options": "Healthcare Facility", "reqd": 1, "search_index": 1}, {"allow_on_submit": 1, "fetch_from": "referred_to_facility.facility_code", "fieldname": "referred_to_facility_code", "fieldtype": "Data", "label": "Referred To Facility Code", "read_only": 1, "search_index": 1}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-06-19 10:01:44.862654", "modified_by": "Administrator", "module": "Hms Tz", "name": "Healthcare Referral", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}