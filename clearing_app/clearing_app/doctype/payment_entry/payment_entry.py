import frappe
from frappe.model.document import Document

class PaymentEntry(Document):
    # ...existing code...

    def on_submit(self):
        # When "Paid by Clearing" is checked, create a clearing Journal Entry
        if self.paid_by_clearing:
            self.create_clearing_journal_entry()
        # When "Charges Paid" is checked, create both Debit Note and Credit Note Journal Entries
        if getattr(self, "charges_paid", None):
            self.create_debit_note()
            self.create_credit_note()
        # ...existing code...

    def create_clearing_journal_entry(self):
        # Create a Journal Entry for clearing
        je = frappe.new_doc("Journal Entry")
        je.voucher_type = "Journal Entry"
        je.posting_date = self.posting_date
        je.company = self.company
        je.remark = f"Clearing for Payment Entry {self.name}"
        # Add debit and credit lines
        je.append("accounts", {
            "account": self.paid_from,
            "debit_in_account_currency": self.paid_amount,
            "reference_type": "Payment Entry",
            "reference_name": self.name
        })
        je.append("accounts", {
            "account": self.paid_to,
            "credit_in_account_currency": self.paid_amount,
            "reference_type": "Payment Entry",
            "reference_name": self.name
        })
        je.insert()
        frappe.msgprint(f"Journal Entry {je.name} created for clearing.")

    def create_debit_note(self):
        # Create a Debit Note Journal Entry for charges paid
        je = frappe.new_doc("Journal Entry")
        je.voucher_type = "Debit Note"
        je.posting_date = self.posting_date
        je.company = self.company
        je.remark = f"Debit Note for Payment Entry {self.name} (Charges Paid)"
        je.append("accounts", {
            "account": self.paid_from,
            "debit_in_account_currency": self.paid_amount,
            "reference_type": "Payment Entry",
            "reference_name": self.name
        })
        je.append("accounts", {
            "account": self.paid_to,
            "credit_in_account_currency": self.paid_amount,
            "reference_type": "Payment Entry",
            "reference_name": self.name
        })
        je.insert()
        frappe.msgprint(f"Debit Note {je.name} created for charges paid.")

    def create_credit_note(self):
        # Create a Credit Note Journal Entry for charges paid
        je = frappe.new_doc("Journal Entry")
        je.voucher_type = "Credit Note"
        je.posting_date = self.posting_date
        je.company = self.company
        je.remark = f"Credit Note for Payment Entry {self.name} (Charges Paid)"
        je.append("accounts", {
            "account": self.paid_to,
            "debit_in_account_currency": self.paid_amount,
            "reference_type": "Payment Entry",
            "reference_name": self.name
        })
        je.append("accounts", {
            "account": self.paid_from,
            "credit_in_account_currency": self.paid_amount,
            "reference_type": "Payment Entry",
            "reference_name": self.name
        })
        je.insert()
        frappe.msgprint(f"Credit Note {je.name} created for charges paid.")
    # ...existing code...